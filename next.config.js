const { BundleAnalyzerPlugin } = require("webpack-bundle-analyzer");
const withBundleAnalyzer = require("@next/bundle-analyzer")({
  enabled: process.env.ANALYZE === "true",
});

/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  // Performance optimizations
  compress: true,
  poweredByHeader: false,

  images: {
    unoptimized: false,
    formats: ["image/avif", "image/webp"],
    minimumCacheTTL: 86400, // 24 hour cache for better performance
    dangerouslyAllowSVG: false,
    remotePatterns: [
      {
        protocol: "https",
        hostname: "images.unsplash.com",
      },
    ],
    // Optimized device sizes for mobile performance
    deviceSizes: [320, 420, 640, 768, 1024, 1200, 1920],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    // Enable image optimization
    loader: "default",
  },
  experimental: {
    scrollRestoration: true,
    optimizePackageImports: [
      "lucide-react",
      "@heroicons/react",
      "@tabler/icons-react",
      "framer-motion",
      "@radix-ui/react-dialog",
      "@radix-ui/react-dropdown-menu",
      "@radix-ui/react-select",
      "@radix-ui/react-slider",
      "@radix-ui/react-tabs",
      "react-intersection-observer",
      "react-countup",
      "react-fast-marquee",
      "react-icons",
      "react-markdown",
      "react-syntax-highlighter",
    ],
    optimizeCss: true,
    // Enable modern bundling
    turbo: {
      rules: {
        "*.svg": {
          loaders: ["@svgr/webpack"],
          as: "*.js",
        },
      },
    },
  },
  compiler: {
    removeConsole:
      process.env.NODE_ENV === "production"
        ? {
            exclude: ["error"],
          }
        : false,
  },
  webpack: (config, { dev, isServer }) => {
    // Bundle analyzer
    if (process.env.ANALYZE === "true" && !isServer) {
      config.plugins.push(
        new BundleAnalyzerPlugin({
          analyzerMode: "static",
          reportFilename: "./analyze/client.html",
          openAnalyzer: false,
        })
      );
    }

    // More conservative module resolution
    config.resolve = {
      ...config.resolve,
      fallback: {
        fs: false,
        path: false,
        os: false,
      },
    };

    // Optimized chunk splitting for mobile performance
    if (!dev && !isServer) {
      config.optimization = {
        ...config.optimization,
        splitChunks: {
          chunks: "all",
          minSize: 10000,
          maxSize: 150000, // Smaller chunks for mobile
          minChunks: 1,
          maxAsyncRequests: 20, // Reduced for mobile
          maxInitialRequests: 15, // Reduced for mobile
          cacheGroups: {
            default: false,
            vendors: false,
            // React framework chunk
            framework: {
              chunks: "all",
              name: "framework",
              test: /[\\/]node_modules[\\/](react|react-dom|scheduler|prop-types|use-subscription)[\\/]/,
              priority: 50,
              enforce: true,
            },
            // Framer Motion chunk (heavy animation library)
            framerMotion: {
              chunks: "all",
              name: "framer-motion",
              test: /[\\/]node_modules[\\/]framer-motion[\\/]/,
              priority: 45,
              enforce: true,
            },
            // UI libraries chunk
            uiLibs: {
              chunks: "all",
              name: "ui-libs",
              test: /[\\/]node_modules[\\/](@radix-ui|@heroicons|@tabler|lucide-react)[\\/]/,
              priority: 40,
              enforce: true,
            },
            // Common vendor chunk
            vendor: {
              chunks: "all",
              name: "vendor",
              test: /[\\/]node_modules[\\/]/,
              priority: 30,
              minChunks: 2,
              reuseExistingChunk: true,
            },
            // Common components chunk
            common: {
              chunks: "all",
              name: "common",
              minChunks: 2,
              priority: 20,
              reuseExistingChunk: true,
            },
          },
        },
        runtimeChunk: { name: "runtime" },
        // Enable module concatenation for smaller bundles
        concatenateModules: true,
        // Enable tree shaking
        usedExports: true,
        sideEffects: false,
      };
    }

    // SVG optimization
    config.module.rules.push({
      test: /\.svg$/,
      use: ["@svgr/webpack"],
    });

    // Ensure proper module factory handling
    config.module.unknownContextCritical = false;

    return config;
  },
  // Headers for security and performance
  async headers() {
    return [
      {
        source: "/(.*)",
        headers: [
          // Security headers
          {
            key: "X-Content-Type-Options",
            value: "nosniff",
          },
          {
            key: "X-Frame-Options",
            value: "DENY",
          },
          {
            key: "X-XSS-Protection",
            value: "1; mode=block",
          },
          {
            key: "Referrer-Policy",
            value: "origin-when-cross-origin",
          },
          // Performance headers
          {
            key: "Cache-Control",
            value: "public, max-age=31536000, immutable",
          },
        ],
      },
      // Static assets caching
      {
        source: "/images/(.*)",
        headers: [
          {
            key: "Cache-Control",
            value: "public, max-age=31536000, immutable",
          },
        ],
      },
      {
        source: "/_next/static/(.*)",
        headers: [
          {
            key: "Cache-Control",
            value: "public, max-age=31536000, immutable",
          },
        ],
      },
      // Font caching
      {
        source: "/fonts/(.*)",
        headers: [
          {
            key: "Cache-Control",
            value: "public, max-age=31536000, immutable",
          },
        ],
      },
    ];
  },
};

module.exports = withBundleAnalyzer(nextConfig);
