const { BundleAnalyzerPlugin } = require("webpack-bundle-analyzer");
const withBundleAnalyzer = require("@next/bundle-analyzer")({
  enabled: process.env.ANALYZE === "true",
});

/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  // Performance optimizations
  compress: true,
  poweredByHeader: false,

  images: {
    unoptimized: false,
    formats: ["image/avif", "image/webp"],
    minimumCacheTTL: 86400, // 24 hour cache for better performance
    dangerouslyAllowSVG: false,
    remotePatterns: [
      {
        protocol: "https",
        hostname: "images.unsplash.com",
      },
    ],
    // Optimized device sizes for mobile performance
    deviceSizes: [320, 420, 640, 768, 1024, 1200, 1920],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    // Enable image optimization
    loader: "default",
  },
  experimental: {
    scrollRestoration: true,
    optimizePackageImports: [
      "lucide-react",
      "@heroicons/react",
      "@tabler/icons-react",
      "framer-motion",
      "@radix-ui/react-dialog",
      "@radix-ui/react-dropdown-menu",
      "@radix-ui/react-select",
      "@radix-ui/react-slider",
      "@radix-ui/react-tabs",
      "react-intersection-observer",
      "react-countup",
      "react-fast-marquee",
      "react-icons",
      "react-markdown",
      "react-syntax-highlighter",
    ],
    optimizeCss: true,
  },
  compiler: {
    removeConsole:
      process.env.NODE_ENV === "production"
        ? {
            exclude: ["error"],
          }
        : false,
  },
  webpack: (config, { dev, isServer, webpack }) => {
    // Bundle analyzer
    if (process.env.ANALYZE === "true" && !isServer) {
      config.plugins.push(
        new BundleAnalyzerPlugin({
          analyzerMode: "static",
          reportFilename: "./analyze/client.html",
          openAnalyzer: false,
        })
      );
    }

    // Ensure stable module IDs for better caching
    if (!dev) {
      config.plugins.push(
        new webpack.ids.HashedModuleIdsPlugin({
          hashFunction: "sha256",
          hashDigest: "hex",
          hashDigestLength: 20,
        })
      );
    }

    // Safe module resolution for Next.js 15
    if (config.resolve.fallback) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
        os: false,
      };
    }

    // Mobile-optimized chunk splitting (production only)
    if (!dev && !isServer) {
      // Preserve Next.js default optimization but add mobile-specific improvements
      const originalSplitChunks = config.optimization?.splitChunks;

      config.optimization = {
        ...config.optimization,
        splitChunks: {
          ...originalSplitChunks,
          chunks: "all",
          minSize: 20000,
          maxSize: 200000, // Smaller chunks for mobile
          cacheGroups: {
            ...originalSplitChunks?.cacheGroups,
            // Framework chunk (React, Next.js core)
            framework: {
              chunks: "all",
              name: "framework",
              test: /(?<!node_modules.*)[\\/]node_modules[\\/](react|react-dom|next)[\\/]/,
              priority: 40,
              enforce: true,
            },
            // Vendor libraries
            vendor: {
              test: /[\\/]node_modules[\\/]/,
              name: "vendor",
              chunks: "all",
              priority: 30,
              minChunks: 1,
              reuseExistingChunk: true,
            },
            // Common components
            common: {
              name: "common",
              minChunks: 2,
              chunks: "all",
              priority: 20,
              reuseExistingChunk: true,
            },
          },
        },
      };
    }

    // SVG optimization with error handling
    const svgRule = {
      test: /\.svg$/,
      use: ["@svgr/webpack"],
    };

    // Only add SVG rule if it doesn't already exist
    const existingSvgRule = config.module.rules.find(
      (rule) => rule.test && rule.test.toString().includes("svg")
    );

    if (!existingSvgRule) {
      config.module.rules.push(svgRule);
    }

    // Safe webpack configuration
    config.module.unknownContextCritical = false;
    config.module.exprContextCritical = false;

    return config;
  },
  // Headers for security and performance
  async headers() {
    return [
      {
        source: "/(.*)",
        headers: [
          // Security headers
          {
            key: "X-Content-Type-Options",
            value: "nosniff",
          },
          {
            key: "X-Frame-Options",
            value: "DENY",
          },
          {
            key: "X-XSS-Protection",
            value: "1; mode=block",
          },
          {
            key: "Referrer-Policy",
            value: "origin-when-cross-origin",
          },
          // Performance headers
          {
            key: "Cache-Control",
            value: "public, max-age=31536000, immutable",
          },
        ],
      },
      // Static assets caching
      {
        source: "/images/(.*)",
        headers: [
          {
            key: "Cache-Control",
            value: "public, max-age=31536000, immutable",
          },
        ],
      },
      {
        source: "/_next/static/(.*)",
        headers: [
          {
            key: "Cache-Control",
            value: "public, max-age=31536000, immutable",
          },
        ],
      },
      // Font caching
      {
        source: "/fonts/(.*)",
        headers: [
          {
            key: "Cache-Control",
            value: "public, max-age=31536000, immutable",
          },
        ],
      },
    ];
  },
};

module.exports = withBundleAnalyzer(nextConfig);
