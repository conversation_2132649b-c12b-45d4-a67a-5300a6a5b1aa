const CACHE_NAME = "innovatio-v1.0.0";
const STATIC_CACHE = "static-v1.0.0";
const DYNAMIC_CACHE = "dynamic-v1.0.0";
const IMAGE_CACHE = "images-v1.0.0";

// Resources to cache immediately
const PRECACHE_URLS = [
  "/",
  "/de",
  "/manifest.json",
  "/images/logo.svg",
  "/fonts/inter-var.woff2",
];

// Cache strategies for different resource types
const CACHE_STRATEGIES = {
  // Cache first for static assets
  static: [
    /\/_next\/static\//,
    /\.(?:css|js|woff|woff2|eot|ttf|otf)$/,
    /\/images\/.*\.(?:png|jpg|jpeg|svg|webp|avif)$/,
  ],

  // Network first for API calls
  networkFirst: [/\/api\//, /\/cms-demo\//],

  // Stale while revalidate for pages
  staleWhileRevalidate: [/\/de\//, /\/en\//, /\/ru\//, /\/tr\//, /\/ar\//],
};

// Install event - cache critical resources
self.addEventListener("install", (event) => {
  console.log("SW: Install event");

  event.waitUntil(
    Promise.all([
      caches.open(STATIC_CACHE).then((cache) => {
        console.log("SW: Precaching static assets");
        return cache.addAll(PRECACHE_URLS);
      }),
      self.skipWaiting(), // Activate immediately
    ])
  );
});

// Activate event - clean up old caches
self.addEventListener("activate", (event) => {
  console.log("SW: Activate event");

  event.waitUntil(
    Promise.all([
      // Clean up old caches
      caches.keys().then((cacheNames) => {
        return Promise.all(
          cacheNames
            .filter((cacheName) => {
              return (
                cacheName !== STATIC_CACHE &&
                cacheName !== DYNAMIC_CACHE &&
                cacheName !== IMAGE_CACHE
              );
            })
            .map((cacheName) => {
              console.log("SW: Deleting old cache:", cacheName);
              return caches.delete(cacheName);
            })
        );
      }),
      self.clients.claim(), // Take control of all pages
    ])
  );
});

// Fetch event - implement caching strategies
self.addEventListener("fetch", (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Skip non-GET requests
  if (request.method !== "GET") {
    return;
  }

  // Skip chrome-extension and other protocols
  if (!url.protocol.startsWith("http")) {
    return;
  }

  event.respondWith(handleRequest(request));
});

async function handleRequest(request) {
  const url = new URL(request.url);

  try {
    // Static assets - Cache First strategy
    if (isStaticAsset(url.pathname)) {
      return await cacheFirst(request, STATIC_CACHE);
    }

    // Images - Cache First with fallback
    if (isImage(url.pathname)) {
      return await cacheFirst(request, IMAGE_CACHE);
    }

    // API calls - Network First strategy
    if (isAPICall(url.pathname)) {
      return await networkFirst(request, DYNAMIC_CACHE);
    }

    // Pages - Stale While Revalidate strategy
    if (isPage(url.pathname)) {
      return await staleWhileRevalidate(request, DYNAMIC_CACHE);
    }

    // Default: try network, fallback to cache
    return await networkFirst(request, DYNAMIC_CACHE);
  } catch (error) {
    console.error("SW: Error handling request:", error);

    // Return offline fallback for pages
    if (isPage(url.pathname)) {
      const offlineResponse = await caches.match("/offline.html");
      if (offlineResponse) {
        return offlineResponse;
      }
    }

    throw error;
  }
}

// Cache First strategy
async function cacheFirst(request, cacheName) {
  const cachedResponse = await caches.match(request);

  if (cachedResponse) {
    return cachedResponse;
  }

  const response = await fetch(request);

  if (response.status === 200) {
    const cache = await caches.open(cacheName);
    cache.put(request, response.clone());
  }

  return response;
}

// Network First strategy
async function networkFirst(request, cacheName) {
  try {
    const response = await fetch(request);

    if (response.status === 200) {
      const cache = await caches.open(cacheName);
      cache.put(request, response.clone());
    }

    return response;
  } catch (error) {
    const cachedResponse = await caches.match(request);

    if (cachedResponse) {
      return cachedResponse;
    }

    throw error;
  }
}

// Stale While Revalidate strategy
async function staleWhileRevalidate(request, cacheName) {
  const cache = await caches.open(cacheName);
  const cachedResponse = await cache.match(request);

  const fetchPromise = fetch(request).then((response) => {
    if (response.status === 200) {
      cache.put(request, response.clone());
    }
    return response;
  });

  return cachedResponse || fetchPromise;
}

// Helper functions to categorize requests
function isStaticAsset(pathname) {
  return CACHE_STRATEGIES.static.some((pattern) => pattern.test(pathname));
}

function isImage(pathname) {
  return /\.(png|jpg|jpeg|gif|webp|avif|svg)$/i.test(pathname);
}

function isAPICall(pathname) {
  return CACHE_STRATEGIES.networkFirst.some((pattern) =>
    pattern.test(pathname)
  );
}

function isPage(pathname) {
  return (
    CACHE_STRATEGIES.staleWhileRevalidate.some((pattern) =>
      pattern.test(pathname)
    ) ||
    pathname === "/" ||
    !pathname.includes(".")
  );
}

// Background sync for offline actions
self.addEventListener("sync", (event) => {
  console.log("SW: Background sync event:", event.tag);

  if (event.tag === "contact-form") {
    event.waitUntil(syncContactForm());
  }
});

async function syncContactForm() {
  // Handle offline contact form submissions
  const db = await openDatabase();
  const pendingForms = await getAllPendingForms(db);

  for (const form of pendingForms) {
    try {
      const response = await fetch("/api/contact", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(form.data),
      });

      if (response.ok) {
        await deletePendingForm(db, form.id);
      }
    } catch (error) {
      console.error("SW: Failed to sync form:", error);
    }
  }
}

// IndexedDB helpers for offline functionality
function openDatabase() {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open("InnovatioOffline", 1);

    request.onerror = () => reject(request.error);
    request.onsuccess = () => resolve(request.result);

    request.onupgradeneeded = (event) => {
      const db = event.target.result;

      if (!db.objectStoreNames.contains("pendingForms")) {
        db.createObjectStore("pendingForms", {
          keyPath: "id",
          autoIncrement: true,
        });
      }
    };
  });
}

function getAllPendingForms(db) {
  return new Promise((resolve, reject) => {
    const transaction = db.transaction(["pendingForms"], "readonly");
    const store = transaction.objectStore("pendingForms");
    const request = store.getAll();

    request.onerror = () => reject(request.error);
    request.onsuccess = () => resolve(request.result);
  });
}

function deletePendingForm(db, id) {
  return new Promise((resolve, reject) => {
    const transaction = db.transaction(["pendingForms"], "readwrite");
    const store = transaction.objectStore("pendingForms");
    const request = store.delete(id);

    request.onerror = () => reject(request.error);
    request.onsuccess = () => resolve();
  });
}
