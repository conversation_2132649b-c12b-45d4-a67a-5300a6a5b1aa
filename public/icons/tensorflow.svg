<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 534.01 508.99"><defs><style>.cls-1{fill:none;}.cls-2{clip-path:url(#clip-path);}.cls-3{fill:url(#linear-gradient);}.cls-4{clip-path:url(#clip-path-2);}.cls-5{fill:url(#linear-gradient-2);}</style><clipPath id="clip-path" transform="translate(23.09 1.92)"><polygon class="cls-1" points="452.23 123.16 235.73 0 235.73 506.11 322.33 456.07 322.33 313.67 387.76 351.2 386.8 254.02 322.33 216.49 322.33 159.72 452.23 235.73 452.23 123.16"/></clipPath><linearGradient id="linear-gradient" x1="-20.21" y1="-48.36" x2="510.92" y2="-48.36" gradientTransform="matrix(1, 0, 0, -1, 0, 204.21)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#ff6f00"/><stop offset="1" stop-color="#ffa800"/></linearGradient><clipPath id="clip-path-2" transform="translate(23.09 1.92)"><polygon class="cls-1" points="0 123.16 216.49 0 216.49 506.11 129.89 456.07 129.89 159.72 0 235.73 0 123.16"/></clipPath><linearGradient id="linear-gradient-2" x1="-23.09" y1="-48.36" x2="508.03" y2="-48.36" xlink:href="#linear-gradient"/></defs><title>google-tensorflow</title><g class="cls-2"><path class="cls-3" d="M-20.21-1.92H510.92v509H-20.21Z" transform="translate(23.09 1.92)"/></g><g class="cls-4"><path class="cls-5" d="M-23.09-1.92H508v509H-23.09Z" transform="translate(23.09 1.92)"/></g></svg>