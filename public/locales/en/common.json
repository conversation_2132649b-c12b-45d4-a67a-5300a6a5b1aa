{"site": {"name": "Global Law Partners", "description": "Expert Legal Solutions for Your Business"}, "nav": {"home": "Home", "skills": "Skills", "experience": "Experience", "clients": "Clients", "apps": "Apps", "contact": "Contact", "letsTalk": "Let's Talk"}, "hero": {"hello": "Hello, I'm", "typeWriter": {"webDeveloper": "Web Developer", "mobileDeveloper": "Mobile App Developer", "flutterSpecialist": "Flutter Specialist"}, "description": "8+ Years of Excellence | Scaling Startups & Enterprises with High-Performance Flutter Apps 🚀"}, "skills": {"title": "My Skills", "subtitle": "Technical Expertise", "description": "Specialized in cross-platform development using cutting-edge technologies.", "developmentSpeed": "Development Speed", "appStability": "App Stability", "costEfficiency": "Cost Efficiency", "performance": "Performance", "fasterTimeToMarket": "Faster time to market with Flutter", "crashFreeSessions": "Crash-free sessions in production", "lowerDevelopmentCosts": "Lower development costs", "smoothUserExperience": "Smooth user experience", "skills": "skills", "proficiency": "Proficiency", "experience": "Experience", "projects": "Projects", "lastUsed": "Last used", "categories": {"frontend": "Frontend", "backend": "Backend", "ai": "AI Tools", "management": "Management", "frontendDesc": "Creating beautiful and responsive interfaces with modern frameworks for web and mobile applications.", "backendDesc": "Building robust backend systems with cloud infrastructure to power feature-rich applications.", "aiDesc": "Leveraging AI technologies to enhance development workflows and create intelligent app features.", "managementDesc": "Managing the full product lifecycle from idea to launch with focus on user experience and growth."}}, "services": {"title": "What I Offer", "subtitle": "My Services", "description": "Expert solutions tailored to your mobile app development needs.", "flutterDevelopment": "Flutter App Development", "flutterDescription": "Building high-performance cross-platform applications with Flutter", "firebaseIntegration": "Firebase Integration", "firebaseDescription": "Scalable backend solutions with Firebase and Cloud Services", "aiFeatures": "AI/ML Features", "aiDescription": "Integrating cutting-edge AI and machine learning capabilities", "mvpDevelopment": "MVP Development", "mvpDescription": "Rapid prototyping and MVP development for startups", "readMore": "Read more", "readLess": "Read less", "codeQualityReview": "Code Quality & Review", "codeQualityReviewDescription": "Ensure maintainable, efficient, and well-documented code", "devOpsCI": "DevOps & CI/CD", "devOpsCIDescription": "Automate deployment, testing, and monitoring", "appPerformanceOptimization": "App Performance Optimization", "appPerformanceOptimizationDescription": "Speed up your app and enhance user experience", "appStoreSubmissionASO": "App Store Submission & ASO", "appStoreSubmissionASODescription": "Navigate app stores requirements and maximize downloads"}, "apps": {"title": "Featured Apps", "subtitle": "Mobile Applications", "description": "Explore my portfolio of innovative Flutter applications, from enterprise solutions to consumer apps.", "toggTitle": "Togg Digital Services", "toggDescription": "Developed core features for Turkeys first smart electric vehicle manufacturer. Implemented innovative digital services and connected car features for a seamless user experience.", "lumeusTitle": "<PERSON><PERSON><PERSON>", "lumeusDescription": "A revolutionary AI-powered learning platform that adapts to individual learning styles. Features include personalized study paths, progress tracking, and interactive content delivery.", "spotzTitle": "<PERSON><PERSON>", "spotzDescription": "A smart parking solution app that helps users find, reserve, and pay for parking spots in real-time using location-based services and machine learning for optimal spot recommendations.", "hostiqTitle": "HostiQ", "hostiqDescription": "A comprehensive property management app for hosts that simplifies guest communication, booking management, and analytics tracking for vacation rentals and properties.", "visitWebsite": "Visit Website", "googlePlay": "Google Play", "appStore": "App Store"}, "clients": {"title": "Our Clients", "subtitle": "Who We've Worked With", "description": "Trusted by industry leaders and innovative startups alike.", "lufthansa": "Collaborated on digital transformation initiatives, providing mobile app development expertise for internal tools and customer-facing applications.", "togg": "Developed core features for Turkey's first smart electric vehicle manufacturer, implementing innovative connected car and mobile solutions.", "union": "Created web and mobile solutions for property management and virtual reality tours in the real estate sector.", "adesso": "Partnered with this IT service provider to deliver high-quality applications for their enterprise clients.", "hegla": "Implemented specialized industrial applications for this manufacturing company, optimizing workflows and processes.", "lumeus": "Built the core mobile application for this AI-powered educational platform, enabling personalized learning experiences.", "visitWebsite": "Visit Website", "testimonials": "TESTIMONIALS", "readMore": "Read more", "readLess": "Read less"}, "testimonials": {"emin": "<PERSON> has been instrumental in our tech growth. His expertise in Flutter development helped us launch our apps faster than anticipated, with exceptional quality and user experience.", "stefanie": "Working with <PERSON> was a game-changer for our real estate digital services. His technical knowledge and problem-solving skills ensured our project exceeded expectations.", "mohammed": "As a UX designer at Togg, collaborating with <PERSON> was seamless. He brings technical solutions to complex UI challenges without compromising on design quality or performance.", "natalia": "<PERSON> developed our property management platform that simplified our entire operation. His attention to detail and understanding of business needs resulted in an intuitive, powerful tool."}, "contact": {"title": "Let's Work Together", "subtitle": "Contact Me", "description": "Have a project in mind? Let's discuss how I can help bring your ideas to life.", "getInTouch": "Get in Touch", "email": "Email", "phone": "Phone", "website": "Website", "location": "Location", "sendMessage": "Send a Message", "name": "Name", "subject": "Subject", "message": "Message", "sending": "Sending...", "messageSent": "Message Sent!", "errorTryAgain": "Error - Try Again", "yourName": "Your name", "yourEmail": "<EMAIL>", "howCanIHelp": "How can I help you?", "yourMessageHere": "Your message here...", "submitButton": "Send Message", "schedule": "Schedule a Call", "freeConsultation": "Book a free 20-min consultation", "orSchedule": "Or schedule a meeting directly using the calendar link"}, "footer": {"description": "Flutter Mobile App Specialist crafting cutting-edge, AI-powered mobile solutions that push boundaries.", "quickLinks": "Quick Links", "rights": "All rights reserved.", "builtWith": "Built with", "and": "and"}}