{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Next.js: Chrome",
            "type": "chrome",
            "request": "launch",
            "url": "http://localhost:3000",
            "webRoot": "${workspaceFolder}",
            "sourceMapPathOverrides": {
                "webpack://_N_E/*": "${webRoot}/*",
                "webpack:///*": "${webRoot}/*",
                "webpack:///./~/*": "${webRoot}/node_modules/*",
                "webpack:///./*": "${webRoot}/*"
            },
            "skipFiles": [
                "chrome-extension://**",
                "chrome-error://**",
                "<node_internals>/**"
            ],
            "disableNetworkCache": true,
            "trace": false,
            "smartStep": true,
            "sourceMaps": true,
            "outFiles": ["${workspaceFolder}/.next/static/chunks/**/*.js"],
            "resolveSourceMapLocations": [
                "${workspaceFolder}/**",
                "!**/node_modules/**"
            ],
            "perScriptSourcemaps": "auto"
        },
        {
            "name": "Next.js: Node",
            "type": "node",
            "request": "launch",
            "runtimeExecutable": "npm",
            "runtimeArgs": ["run", "dev"],
            "sourceMaps": true,
            "resolveSourceMapLocations": [
                "${workspaceFolder}/**",
                "!**/node_modules/**"
            ],
            "skipFiles": [
                "<node_internals>/**",
                "**/node_modules/**"
            ],
            "console": "integratedTerminal"
        }
    ],
    "compounds": [
        {
            "name": "Next.js: Full Stack",
            "configurations": ["Next.js: Node", "Next.js: Chrome"]
        }
    ]
}