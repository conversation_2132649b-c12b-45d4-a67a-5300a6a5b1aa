{"name": "website_nextjs_react", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build && next-sitemap", "build:analyze": "ANALYZE=true next build", "analyze": "ANALYZE=true next build && npx serve analyze", "start": "next start", "lint": "next lint", "postbuild": "next-sitemap", "bundle-size": "npx bundlesize", "perf:lighthouse": "npx lighthouse http://localhost:3000 --output=json --output-path=./lighthouse-report.json", "perf:analyze": "node performance-monitor.js analyze", "perf:full": "node performance-monitor.js full", "perf:monitor": "node performance-monitor.js", "optimize:images": "node scripts/optimize-images.js", "optimize:all": "npm run optimize:images && npm run build:analyze"}, "dependencies": {"@floating-ui/react": "0.24.8", "@floating-ui/react-dom": "1.3.0", "@formatjs/intl-localematcher": "^0.6.1", "@headlessui/react": "1.7.17", "@heroicons/react": "^2.2.0", "@notionhq/client": "^3.1.3", "@number-flow/react": "^0.5.9", "@radix-ui/react-dialog": "1.0.0", "@radix-ui/react-dropdown-menu": "2.0.0", "@radix-ui/react-label": "2.0.0", "@radix-ui/react-select": "1.2.0", "@radix-ui/react-slider": "1.1.0", "@radix-ui/react-slot": "1.0.0", "@radix-ui/react-switch": "1.0.0", "@radix-ui/react-tabs": "1.0.0", "@radix-ui/react-toggle": "1.0.0", "@svgr/webpack": "^8.1.0", "@tabler/icons-react": "^3.31.0", "@tsparticles/engine": "^3.8.1", "@tsparticles/react": "^3.0.0", "@tsparticles/slim": "^3.8.1", "@types/nodemailer": "^6.4.17", "@types/react-signature-canvas": "^1.0.7", "@types/react-syntax-highlighter": "^15.5.13", "@types/uuid": "^10.0.0", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "critters": "^0.0.23", "embla-carousel": "^8.6.0", "embla-carousel-auto-scroll": "^8.6.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.16.0", "lottie-react": "^2.4.1", "lucide-react": "^0.484.0", "negotiator": "^1.0.0", "next": "^15.3.3", "next-intl": "^4.0.2", "next-sitemap": "^4.2.3", "next-themes": "^0.4.6", "nodemailer": "^6.10.0", "react": "^18.3.1", "react-countup": "^6.5.3", "react-dom": "^18.3.1", "react-fast-marquee": "^1.6.5", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-markdown": "^10.1.0", "react-signature-canvas": "^1.1.0-alpha.2", "react-syntax-highlighter": "^15.6.1", "react-type-animation": "^3.2.0", "resend": "^4.5.1", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.4", "uuid": "^11.1.0", "web-vitals": "^4.2.4"}, "devDependencies": {"@babel/plugin-transform-class-properties": "^7.27.1", "@babel/plugin-transform-private-methods": "^7.27.1", "@babel/plugin-transform-private-property-in-object": "^7.27.1", "@eslint/eslintrc": "^3", "@next/bundle-analyzer": "^14.2.5", "@tailwindcss/line-clamp": "^0.4.4", "@tailwindcss/postcss": "^4", "@types/negotiator": "^0.6.3", "@types/node": "^20", "@types/react": "18.2.55", "@types/react-dom": "18.2.19", "babel-plugin-import": "^1.13.8", "babel-plugin-transform-react-remove-prop-types": "^0.4.24", "babel-plugin-transform-remove-console": "^6.9.4", "babel-plugin-transform-remove-debugger": "^6.9.4", "bundlesize": "^0.18.1", "eslint": "^9", "eslint-config-next": "15.2.1", "imagemin": "^9.0.1", "imagemin-avif": "^0.1.6", "imagemin-webp": "^8.0.0", "lighthouse": "^11.7.1", "react-refresh": "^0.14.2", "serve": "^14.2.3", "sharp": "^0.34.2", "tailwindcss": "^4", "typescript": "^5", "webp-converter": "^2.3.3", "webpack-bundle-analyzer": "^4.10.2"}, "browserslist": {"production": ["> 0.5%", "last 2 versions", "not dead", "not ie 11", "not chrome < 91", "not firefox < 90", "not safari < 14", "not edge < 91"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version", "last 1 edge version"]}, "resolutions": {"@floating-ui/react-dom": "1.3.0", "@floating-ui/react": "0.24.8"}}