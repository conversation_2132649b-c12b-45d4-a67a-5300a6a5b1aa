/** @type {import('next-sitemap').IConfig} */
module.exports = {
  siteUrl: "https://innovatio-pro.com",
  generateRobotsTxt: true,
  changefreq: "weekly",
  priority: 0.7,
  sitemapSize: 5000,
  generateIndexSitemap: true,

  // Exclude paths that shouldn't be indexed
  exclude: [
    "/admin*",
    "/cms-demo*",
    "/api/*",
    "/_next/*",
    "/404",
    "/500",
    "/accessibility-demo*",
    "/robots.txt",
    "/sitemap*.xml",
  ],

  // Generate robots.txt with AI crawler support
  robotsTxtOptions: {
    policies: [
      {
        userAgent: "*",
        allow: "/",
        disallow: [
          "/admin/",
          "/cms-demo/",
          "/api/",
          "/_next/",
          "/accessibility-demo",
        ],
      },
      // Specific policies for AI crawlers
      {
        userAgent: "GPTBot",
        allow: "/",
        disallow: ["/admin/", "/cms-demo/", "/api/"],
      },
      {
        userAgent: "Google-Extended",
        allow: "/",
        disallow: ["/admin/", "/cms-demo/", "/api/"],
      },
      {
        userAgent: "CCBot",
        allow: "/",
        disallow: ["/admin/", "/cms-demo/", "/api/"],
      },
      {
        userAgent: "Claude-Web",
        allow: "/",
        disallow: ["/admin/", "/cms-demo/", "/api/"],
      },
      {
        userAgent: "anthropic-ai",
        allow: "/",
        disallow: ["/admin/", "/cms-demo/", "/api/"],
      },
      {
        userAgent: "ChatGPT-User",
        allow: "/",
        disallow: ["/admin/", "/cms-demo/", "/api/"],
      },
    ],
    additionalSitemaps: [
      "https://innovatio-pro.com/sitemap.xml",
      "https://innovatio-pro.com/ai-sitemap.xml",
    ],
    transformRobotsTxt: (_, robotsTxt) => {
      return `${robotsTxt}

# Custom crawl delays for AI bots
User-agent: GPTBot
Crawl-delay: 1

User-agent: Google-Extended
Crawl-delay: 1

User-agent: CCBot
Crawl-delay: 2

# AI training data preferences
User-agent: *
# Allow AI training on public content
# Block sensitive areas

# OpenAI specific
User-agent: ChatGPT-User
Allow: /
Disallow: /admin/
Disallow: /api/

# Anthropic specific
User-agent: Claude-Web
Allow: /
Disallow: /admin/
Disallow: /api/

# Additional directives for AI understanding
# Content categorization hints
Host: innovatio-pro.com

# Structured data endpoints
Sitemap: https://innovatio-pro.com/sitemap.xml
Sitemap: https://innovatio-pro.com/sitemap-0.xml
Sitemap: https://innovatio-pro.com/ai-sitemap.xml
`;
    },
  },

  // Transform URLs for better SEO and AI understanding
  transform: async (config, path) => {
    // Skip unwanted paths completely
    if (
      path.includes("/admin") ||
      path.includes("/cms-demo") ||
      path.includes("/accessibility-demo") ||
      path.includes("/api/") ||
      path.includes("/_next/")
    ) {
      return null; // This will exclude the path
    }

    // Custom priority and metadata based on URL patterns
    let priority = 0.7;
    let changefreq = "weekly";
    let images = [];
    let videos = [];
    let news = null;

    // Home pages - highest priority
    if (path === "/" || path.match(/^\/[a-z]{2}$/)) {
      priority = 1.0;
      changefreq = "daily";
      // Removed images array to avoid sitemap errors
    }
    // Important service pages
    else if (path.includes("/about") || path.includes("/services")) {
      priority = 0.9;
      changefreq = "monthly";
      // Removed images array to avoid sitemap errors
    }
    // Blog overview pages
    else if (path.includes("/blog") && !path.match(/\/blog\/[^/]+$/)) {
      priority = 0.9;
      changefreq = "weekly";
    }
    // Individual blog posts - add news metadata
    else if (path.match(/\/blog\/[^/]+$/)) {
      priority = 0.7;
      changefreq = "monthly";
      news = {
        publication: {
          name: "Innovatio Blog",
          language: path.startsWith("/de")
            ? "de"
            : path.startsWith("/ru")
              ? "ru"
              : path.startsWith("/tr")
                ? "tr"
                : path.startsWith("/ar")
                  ? "ar"
                  : "en",
        },
        genres: "Technology, Software Development, AI",
        publication_date: new Date().toISOString(),
        title: `${path?.split("/").pop()?.replace(/-/g, " ") || "Article"} | Innovatio Blog`,
        keywords:
          "web development, mobile apps, AI integration, digital transformation",
      };
    }
    // Contact and pricing
    else if (path.includes("/contact") || path.includes("/pricing")) {
      priority = 0.8;
      changefreq = "monthly";
    }
    // FAQ and legal pages
    else if (
      path.includes("/faq") ||
      path.includes("/privacy") ||
      path.includes("/terms")
    ) {
      priority = 0.6;
      changefreq = "yearly";
    }

    // Enhanced metadata for AI understanding
    const alternateRefs = [
      { href: `https://innovatio-pro.com${path || ""}`, hreflang: "en" },
      {
        href: `https://innovatio-pro.com/de${path === "/" ? "" : path || ""}`,
        hreflang: "de",
      },
      {
        href: `https://innovatio-pro.com/ru${path === "/" ? "" : path || ""}`,
        hreflang: "ru",
      },
      {
        href: `https://innovatio-pro.com/tr${path === "/" ? "" : path || ""}`,
        hreflang: "tr",
      },
      {
        href: `https://innovatio-pro.com/ar${path === "/" ? "" : path || ""}`,
        hreflang: "ar",
      },
    ].filter((ref) => ref.href && !ref.href.includes("undefined"));

    return {
      loc: path || "/",
      changefreq,
      priority,
      lastmod: new Date().toISOString(),
      alternateRefs: alternateRefs || [],
      images: images || [],
      videos: videos || [],
      news,
    };
  },

  // Additional paths for AI crawlers
  additionalPaths: async (config) => {
    const locales = ["en", "de", "ru", "tr", "ar"];
    const paths = [];

    // Add key landing pages for each locale
    const keyPages = [
      "/",
      "/about",
      "/services",
      "/contact",
      "/pricing",
      "/blog",
      "/faq",
    ];

    locales.forEach((locale) => {
      keyPages.forEach((page) => {
        const localePrefix = locale === "en" ? "" : `/${locale}`;
        const fullPath =
          page === "/" ? localePrefix || "/" : `${localePrefix}${page}`;

        if (fullPath && !paths.includes(fullPath)) {
          paths.push({
            loc: fullPath,
            priority: page === "/" ? 1.0 : 0.8,
            changefreq: "weekly",
            lastmod: new Date().toISOString(),
          });
        }
      });
    });

    return paths;
  },
};
