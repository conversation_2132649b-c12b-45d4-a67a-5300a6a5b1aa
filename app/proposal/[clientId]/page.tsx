"use client";

import { useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";
import {
  Calendar,
  Clock,
  Package,
  User,
  Mail,
  Building,
  Phone,
  MessageCircle,
  CheckCircle,
  X,
  Edit,
  ExternalLink,
  Lock,
} from "lucide-react";

interface ProposalPageProps {
  params: Promise<{
    clientId: string;
  }>;
}

interface ProposalData {
  clientName: string;
  clientEmail: string;
  company?: string;
  phone?: string;
  selectedService: string;
  estimatedBudget: string;
  projectTimeline: string;
  message: string;
  heardAbout?: string;
  changeRequests?: string;
  changeRequestDate?: string | null;
  status?: string;
  urlPassword?: string;
}

interface ServicePackage {
  id: string;
  name: string;
  originalPrice: number;
  discountedPrice: number;
  timeline: string;
  description: string;
  features: string[];
  discount: number;
}

// Service packages basierend auf dem Screenshot
const SERVICE_PACKAGES: Record<string, ServicePackage> = {
  mvp: {
    id: "mvp",
    name: "MVP-Entwicklung",
    originalPrice: 10000,
    discountedPrice: 8500,
    timeline: "3-4 Wochen",
    description:
      "Bringen Sie Ihre Idee schnell mit einem Minimum Viable Product auf den Markt",
    features: [
      "Implementierung der Kernfunktionalität",
      "Grundlegendes UI-Design",
      "Benutzerauthentifizierung",
      "Datenspeicherlösung",
      "Deployment auf einer Plattform",
      "Weltweit konform",
    ],
    discount: 15,
  },
  prototype: {
    id: "prototype",
    name: "Schneller Prototyp",
    originalPrice: 5000,
    discountedPrice: 4200,
    timeline: "1-2 Wochen",
    description: "Testen Sie Ihr Konzept mit einem funktionalen Prototyp",
    features: [
      "Interaktive UI-Mockups",
      "Grundlegende Funktionalität",
      "Implementierung des Benutzerflusses",
      "Präsentation für Stakeholder",
      "Flutter-gesteuerte MVP-Entwicklung",
    ],
    discount: 16,
  },
  landingpage: {
    id: "landingpage",
    name: "Landing-Page-Entwicklung",
    originalPrice: 3500,
    discountedPrice: 3000,
    timeline: "2-4 Wochen",
    description: "WCAG 2.0-konforme Landing-Pages mit neuesten Webtechnologien",
    features: [
      "WCAG 2.0 AA-Konformität",
      "Inklusives Design für alle Nutzer",
      "Screenreader-Kompatibilität",
      "Hohe Performance-Metriken",
      "SEO-optimierte Struktur",
      "Responsives Design",
    ],
    discount: 14,
  },
};

export default function ProposalPage({ params }: ProposalPageProps) {
  const searchParams = useSearchParams();
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [loading, setLoading] = useState(true);
  const [proposalData, setProposalData] = useState<ProposalData | null>(null);
  const [modifications, setModifications] = useState<string>("");
  const [showModificationForm, setShowModificationForm] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [proposalAccepted, setProposalAccepted] = useState(false);
  const [clientId, setClientId] = useState<string>("");

  // Password protection states
  const [passwordInput, setPasswordInput] = useState("");
  const [isPasswordVerified, setIsPasswordVerified] = useState(false);
  const [showPasswordPrompt, setShowPasswordPrompt] = useState(false);

  const auth = searchParams?.get("auth");

  // Resolve params async
  useEffect(() => {
    const resolveParams = async () => {
      const resolvedParams = await params;
      setClientId(resolvedParams.clientId);
    };
    resolveParams();
  }, [params]);

  useEffect(() => {
    if (auth && auth.length === 32 && clientId) {
      setIsAuthorized(true);

      // Load real data from Notion only
      const fetchProposalData = async () => {
        try {
          const response = await fetch(`/api/get-proposal-data/${clientId}`);
          if (response.ok) {
            const realData = await response.json();
            setProposalData(realData);
            console.log("Loaded real proposal data:", realData);

            // Check if password is required
            if (realData.urlPassword && realData.urlPassword.length > 0) {
              setShowPasswordPrompt(true);
            } else {
              setIsPasswordVerified(true);
            }
          } else {
            console.error("Failed to load proposal data:", response.status);
            // Don't set any fallback data - show error instead
          }
        } catch (error) {
          console.error("Error fetching proposal data:", error);
          // Don't set any fallback data - show error instead
        } finally {
          setLoading(false);
        }
      };

      fetchProposalData();
    } else if (clientId) {
      // If we have clientId but no auth or invalid auth, stop loading
      setLoading(false);
    }
  }, [auth, clientId]);

  const handlePasswordSubmit = () => {
    if (passwordInput === proposalData?.urlPassword) {
      setIsPasswordVerified(true);
      setShowPasswordPrompt(false);
    } else {
      alert("Incorrect password. Please try again.");
    }
  };

  const selectedPackage = proposalData
    ? SERVICE_PACKAGES[proposalData.selectedService]
    : null;

  const isQualified = proposalData?.status === "Qualified";

  const handleAcceptProposal = async () => {
    setIsSubmitting(true);
    try {
      const response = await fetch("/api/proposal/accept", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          clientId: clientId,
        }),
      });

      if (response.ok) {
        setProposalAccepted(true);
        setShowSuccessModal(true);
        setSuccessMessage(
          "Vielen Dank! Wir werden Ihre Anfrage überprüfen und Ihnen einen Vertrag zum Unterzeichnen zusenden."
        );
      }
    } catch (error) {
      console.error("Error accepting proposal:", error);
    }
    setIsSubmitting(false);
  };

  const handleRequestModification = async () => {
    setIsSubmitting(true);
    try {
      const response = await fetch("/api/proposal/modify", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          clientId: clientId,
          modifications: modifications,
          date: new Date().toISOString(),
        }),
      });

      if (response.ok) {
        setSuccessMessage("Änderungsanfrage erfolgreich gesendet!");
        setShowModificationForm(false);
        setModifications("");

        // Refresh proposal data to show the new change request
        setTimeout(() => {
          window.location.reload();
        }, 2000);
      } else {
        setSuccessMessage("Fehler beim Senden der Änderungsanfrage.");
      }
    } catch (error) {
      console.error("Error sending modification request:", error);
    }
    setIsSubmitting(false);
  };

  const handleScheduleMeeting = () => {
    window.open("https://calendly.com/v-hermann-it", "_blank");
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!isAuthorized) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="max-w-md w-full bg-gray-800 shadow-lg rounded-lg p-8 text-center">
          <Lock className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-white mb-4">
            Unauthorized Access
          </h1>
          <p className="text-gray-300 mb-6">
            You don't have permission to view this proposal.
          </p>
          <p className="text-sm text-gray-400">
            Please contact support if you believe this is an error.
          </p>
        </div>
      </div>
    );
  }

  // Password verification prompt
  if (showPasswordPrompt && !isPasswordVerified) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="max-w-md w-full bg-gray-800 shadow-lg rounded-lg p-8">
          <Lock className="w-12 h-12 text-blue-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-white mb-4 text-center">
            Proposal Access
          </h1>
          <p className="text-gray-300 mb-6 text-center">
            Please enter the password to access this proposal.
          </p>
          <div className="space-y-4">
            <input
              type="password"
              value={passwordInput}
              onChange={(e) => setPasswordInput(e.target.value)}
              placeholder="Enter password"
              className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-blue-500"
              onKeyPress={(e) => e.key === "Enter" && handlePasswordSubmit()}
            />
            <button
              onClick={handlePasswordSubmit}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-lg font-semibold transition-colors"
            >
              Access Proposal
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!isPasswordVerified) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!proposalData) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="max-w-md w-full bg-gray-800 shadow-lg rounded-lg p-8 text-center">
          <h1 className="text-2xl font-bold text-white mb-4">
            Proposal Not Found
          </h1>
          <p className="text-gray-300 mb-6">
            The proposal data could not be loaded. This may be because:
          </p>
          <ul className="text-gray-400 text-sm text-left mb-6 space-y-2">
            <li>• The proposal ID is invalid</li>
            <li>• The Notion page doesn't exist</li>
            <li>• There's no data for this client</li>
            <li>• Access permissions are missing</li>
          </ul>
          <p className="text-sm text-gray-500">
            Please contact support if you believe this is an error.
          </p>
        </div>
      </div>
    );
  }

  if (!selectedPackage) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="max-w-md w-full bg-gray-800 shadow-lg rounded-lg p-8 text-center">
          <h1 className="text-2xl font-bold text-white mb-4">
            Service Package Not Found
          </h1>
          <p className="text-gray-300 mb-6">
            The selected service package "{proposalData.selectedService}" is not
            available.
          </p>
          <p className="text-sm text-gray-500">
            Please contact support to resolve this issue.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="bg-gray-800 border border-blue-500 rounded-lg p-6 mb-6">
          <div className="flex items-center gap-3 mb-2">
            <div className="text-blue-500">📄</div>
            <h1 className="text-2xl font-bold text-white">
              Projektangebot - {proposalData.clientName}
            </h1>
          </div>
          <div className="flex items-center justify-between">
            <p className="text-blue-400">
              Proposal ID: {clientId} • Gültig bis:{" "}
              {new Date(
                Date.now() + 30 * 24 * 60 * 60 * 1000
              ).toLocaleDateString("de-DE")}
            </p>
            {proposalData?.status && (
              <div
                className={`px-3 py-1 rounded-full text-sm font-semibold ${
                  proposalData.status === "ChangeRequest"
                    ? "bg-orange-500/20 text-orange-400 border border-orange-500/30"
                    : proposalData.status === "qualified"
                      ? "bg-green-500/20 text-green-400 border border-green-500/30"
                      : "bg-blue-500/20 text-blue-400 border border-blue-500/30"
                }`}
              >
                Status:{" "}
                {proposalData.status === "ChangeRequest"
                  ? "Änderung angefragt"
                  : proposalData.status === "qualified"
                    ? "Akzeptiert"
                    : proposalData.status}
              </div>
            )}
          </div>
        </div>

        {/* Success Message */}
        {successMessage && (
          <div className="bg-green-800 border border-green-500 rounded-lg p-4 mb-6">
            <div className="flex items-center gap-3">
              <CheckCircle className="text-green-400 w-5 h-5" />
              <span className="text-green-100">{successMessage}</span>
            </div>
          </div>
        )}

        {/* Customer Data */}
        <div className="bg-gray-800 rounded-lg p-6 mb-6">
          <div className="flex items-center gap-3 mb-4">
            <User className="text-white w-5 h-5" />
            <h2 className="text-xl font-semibold text-white">Kundendaten</h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="flex items-center gap-3">
              <User className="text-gray-400 w-4 h-4" />
              <div>
                <span className="text-gray-400 text-sm">Name: </span>
                <span className="text-white">{proposalData.clientName}</span>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Mail className="text-gray-400 w-4 h-4" />
              <div>
                <span className="text-gray-400 text-sm">Email: </span>
                <span className="text-white">{proposalData.clientEmail}</span>
              </div>
            </div>
            {proposalData.company && (
              <div className="flex items-center gap-3">
                <Building className="text-gray-400 w-4 h-4" />
                <div>
                  <span className="text-gray-400 text-sm">Firma: </span>
                  <span className="text-white">{proposalData.company}</span>
                </div>
              </div>
            )}
            {proposalData.phone && (
              <div className="flex items-center gap-3">
                <Phone className="text-gray-400 w-4 h-4" />
                <div>
                  <span className="text-gray-400 text-sm">Telefon: </span>
                  <span className="text-white">{proposalData.phone}</span>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Selected Package - Styled like in the image */}
        <div className="bg-gray-800 rounded-lg p-6 mb-6">
          <div className="flex items-center gap-3 mb-6">
            <Package className="text-white w-5 h-5" />
            <h2 className="text-xl font-semibold text-white">
              Ausgewähltes Paket
            </h2>
          </div>

          {/* Package Card */}
          <div className="bg-gradient-to-br from-blue-600 to-purple-700 rounded-lg p-6 text-white relative overflow-hidden">
            {/* Header */}
            <div className="flex items-start justify-between mb-4">
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <Clock className="w-4 h-4" />
                  <span className="text-blue-100">
                    {selectedPackage.timeline}
                  </span>
                </div>
                <h3 className="text-2xl font-bold mb-2">
                  {selectedPackage.name}
                </h3>
              </div>
              <div className="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                💾 Save {selectedPackage.discount}%
              </div>
            </div>

            {/* Description */}
            <p className="text-blue-100 mb-6">{selectedPackage.description}</p>

            {/* Price */}
            <div className="mb-6">
              <div className="flex items-baseline gap-3">
                <span className="text-4xl font-bold">
                  €{selectedPackage.discountedPrice.toLocaleString("de-DE")}
                </span>
                <span className="text-xl text-blue-200 line-through">
                  €{selectedPackage.originalPrice.toLocaleString("de-DE")}
                </span>
              </div>
            </div>

            {/* Features */}
            <div className="space-y-3">
              {selectedPackage.features.map((feature, index) => (
                <div key={index} className="flex items-center gap-3">
                  <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
                  <span className="text-white">{feature}</span>
                </div>
              ))}
            </div>

            {/* Timeline and tags */}
            <div className="mt-6 flex flex-wrap gap-2">
              <div className="bg-blue-500/30 text-blue-100 px-3 py-1 rounded-full text-sm flex items-center gap-1">
                ⚡ Fast Delivery
              </div>
              <div className="bg-green-500/30 text-green-100 px-3 py-1 rounded-full text-sm flex items-center gap-1">
                🔒 Secure
              </div>
              <div className="bg-purple-500/30 text-purple-100 px-3 py-1 rounded-full text-sm flex items-center gap-1">
                🎨 Modern
              </div>
            </div>
          </div>
        </div>

        {/* Project Details */}
        <div className="bg-gray-800 rounded-lg p-6 mb-6">
          <h3 className="text-xl font-semibold text-white mb-4">
            Projektdetails
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="bg-gray-700 rounded-lg p-4">
              <div className="text-gray-400 text-sm mb-1">Budget</div>
              <div className="text-white font-semibold">
                {proposalData.estimatedBudget
                  .replace("_", " - ")
                  .replace("k", ".000€")}
              </div>
            </div>
            <div className="bg-gray-700 rounded-lg p-4">
              <div className="text-gray-400 text-sm mb-1">Timeline</div>
              <div className="text-white font-semibold">
                {proposalData.projectTimeline
                  .replace("_", "-")
                  .replace("months", " Monate")
                  .replace("month", " Monat")}
              </div>
            </div>
            <div className="bg-gray-700 rounded-lg p-4">
              <div className="text-gray-400 text-sm mb-1">Quelle</div>
              <div className="text-white font-semibold">
                {proposalData.heardAbout}
              </div>
            </div>
          </div>

          <div className="bg-gray-700 rounded-lg p-4">
            <div className="text-gray-400 text-sm mb-2">
              Projektbeschreibung
            </div>
            <div className="text-white">{proposalData.message}</div>
          </div>
        </div>

        {/* Investment Breakdown */}
        <div className="bg-gray-800 rounded-lg p-6 mb-6">
          <h3 className="text-xl font-semibold text-white mb-4">
            Investitionsaufschlüsselung
          </h3>
          <div className="space-y-3">
            <div className="flex justify-between items-center py-3 border-b border-gray-700">
              <span className="text-gray-300">
                Grundpaket ({selectedPackage.name})
              </span>
              <span className="text-white font-semibold">
                €{selectedPackage.originalPrice.toLocaleString("de-DE")}
              </span>
            </div>
            <div className="flex justify-between items-center py-3 border-b border-gray-700">
              <span className="text-gray-300">
                Rabatt ({selectedPackage.discount}%)
              </span>
              <span className="text-green-400 font-semibold">
                -€
                {(
                  selectedPackage.originalPrice -
                  selectedPackage.discountedPrice
                ).toLocaleString("de-DE")}
              </span>
            </div>
            <div className="flex justify-between items-center py-3 border-t-2 border-gray-600 text-lg font-bold">
              <span className="text-white">Gesamtinvestition</span>
              <span className="text-blue-400">
                €{selectedPackage.discountedPrice.toLocaleString("de-DE")}
              </span>
            </div>
          </div>
        </div>

        {/* Proposal Acceptance Note */}
        <div className="bg-gray-800 rounded-lg p-6 mb-6">
          <h3 className="text-xl font-semibold text-white mb-4">
            Angebot akzeptieren
          </h3>
          <p className="text-gray-400 mb-4">
            Durch das Akzeptieren dieses Angebots stimmen Sie den Projektdetails
            und dem Preis zu. Nach der Annahme erhalten Sie eine E-Mail mit dem
            finalen Vertrag zur digitalen Unterzeichnung.
          </p>
          <div className="bg-blue-900/30 border border-blue-500/30 rounded-lg p-4">
            <div className="flex items-center gap-3 text-blue-300">
              <div className="text-2xl">📋</div>
              <div>
                <h4 className="font-semibold">Nächste Schritte:</h4>
                <ul className="text-sm mt-2 space-y-1">
                  <li>1. Angebot akzeptieren</li>
                  <li>2. Sie erhalten eine E-Mail mit dem Vertrag</li>
                  <li>3. Digitale Unterschrift im Vertrag</li>
                  <li>4. Projektstart</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Status Message for Qualified Proposals */}
        {isQualified && (
          <div className="bg-green-900/30 border border-green-500/30 rounded-lg p-6 mb-6">
            <div className="flex items-center gap-3 text-green-300">
              <div className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
                <CheckCircle className="w-5 h-5 text-white" />
              </div>
              <div>
                <h4 className="font-semibold text-lg">
                  Angebot bereits angenommen!
                </h4>
                <p className="text-sm mt-1 text-green-400">
                  ✅ Status: Qualified | 📧 Vertragsunterlagen werden
                  vorbereitet | 📞 Wir melden uns in Kürze
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Action Buttons - Only show if not qualified */}
        {!isQualified && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <button
              onClick={handleAcceptProposal}
              disabled={isSubmitting}
              className={`py-3 px-6 rounded-lg font-semibold transition-colors flex items-center justify-center gap-2 ${
                !isSubmitting
                  ? "bg-green-600 hover:bg-green-700 text-white"
                  : "bg-gray-600 text-gray-400 cursor-not-allowed"
              }`}
            >
              <CheckCircle className="w-5 h-5" />
              {isSubmitting ? "Wird verarbeitet..." : "Angebot akzeptieren"}
            </button>

            <button
              onClick={() => setShowModificationForm(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white py-3 px-6 rounded-lg font-semibold transition-colors flex items-center justify-center gap-2"
            >
              <Edit className="w-5 h-5" />
              Änderungen anfordern
            </button>

            <button
              onClick={handleScheduleMeeting}
              className="bg-purple-600 hover:bg-purple-700 text-white py-3 px-6 rounded-lg font-semibold transition-colors flex items-center justify-center gap-2"
            >
              <Calendar className="w-5 h-5" />
              Meeting planen
            </button>
          </div>
        )}

        {/* Existing Change Requests */}
        {proposalData.changeRequests && (
          <div className="bg-gray-800 rounded-lg p-6 mb-6 border border-orange-500">
            <div className="flex items-center gap-3 mb-4">
              <Edit className="text-orange-400 w-5 h-5" />
              <h3 className="text-xl font-semibold text-white">
                Bisherige Änderungsanfragen
              </h3>
              {proposalData.changeRequestDate && (
                <span className="text-orange-400 text-sm">
                  Letzte Änderung:{" "}
                  {new Date(proposalData.changeRequestDate).toLocaleDateString(
                    "de-DE"
                  )}
                </span>
              )}
            </div>
            <div className="bg-gray-700 rounded-lg p-4 max-h-96 overflow-y-auto">
              <div className="text-gray-300 whitespace-pre-wrap font-mono text-sm">
                {proposalData.changeRequests
                  .split("--- NEUE ÄNDERUNG ---")
                  .map((request, index) => (
                    <div
                      key={index}
                      className={
                        index > 0 ? "mt-6 pt-6 border-t border-gray-600" : ""
                      }
                    >
                      {request.trim() && (
                        <div className="bg-gray-600 rounded p-3">
                          {request.trim()}
                        </div>
                      )}
                    </div>
                  ))}
              </div>
            </div>
          </div>
        )}

        {/* Modification Form - Only show if not qualified */}
        {!isQualified && showModificationForm && (
          <div className="bg-gray-800 rounded-lg p-6 mb-6 border border-yellow-500">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-semibold text-white">
                Neue Änderungsanfrage
              </h3>
              <button
                onClick={() => setShowModificationForm(false)}
                className="text-gray-400 hover:text-white"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            <textarea
              value={modifications}
              onChange={(e) => setModifications(e.target.value)}
              placeholder="Beschreiben Sie die gewünschten Änderungen..."
              className="w-full h-32 p-4 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-blue-500 focus:outline-none resize-none"
            />
            <div className="flex gap-4 mt-4">
              <button
                onClick={handleRequestModification}
                disabled={!modifications.trim() || isSubmitting}
                className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white px-6 py-2 rounded transition-colors"
              >
                {isSubmitting ? "Wird gesendet..." : "Änderung senden"}
              </button>
              <button
                onClick={() => setShowModificationForm(false)}
                className="bg-gray-600 hover:bg-gray-500 text-white px-6 py-2 rounded transition-colors"
              >
                Abbrechen
              </button>
            </div>
          </div>
        )}

        {/* Contact Information */}
        <div className="bg-gray-800 rounded-lg p-6">
          <h3 className="text-xl font-semibold text-white mb-4">
            Fragen? Kontaktieren Sie uns!
          </h3>
          <p className="text-gray-400 mb-4">
            Wir sind hier, um alle Ihre Fragen zu diesem Angebot zu beantworten.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center gap-3">
              <Mail className="w-5 h-5 text-blue-400" />
              <span className="text-white"><EMAIL></span>
            </div>
            <div className="flex items-center gap-3">
              <Phone className="w-5 h-5 text-green-400" />
              <span className="text-white">+49 175 9918357</span>
            </div>
            <div className="flex items-center gap-3">
              <MessageCircle className="w-5 h-5 text-purple-400" />
              <span className="text-white">WhatsApp Support</span>
            </div>
          </div>
        </div>
      </div>

      {/* Success Modal */}
      {showSuccessModal && (
        <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800 rounded-xl p-8 max-w-md w-full border border-gray-600">
            <div className="text-center">
              <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-white mb-4">
                {proposalAccepted ? "Angebot angenommen!" : "Erfolgreich!"}
              </h3>
              <p className="text-gray-300 mb-6">{successMessage}</p>

              {proposalAccepted && (
                <div className="text-sm text-gray-400 mb-6 space-y-2">
                  <p>✅ Status wurde auf "Qualified" aktualisiert</p>
                  <p>📧 Vertragsunterlagen werden vorbereitet</p>
                  <p>📞 Unser Team meldet sich in Kürze bei Ihnen</p>
                </div>
              )}

              <div className="flex justify-center">
                <button
                  onClick={() => {
                    try {
                      window.location.replace("/");
                    } catch (error) {
                      window.location.href = "/";
                    }
                  }}
                  className="w-full max-w-sm bg-blue-600 hover:bg-blue-700 text-white py-3 px-6 rounded-lg transition-colors font-semibold"
                >
                  Zur Homepage
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Success Message for Modifications */}
      {successMessage && !showSuccessModal && (
        <div className="fixed top-4 right-4 bg-green-600 text-white px-6 py-3 rounded-lg shadow-lg z-50">
          {successMessage}
        </div>
      )}
    </div>
  );
}
