import { NextRequest, NextResponse } from 'next/server';
import { Resend } from 'resend';

const resend = new Resend(process.env.RESEND_API_KEY);

export async function POST(request: NextRequest) {
    try {
        const { email } = await request.json();

        if (!email || !email.includes('@')) {
            return NextResponse.json(
                { error: 'Invalid email address' },
                { status: 400 }
            );
        }

        // Add to newsletter list
        if (process.env.RESEND_API_KEY) {
            try {
                await resend.contacts.create({
                    email,
                    audienceId: process.env.RESEND_AUDIENCE_ID || '',
                });
            } catch (resendError) {
                console.error('Resend error:', resendError);
                // Continue with confirmation email even if list addition fails
            }
        }

        // Send welcome email
        if (process.env.RESEND_API_KEY) {
            try {
                await resend.emails.send({
                    from: '<EMAIL>',
                    to: email,
                    subject: 'Willkommen bei Innovatio Tech Insights!',
                    html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
              <div style="text-align: center; margin-bottom: 30px;">
                <h1 style="color: #152660; margin-bottom: 10px;">Willkommen bei Innovatio!</h1>
                <p style="color: #666; font-size: 18px;">Vielen Dank für Ihr Interesse an unseren Tech Insights</p>
              </div>
              
              <div style="background: linear-gradient(135deg, #152660, #1e3a8a); color: white; padding: 30px; border-radius: 12px; text-align: center; margin-bottom: 30px;">
                <h2 style="margin: 0 0 15px 0;">🚀 Sie sind jetzt dabei!</h2>
                <p style="margin: 0; font-size: 16px;">Erhalten Sie die neuesten Insights zu Flutter, AI und mobiler Entwicklung direkt in Ihr Postfach.</p>
              </div>
              
              <div style="margin-bottom: 30px;">
                <h3 style="color: #152660; margin-bottom: 15px;">Was Sie erwarten können:</h3>
                <ul style="list-style: none; padding: 0;">
                  <li style="margin-bottom: 10px; padding-left: 25px; position: relative;">
                    <span style="position: absolute; left: 0; color: #152660;">✓</span>
                    Wöchentliche Tech-Insights und Tutorials
                  </li>
                  <li style="margin-bottom: 10px; padding-left: 25px; position: relative;">
                    <span style="position: absolute; left: 0; color: #152660;">✓</span>
                    Exklusive Flutter Best Practices
                  </li>
                  <li style="margin-bottom: 10px; padding-left: 25px; position: relative;">
                    <span style="position: absolute; left: 0; color: #152660;">✓</span>
                    Early Access zu neuen Features
                  </li>
                  <li style="margin-bottom: 10px; padding-left: 25px; position: relative;">
                    <span style="position: absolute; left: 0; color: #152660;">✓</span>
                    Case Studies von realen Projekten
                  </li>
                </ul>
              </div>
              
              <div style="text-align: center; margin-bottom: 30px;">
                <a href="https://innovatio-pro.com/blog" style="background: linear-gradient(135deg, #152660, #1e3a8a); color: white; padding: 12px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; display: inline-block;">
                  Blog besuchen
                </a>
              </div>
              
              <div style="border-top: 1px solid #eee; padding-top: 20px; text-align: center; color: #666; font-size: 14px;">
                <p>Sie können sich jederzeit abmelden, indem Sie auf den Link in unseren E-Mails klicken.</p>
                <p style="margin-top: 10px;">
                  <strong>Innovatio</strong><br>
                  Innovative Mobile Solutions<br>
                  <a href="https://innovatio-pro.com" style="color: #152660;">innovatio-pro.com</a>
                </p>
              </div>
            </div>
          `,
                });
            } catch (emailError) {
                console.error('Email sending error:', emailError);
                // Don't fail the subscription if email fails
            }
        }

        return NextResponse.json({
            success: true,
            message: 'Successfully subscribed to newsletter'
        });

    } catch (error) {
        console.error('Newsletter subscription error:', error);
        return NextResponse.json(
            { error: 'Failed to subscribe to newsletter' },
            { status: 500 }
        );
    }
} 