import { NextRequest, NextResponse } from 'next/server';
import nodemailer from 'nodemailer';
import { Resend } from 'resend';

export async function POST(request: NextRequest) {
  try {
    const { name, email, subject, message } = await request.json();

    // Validate required fields
    if (!name || !email || !subject || !message) {
      return NextResponse.json(
        { error: 'All fields are required' },
        { status: 400 }
      );
    }

    console.log('=== Environment Variables Check ===');
    console.log('SMTP_HOST:', process.env.SMTP_HOST);
    console.log('SMTP_PORT:', process.env.SMTP_PORT);
    console.log('SMTP_USER:', process.env.SMTP_USER);
    console.log('SMTP_PASS is set:', !!process.env.SMTP_PASS);
    console.log('RESEND_API_KEY is set:', !!process.env.RESEND_API_KEY);
    console.log('=====================================');

    // Try SMTP first, then fallback to Resend
    let emailSent = false;
    let lastError: any;

    // SMTP Configuration attempts - Google Workspace optimized
    const smtpConfigs = [
      {
        name: 'Google Workspace SMTP (Port 587)',
        host: 'smtp.gmail.com',
        port: 587,
        secure: false, // true for 465, false for other ports
        auth: {
          user: process.env.SMTP_USER || '<EMAIL>',
          pass: process.env.SMTP_PASS || 'your-google-app-password'
        },
        tls: {
          rejectUnauthorized: false
        }
      },
      {
        name: 'Google Workspace SMTP (Port 465)',
        host: 'smtp.gmail.com',
        port: 465,
        secure: true, // true for 465, false for other ports
        auth: {
          user: process.env.SMTP_USER || '<EMAIL>',
          pass: process.env.SMTP_PASS || 'your-google-app-password'
        },
        tls: {
          rejectUnauthorized: false
        }
      },
      {
        name: 'Netcup Backup SMTP',
        host: 'mail.innovatio-pro.com',
        port: 587,
        secure: false,
        auth: {
          user: process.env.SMTP_USER || '<EMAIL>',
          pass: process.env.SMTP_PASS || 'R9u359@wk'
        },
        tls: { rejectUnauthorized: false }
      },
      {
        name: 'Netcup Backup MX Server',
        host: 'mxe8f3.netcup.net',
        port: 587,
        secure: false,
        auth: {
          user: process.env.SMTP_USER || '<EMAIL>',
          pass: process.env.SMTP_PASS || 'R9u359@wk'
        },
        tls: { rejectUnauthorized: false }
      }
    ];

    // Try SMTP configurations
    for (const config of smtpConfigs) {
      try {
        console.log(`\n=== Trying SMTP: ${config.name} ===`);

        const transporter = nodemailer.createTransport(config);

        // Test connection with timeout
        const connectionPromise = transporter.verify();
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Connection timeout')), 5000)
        );

        await Promise.race([connectionPromise, timeoutPromise]);
        console.log('✅ SMTP connection successful!');

        const mailOptions = {
          from: `"Contact Form" <${config.auth.user}>`,
          to: '<EMAIL>',
          replyTo: email,
          subject: `New Contact Form Message: ${subject}`,
          text: `
New contact form submission:

Name: ${name}
Email: ${email}
Subject: ${subject}

Message:
${message}
          `,
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2 style="color: #3B82F6;">New Contact Form Submission</h2>
              
              <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <p><strong>Name:</strong> ${name}</p>
                <p><strong>Email:</strong> ${email}</p>
                <p><strong>Subject:</strong> ${subject}</p>
              </div>
              
              <div style="background: #ffffff; padding: 20px; border: 1px solid #e9ecef; border-radius: 8px;">
                <h3 style="color: #495057; margin-top: 0;">Message:</h3>
                <p style="line-height: 1.6; color: #6c757d;">${message.replace(/\n/g, '<br>')}</p>
              </div>
              
              <div style="margin-top: 20px; padding: 15px; background: #e7f3ff; border-radius: 8px;">
                <p style="margin: 0; color: #0066cc;">
                  <strong>Reply to:</strong> <a href="mailto:${email}">${email}</a>
                </p>
              </div>
              
              <hr style="margin: 30px 0; border: none; border-top: 1px solid #dee2e6;">
              <p style="color: #6c757d; font-size: 12px; text-align: center;">
                This email was sent from the contact form on innovatio-pro.com via SMTP
              </p>
            </div>
          `
        };

        const info = await transporter.sendMail(mailOptions);
        console.log('✅ SMTP Email sent successfully!');

        emailSent = true;
        return NextResponse.json({
          message: 'Email sent successfully via SMTP',
          messageId: info.messageId,
          method: 'SMTP'
        }, { status: 200 });

      } catch (error: any) {
        console.error(`❌ SMTP "${config.name}" failed:`, error?.message);
        lastError = error;
        continue;
      }
    }

    // If SMTP failed, try Resend
    if (!emailSent && process.env.RESEND_API_KEY) {
      try {
        console.log('\n=== Trying Resend API ===');

        const resend = new Resend(process.env.RESEND_API_KEY);

        const data = await resend.emails.send({
          from: 'Contact Form <<EMAIL>>',
          to: ['<EMAIL>'],
          replyTo: email,
          subject: `New Contact Form Message: ${subject}`,
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2 style="color: #3B82F6;">New Contact Form Submission</h2>
              
              <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <p><strong>Name:</strong> ${name}</p>
                <p><strong>Email:</strong> ${email}</p>
                <p><strong>Subject:</strong> ${subject}</p>
              </div>
              
              <div style="background: #ffffff; padding: 20px; border: 1px solid #e9ecef; border-radius: 8px;">
                <h3 style="color: #495057; margin-top: 0;">Message:</h3>
                <p style="line-height: 1.6; color: #6c757d;">${message.replace(/\n/g, '<br>')}</p>
              </div>
              
              <div style="margin-top: 20px; padding: 15px; background: #e7f3ff; border-radius: 8px;">
                <p style="margin: 0; color: #0066cc;">
                  <strong>Reply to:</strong> <a href="mailto:${email}">${email}</a>
                </p>
              </div>
              
              <hr style="margin: 30px 0; border: none; border-top: 1px solid #dee2e6;">
              <p style="color: #6c757d; font-size: 12px; text-align: center;">
                This email was sent from the contact form on innovatio-pro.com via Resend
              </p>
            </div>
          `
        });

        console.log('✅ Resend Email sent successfully!');
        emailSent = true;

        return NextResponse.json({
          message: 'Email sent successfully via Resend',
          messageId: data.data?.id || 'resend-email-sent',
          method: 'Resend'
        }, { status: 200 });

      } catch (error: any) {
        console.error('❌ Resend failed:', error?.message);
        lastError = error;
      }
    }

    // If both methods failed, throw the last error
    if (!emailSent) {
      throw lastError || new Error('No email service available');
    }

  } catch (error: any) {
    console.error('💥 All email methods failed:', error);

    let errorMessage = 'Failed to send email';
    let errorDetails = error?.message || 'Unknown error';

    if (error?.code === 'EAUTH') {
      errorMessage = 'Authentication failed. SMTP credentials may be incorrect.';
      errorDetails = 'Please verify your email credentials in the hosting control panel.';
    } else if (error?.code === 'ECONNECTION' || error?.code === 'ECONNREFUSED') {
      errorMessage = 'Connection failed. SMTP server may be unreachable.';
      errorDetails = 'The SMTP server is not accepting connections. This could be due to firewall restrictions or server issues.';
    }

    return NextResponse.json({
      error: errorMessage,
      details: errorDetails,
      suggestions: [
        'Check SMTP server configuration',
        'Verify firewall settings allow SMTP connections',
        'Consider using Resend by adding RESEND_API_KEY to environment variables',
        'Contact your hosting provider for SMTP support'
      ]
    }, { status: 500 });
  }
} 