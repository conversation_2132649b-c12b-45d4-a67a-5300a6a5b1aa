// Enhanced Contact Form API Route - Innovatio-Pro
import { NextRequest, NextResponse } from 'next/server';
import { Client } from '@notionhq/client';
import { v4 as uuidv4 } from 'uuid';
import * as crypto from 'crypto';

// Initialize Notion client
const notion = new Client({
    auth: process.env.NOTION_TOKEN,
});

const CRM_DATABASE_ID = process.env.NOTION_CRM_DATABASE_ID || '';

if (!CRM_DATABASE_ID) {
    console.warn('NOTION_CRM_DATABASE_ID not set in environment variables');
}

// Helper function to generate secure URL password
function generateUrlPassword(): string {
    // Define character sets
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const numbers = '0123456789';
    const specialChars = '!@#$%&*';

    // Combine all character sets
    const allChars = lowercase + uppercase + numbers + specialChars;

    let password = '';

    // Ensure at least one character from each set
    password += lowercase[Math.floor(Math.random() * lowercase.length)];
    password += uppercase[Math.floor(Math.random() * uppercase.length)];
    password += numbers[Math.floor(Math.random() * numbers.length)];
    password += specialChars[Math.floor(Math.random() * specialChars.length)];

    // Fill remaining 12 characters randomly
    for (let i = 4; i < 16; i++) {
        password += allChars[Math.floor(Math.random() * allChars.length)];
    }

    // Shuffle the password to avoid predictable patterns
    return password.split('').sort(() => Math.random() - 0.5).join('');
}

// Helper function to format price
function formatPrice(price: number): string {
    return new Intl.NumberFormat('de-DE', {
        style: 'currency',
        currency: 'EUR',
    }).format(price);
}

// Import service configurations
import { SERVICES, getServiceById } from '@/lib/config/services';

function getServicePrice(serviceId: string): number {
    const service = getServiceById(serviceId);
    if (!service) return 0;

    // Handle both basePrice and price properties for backward compatibility
    return 'basePrice' in service ? service.basePrice : service.price;
}

// Create lead in Notion CRM
async function createLeadFromContact(data: {
    name: string;
    email: string;
    phone?: string;
    company?: string;
    message: string;
    selectedService?: string;
    estimatedBudget?: string;
    projectTimeline?: string;
    additionalServices?: string[];
    source?: string;
}) {
    try {
        console.log('Creating Notion entry with data:', data);
        console.log('Using database ID:', CRM_DATABASE_ID);
        console.log('Token available:', !!process.env.NOTION_TOKEN);

        const clientId = uuidv4();
        const urlPassword = generateUrlPassword();
        const proposalUrl = `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/proposal/${clientId}?auth=${urlPassword}`;
        const contractUrl = `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/contract/${clientId}?auth=${urlPassword}`;

        // Calculate estimated value based on selected service
        let estimatedValue = 0;
        if (data.selectedService) {
            estimatedValue = getServicePrice(data.selectedService);
        }

        // If no estimated budget is provided, use the service price as default
        let budgetValue = estimatedValue;
        if (data.estimatedBudget && data.estimatedBudget.trim() !== '') {
            // Convert budget range to numeric value for storage
            const budgetMap: { [key: string]: number } = {
                'under_5k': 2500,
                '5k_10k': 7500,
                '10k_20k': 15000,
                '20k_50k': 35000,
                'over_50k': 75000,
                'not_sure': estimatedValue || 5000
            };
            budgetValue = budgetMap[data.estimatedBudget] || estimatedValue;
        }

        // First, let's test if we can access the database and get its structure
        try {
            const database = await notion.databases.retrieve({
                database_id: CRM_DATABASE_ID,
            });
            console.log('Database access successful');
            console.log('Available properties in database:', Object.keys(database.properties));

            // Log each property with its type
            Object.entries(database.properties).forEach(([name, prop]) => {
                console.log(`Property: "${name}" - Type: ${(prop as any).type}`);
            });
        } catch (dbError) {
            console.error('Database access error:', dbError);
            throw new Error(`Cannot access Notion database: ${(dbError as any).message}`);
        }

        const response = await notion.pages.create({
            parent: {
                database_id: CRM_DATABASE_ID,
            },
            properties: {
                'Name': {
                    title: [
                        {
                            text: {
                                content: data.name,
                            },
                        },
                    ],
                },
                'E-mail': {
                    email: data.email,
                },
                ...(data.phone && data.phone.trim() !== '' ? {
                    'Phone': {
                        phone_number: data.phone,
                    }
                } : {}),
                ...(data.company && data.company.trim() !== '' ? {
                    'CompanyName': {
                        rich_text: [
                            {
                                text: {
                                    content: data.company,
                                },
                            },
                        ],
                    }
                } : {}),
                'Status': {
                    select: {
                        name: 'New Lead',
                    },
                },
                ...(data.selectedService ? {
                    'InterestedService': {
                        select: {
                            name: getServiceById(data.selectedService)?.name || data.selectedService,
                        },
                    }
                } : {}),
                'EstimatedBudget': {
                    number: budgetValue,
                },
                ...(data.projectTimeline && data.projectTimeline.trim() !== '' ? {
                    'ProjectTimeline': {
                        rich_text: [
                            {
                                text: {
                                    content: data.projectTimeline,
                                },
                            },
                        ],
                    }
                } : {}),
                'HeardAbout': {
                    rich_text: [
                        {
                            text: {
                                content: data.source || 'Website Contact Form',
                            },
                        },
                    ],
                },
                'Description': {
                    rich_text: [
                        {
                            text: {
                                content: data.message,
                            },
                        },
                    ],
                },
                'ServicePrice': {
                    number: estimatedValue,
                },
                'ClientID': {
                    rich_text: [
                        {
                            text: {
                                content: clientId,
                            },
                        },
                    ],
                },
                'ProposalURL': {
                    url: proposalUrl,
                },
                'ContractURL': {
                    url: contractUrl,
                },
                'URLPassword': {
                    rich_text: [
                        {
                            text: {
                                content: urlPassword,
                            },
                        },
                    ],
                },
                'Created Date': {
                    date: {
                        start: new Date().toISOString().split('T')[0],
                    },
                },
            },
        });

        console.log('Created Notion entry successfully:', response.id);

        return {
            notionId: response.id,
            clientId,
            proposalUrl,
            contractUrl,
            urlPassword,
        };

    } catch (error) {
        console.error('Detailed error creating Notion entry:', error);
        throw error;
    }
}

export async function POST(request: NextRequest) {
    try {
        const formData = await request.json();
        console.log('Received form data:', formData);

        // Basic validation
        if (!formData.name || !formData.email || !formData.message) {
            return NextResponse.json(
                {
                    success: false,
                    error: 'Name, email, and message are required'
                },
                { status: 400 }
            );
        }

        // Email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(formData.email)) {
            return NextResponse.json(
                {
                    success: false,
                    error: 'Please provide a valid email address'
                },
                { status: 400 }
            );
        }

        // Check if Notion is configured and working
        if (!CRM_DATABASE_ID || !process.env.NOTION_TOKEN) {
            console.warn('⚠️  Notion CRM not configured - saving submission to logs');
        } else {
            // Try to create lead in Notion CRM
            try {
                const lead = await createLeadFromContact({
                    name: formData.name,
                    email: formData.email,
                    phone: formData.phone,
                    company: formData.companyName,
                    message: formData.message,
                    selectedService: formData.selectedService,
                    estimatedBudget: formData.estimatedBudget,
                    projectTimeline: formData.projectTimeline,
                    additionalServices: [], // Not currently collected in the form
                    source: formData.heardAbout,
                });

                console.log('✅ Notion CRM entry created successfully:', {
                    leadId: lead.clientId,
                    notionId: lead.notionId
                });

                return NextResponse.json({
                    success: true,
                    message: 'Thank you for your message! We will get back to you soon.',
                    data: {
                        leadId: lead.clientId,
                        proposalUrl: lead.proposalUrl,
                        // Don't expose sensitive data like password
                    }
                });

            } catch (notionError) {
                console.error('❌ Notion CRM error - falling back to manual processing:', notionError);
            }
        }

        // Fallback: Log submission for manual processing
        const tempId = 'temp-' + Date.now();
        console.log('📝 MANUAL PROCESSING REQUIRED - Contact Form Submission:', {
            tempId,
            timestamp: new Date().toISOString(),
            data: {
                name: formData.name,
                email: formData.email,
                phone: formData.phone || 'N/A',
                company: formData.companyName || 'N/A',
                selectedService: formData.selectedService || 'N/A',
                estimatedBudget: formData.estimatedBudget || 'N/A',
                projectTimeline: formData.projectTimeline || 'N/A',
                heardAbout: formData.heardAbout || 'N/A',
                message: formData.message
            },
            note: 'Notion CRM unavailable - manual entry required'
        });

        return NextResponse.json({
            success: true,
            message: 'Thank you for your message! We will get back to you soon.',
            data: {
                leadId: tempId,
                proposalUrl: null,
                note: 'Your submission has been received and will be processed manually.'
            }
        });

    } catch (error) {
        console.error('❌ Enhanced contact form error:', error);

        return NextResponse.json(
            {
                success: false,
                error: 'Failed to submit form. Please try again later.',
                details: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
            },
            { status: 500 }
        );
    }
}

// Handle OPTIONS request for CORS
export async function OPTIONS() {
    return NextResponse.json({}, { status: 200 });
} 