import { NextRequest, NextResponse } from 'next/server';
import { Client } from '@notionhq/client';

const notion = new Client({
    auth: process.env.NOTION_TOKEN,
});

const DATABASE_ID = process.env.NOTION_CRM_DATABASE_ID || '20af774e19468030b825e8bdbe58605a';

export async function POST(request: NextRequest) {
    try {
        const { clientId } = await request.json();

        console.log('Processing proposal acceptance for client:', clientId);

        // First, find the Notion page by clientId
        const response = await notion.databases.query({
            database_id: DATABASE_ID,
            filter: {
                property: 'ClientID',
                rich_text: {
                    equals: clientId
                }
            }
        });

        if (response.results.length === 0) {
            console.error('No Notion page found for clientId:', clientId);
            return NextResponse.json(
                { success: false, error: 'Client not found in database' },
                { status: 404 }
            );
        }

        const notionPage = response.results[0];
        const notionPageId = notionPage.id;

        // Get URLPassword from the Notion page
        const properties = (notionPage as any).properties;
        const urlPassword = properties['URLPassword']?.rich_text?.[0]?.text?.content || '';

        // Update Notion status to "Qualified"
        try {
            await notion.pages.update({
                page_id: notionPageId,
                properties: {
                    'Status': {
                        select: {
                            name: 'Qualified'
                        }
                    },
                    'ProposalURL': {
                        url: `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/proposal/${clientId}?auth=${urlPassword}`
                    },
                    'ContractURL': {
                        url: `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/contract/${clientId}?auth=${urlPassword}`
                    }
                },
            });

            console.log('Notion updated successfully for page:', notionPageId);
        } catch (notionError) {
            console.error('Error updating Notion:', notionError);
            return NextResponse.json(
                {
                    success: false,
                    error: 'Failed to update Notion page. Please check if the page exists and is accessible.'
                },
                { status: 404 }
            );
        }

        // Send contract email
        try {
            const emailResponse = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/send-contract-email`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    clientId,
                    contractUrl: `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/contract/${clientId}?auth=${urlPassword}`,
                }),
            });

            if (!emailResponse.ok) {
                console.error('Failed to send contract email');
            }
        } catch (emailError) {
            console.error('Error sending contract email:', emailError);
        }

        return NextResponse.json({
            success: true,
            message: 'Proposal accepted successfully'
        });

    } catch (error) {
        console.error('Error processing proposal acceptance:', error);
        return NextResponse.json(
            {
                success: false,
                error: 'Failed to process proposal acceptance'
            },
            { status: 500 }
        );
    }
} 