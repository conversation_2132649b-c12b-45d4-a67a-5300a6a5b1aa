import { NextRequest, NextResponse } from 'next/server';
import { Client } from '@notionhq/client';

const notion = new Client({
    auth: process.env.NOTION_TOKEN,
});

const DATABASE_ID = process.env.NOTION_CRM_DATABASE_ID || '20af774e19468030b825e8bdbe58605a';



export async function POST(request: NextRequest) {
    try {
        const { clientId, modifications, date } = await request.json();

        console.log('Processing modification request for client:', clientId);

        // Format modification request with timestamp
        const newChangeRequest = `[${new Date(date).toLocaleDateString('de-DE')} ${new Date(date).toLocaleTimeString('de-DE')}]\n${modifications}`;

        // First, find the Notion page by clientId
        const response = await notion.databases.query({
            database_id: DATABASE_ID,
            filter: {
                property: 'ClientID',
                rich_text: {
                    equals: clientId
                }
            }
        });

        if (response.results.length === 0) {
            console.error('No Notion page found for clientId:', clientId);
            return NextResponse.json(
                { success: false, error: 'Client not found in database' },
                { status: 404 }
            );
        }

        const notionPage = response.results[0];
        const notionPageId = notionPage.id;

        // Get existing change requests first
        let existingChangeRequests = '';
        try {
            const properties = (notionPage as any).properties;
            existingChangeRequests = properties['ChangeRequest']?.rich_text?.[0]?.text?.content || '';
        } catch (error) {
            console.log('Could not retrieve existing change requests:', error);
        }

        // Append new change request with divider
        const allChangeRequests = existingChangeRequests
            ? `${existingChangeRequests}\n\n--- NEUE ÄNDERUNG ---\n\n${newChangeRequest}`
            : newChangeRequest;

        // Update Notion with modification request
        try {
            await notion.pages.update({
                page_id: notionPageId,
                properties: {
                    'Status': {
                        select: {
                            name: 'ChangeRequest'
                        }
                    },
                    'ChangeRequest': {
                        rich_text: [
                            {
                                type: 'text',
                                text: {
                                    content: allChangeRequests
                                }
                            }
                        ]
                    },
                    'ChangeRequestDate': {
                        date: {
                            start: new Date(date).toISOString()
                        }
                    }
                },
            });

            console.log('Notion updated with modification request for page:', notionPageId);
        } catch (notionError) {
            console.error('Error updating Notion:', notionError);
            return NextResponse.json(
                {
                    success: false,
                    error: 'Failed to update Notion page. Please check if the page exists and is accessible.'
                },
                { status: 404 }
            );
        }

        // Send notification email to admin
        try {
            const notificationResponse = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/send-modification-notification`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    clientId,
                    modifications,
                    date,
                }),
            });

            if (!notificationResponse.ok) {
                console.error('Failed to send modification notification email');
            }
        } catch (emailError) {
            console.error('Error sending notification email:', emailError);
            // Continue even if email fails
        }

        return NextResponse.json({
            success: true,
            message: 'Modification request submitted successfully'
        });

    } catch (error) {
        console.error('Error processing modification request:', error);
        return NextResponse.json(
            {
                success: false,
                error: 'Failed to process modification request'
            },
            { status: 500 }
        );
    }
} 