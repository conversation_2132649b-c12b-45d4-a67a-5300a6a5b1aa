import { NextRequest, NextResponse } from 'next/server';
import { Client } from '@notionhq/client';

const notion = new Client({
    auth: process.env.NOTION_TOKEN,
});

const CRM_DATABASE_ID = process.env.NOTION_CRM_DATABASE_ID || '';

export async function POST(request: NextRequest) {
    try {
        const { clientId, signature, agreements } = await request.json();

        if (!CRM_DATABASE_ID) {
            console.error('NOTION_CRM_DATABASE_ID is not configured');
            return NextResponse.json(
                { error: 'Database configuration missing' },
                { status: 500 }
            );
        }

        // Find the Notion page by clientId
        const response = await notion.databases.query({
            database_id: CRM_DATABASE_ID,
            filter: {
                property: 'ClientID',
                rich_text: {
                    equals: clientId
                }
            }
        });

        if (response.results.length === 0) {
            return NextResponse.json(
                { error: 'Client not found' },
                { status: 404 }
            );
        }

        const notionPage = response.results[0];
        const notionPageId = notionPage.id;

        // Split signature into 3 parts (due to Notion's text field limitations)
        const signatureLength = signature.length;
        const partLength = Math.ceil(signatureLength / 3);
        const signature1 = signature.substring(0, partLength);
        const signature2 = signature.substring(partLength, partLength * 2);
        const signature3 = signature.substring(partLength * 2);

        // Update Notion with signature data and status
        await notion.pages.update({
            page_id: notionPageId,
            properties: {
                'Status': {
                    select: {
                        name: 'Won'
                    }
                },
                'Signature1': {
                    rich_text: [{ text: { content: signature1 } }]
                },
                'Signature2': {
                    rich_text: [{ text: { content: signature2 } }]
                },
                'Signature3': {
                    rich_text: [{ text: { content: signature3 } }]
                },
                'SignatureDate': {
                    date: { start: new Date().toISOString() }
                },
                'LegalAgreements': {
                    rich_text: [{
                        text: {
                            content: JSON.stringify(agreements)
                        }
                    }]
                }
            }
        });

        return NextResponse.json({
            success: true,
            message: 'Contract concluded successfully'
        });

    } catch (error) {
        console.error('Error concluding contract:', error);
        return NextResponse.json(
            { error: 'Failed to conclude contract' },
            { status: 500 }
        );
    }
} 