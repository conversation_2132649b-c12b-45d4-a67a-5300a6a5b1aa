import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
    return NextResponse.json({
        SMTP_HOST: process.env.SMTP_HOST,
        SMTP_PORT: process.env.SMTP_PORT,
        SMTP_USER: process.env.SMTP_USER,
        SMTP_PASS_SET: !!process.env.SMTP_PASS,
        RESEND_API_KEY_SET: !!process.env.RESEND_API_KEY,
        RESEND_API_KEY_PREFIX: process.env.RESEND_API_KEY?.substring(0, 6) || 'not set',
        NODE_ENV: process.env.NODE_ENV
    });
} 