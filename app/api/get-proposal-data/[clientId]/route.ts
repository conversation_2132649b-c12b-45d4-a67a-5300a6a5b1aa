import { NextRequest, NextResponse } from 'next/server';
import { Client } from '@notionhq/client';

const notion = new Client({
    auth: process.env.NOTION_TOKEN,
});

const CRM_DATABASE_ID = process.env.NOTION_CRM_DATABASE_ID || '';

if (!CRM_DATABASE_ID) {
    console.warn('NOTION_CRM_DATABASE_ID not set in environment variables');
}

// Complete package definitions with all details
const SERVICE_PACKAGES: { [key: string]: any } = {
    mvp: {
        id: 'mvp',
        name: 'MVP Development',
        price: 8500,
        originalPrice: 10000,
        timeframe: '3-4 weeks',
        description: 'Launch your idea quickly with a Minimum Viable Product',
        features: [
            'Core functionality implementation',
            'Modern UI/UX design',
            'User authentication & security',
            'Scalable data storage solution',
            'Cross-platform deployment',
            'Worldwide Compliance Ready',
            '3 months support included',
            'Performance optimization'
        ],
        trustIndicators: ['⚡ Fast Delivery', '🔒 Secure', '📱 Modern']
    },
    prototype: {
        id: 'prototype',
        name: 'Rapid Prototype',
        price: 4200,
        originalPrice: 5000,
        timeframe: '1-2 weeks',
        description: 'Test your concept with a functional prototype',
        features: [
            'Interactive UI mockups',
            'Core functionality demo',
            'User flow implementation',
            'Stakeholder presentation',
            'Flutter-driven development',
            'Iteration feedback loops',
            'Technical documentation'
        ],
        trustIndicators: ['🚀 Quick Setup', '✨ Interactive', '📊 Analytics']
    },
    landingpage: {
        id: 'landingpage',
        name: 'Landing Page Development',
        price: 3000,
        originalPrice: 3500,
        timeframe: '2-4 weeks',
        description: 'WCAG 2.0 compliant landing pages with the latest web technologies',
        features: [
            'WCAG 2.0 AA compliance',
            'Inclusive design for all users',
            'Screen reader compatibility',
            'High-performance metrics',
            'SEO optimized structure',
            'Responsive design',
            'Analytics integration',
            'Conversion optimization'
        ],
        trustIndicators: ['♿ Accessible', '🎯 Optimized', '📈 High Convert']
    },
    architecture: {
        id: 'architecture',
        name: 'Project Architecture',
        price: 3800,
        originalPrice: 4500,
        timeframe: '1-2 weeks',
        description: 'Solid foundation for your project\'s success',
        features: [
            'Technical specifications',
            'System architecture design',
            'Database schema design',
            'API documentation',
            'Development roadmap',
            'Security planning',
            'Scalability guidelines',
            'Technology recommendations'
        ],
        trustIndicators: ['🏗️ Scalable', '🔐 Secure', '📋 Documented']
    },
    consulting: {
        id: 'consulting',
        name: 'Technical Consulting',
        price: 110,
        originalPrice: 130,
        timeframe: 'Ongoing',
        description: 'Expert guidance for your technical decisions',
        features: [
            'Technology stack recommendations',
            'Code reviews & quality assurance',
            'Performance optimization',
            'Security assessment',
            'Scalability planning',
            'Payment gateway integration',
            'Team mentoring',
            'Best practices implementation'
        ],
        trustIndicators: ['👨‍💻 Expert', '⚡ Available', '🎓 Mentoring']
    },
    fullstack: {
        id: 'fullstack',
        name: 'Full-Stack Development',
        price: 12000,
        originalPrice: 15000,
        timeframe: '6-8 weeks',
        description: 'Complete full-stack application development',
        features: [
            'Frontend & Backend development',
            'Database design & implementation',
            'User authentication system',
            'Payment gateway integration',
            'Admin dashboard',
            'API development',
            'Testing & deployment',
            '6 months support included'
        ],
        trustIndicators: ['💻 Full-Stack', '🔧 Complete', '🛡️ Secure']
    },
    homepage: {
        id: 'homepage',
        name: 'Professional Homepage',
        price: 3999,
        originalPrice: 4999,
        timeframe: '3-5 weeks',
        description: 'Professional homepage with modern design',
        features: [
            'Responsive design',
            'SEO optimization',
            'Contact forms',
            'Social media integration',
            'Analytics setup',
            'Performance optimization',
            'Content management',
            '3 months support'
        ],
        trustIndicators: ['🎨 Professional', '📱 Responsive', '🚀 Fast']
    }
};

export async function GET(
    request: NextRequest,
    { params }: { params: Promise<{ clientId: string }> }
) {
    try {
        const { clientId } = await params;

        if (!CRM_DATABASE_ID) {
            console.error('NOTION_CRM_DATABASE_ID is not configured');
            return NextResponse.json(
                { error: 'Database configuration missing' },
                { status: 500 }
            );
        }

        // Query the database for an entry with this Client ID
        const response = await notion.databases.query({
            database_id: CRM_DATABASE_ID,
            filter: {
                property: 'ClientID',
                rich_text: {
                    equals: clientId
                }
            }
        });

        if (response.results.length === 0) {
            return NextResponse.json(
                { error: 'Proposal not found' },
                { status: 404 }
            );
        }

        // Get the first matching page
        const page = response.results[0];

        // Extract properties from the Notion page
        const properties = (page as any).properties;

        // Map budget properly - check both EstimatedBudget (number) and ServicePrice (number)
        let budgetValue = '5k_10k'; // default
        if (properties['EstimatedBudget']?.number) {
            const budget = properties['EstimatedBudget'].number;
            if (budget <= 5000) budgetValue = 'under_5k';
            else if (budget <= 10000) budgetValue = '5k_10k';
            else if (budget <= 20000) budgetValue = '10k_20k';
            else if (budget <= 50000) budgetValue = '20k_50k';
            else budgetValue = 'over_50k';
        } else if (properties['ServicePrice']?.number) {
            const budget = properties['ServicePrice'].number;
            if (budget <= 5000) budgetValue = 'under_5k';
            else if (budget <= 10000) budgetValue = '5k_10k';
            else if (budget <= 20000) budgetValue = '10k_20k';
            else if (budget <= 50000) budgetValue = '20k_50k';
            else budgetValue = 'over_50k';
        }

        // Get selected service and package details
        const serviceName = properties['InterestedService']?.select?.name;
        const serviceNameToId: { [key: string]: string } = {
            'MVP Development': 'mvp',
            'Rapid Prototype': 'prototype',
            'Landing Page Development': 'landingpage',
            'Project Architecture': 'architecture',
            'Technical Consulting': 'consulting',
            'Full-Stack Development': 'fullstack',
            'Professional Homepage': 'homepage'
        };
        const selectedServiceId = serviceNameToId[serviceName || ''] || 'landingpage';
        const selectedPackage = SERVICE_PACKAGES[selectedServiceId];

        const proposalData = {
            clientName: properties['Name']?.title?.[0]?.text?.content || 'Unknown Client',
            clientEmail: properties['E-mail']?.email || '',
            company: properties['CompanyName']?.rich_text?.[0]?.text?.content || '',
            phone: properties['Phone']?.phone_number || '',
            selectedService: selectedServiceId,
            selectedPackage: selectedPackage, // Full package details
            estimatedBudget: budgetValue,
            projectTimeline: properties['ProjectTimeline']?.rich_text?.[0]?.text?.content || '3_6_months',
            message: properties['Description']?.rich_text?.[0]?.text?.content || '',
            heardAbout: properties['HeardAbout']?.rich_text?.[0]?.text?.content || 'linkedin',
            // New fields for change requests
            changeRequests: properties['ChangeRequest']?.rich_text?.[0]?.text?.content || '',
            changeRequestDate: properties['ChangeRequestDate']?.date?.start || null,
            status: properties['Status']?.select?.name || 'new',
            // Password protection
            urlPassword: properties['URLPassword']?.rich_text?.[0]?.text?.content || '',
            // Signature data (if exists)
            signature1: properties['Signature1']?.rich_text?.[0]?.text?.content || '',
            signature2: properties['Signature2']?.rich_text?.[0]?.text?.content || '',
            signature3: properties['Signature3']?.rich_text?.[0]?.text?.content || '',
            signatureDate: properties['SignatureDate']?.date?.start || null
        };

        console.log('Retrieved proposal data from Notion:', proposalData);

        return NextResponse.json(proposalData);

    } catch (error) {
        console.error('Error fetching proposal data:', error);
        return NextResponse.json(
            { error: 'Failed to fetch proposal data' },
            { status: 404 }
        );
    }
} 