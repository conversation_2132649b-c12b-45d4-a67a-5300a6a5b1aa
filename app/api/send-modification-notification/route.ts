import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
    try {
        const { clientId, modifications, date } = await request.json();

        // This is a placeholder for email sending logic
        // You can integrate with your preferred email service (Resend, SendGrid, etc.)

        console.log('Modification notification email should be sent:', {
            clientId,
            modifications,
            date,
            to: '<EMAIL>',
            subject: `Modification Request - Client ${clientId}`,
            template: 'modification-request',
            data: {
                clientId,
                modifications,
                requestDate: new Date(date).toLocaleDateString('de-DE'),
                requestTime: new Date(date).toLocaleTimeString('de-DE')
            }
        });

        // For demo purposes, we'll just log and return success
        // In production, integrate with your email service

        return NextResponse.json({
            success: true,
            message: 'Modification notification sent successfully'
        });

    } catch (error) {
        console.error('Error sending modification notification:', error);
        return NextResponse.json(
            {
                success: false,
                error: 'Failed to send modification notification'
            },
            { status: 500 }
        );
    }
} 