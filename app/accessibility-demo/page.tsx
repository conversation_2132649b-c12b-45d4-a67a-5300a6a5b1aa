'use client'

import React, { useState } from 'react'
import { useAccessibility } from '@/providers/AccessibilityProvider'
import { AccessibleHeading } from '@/components/ui/AccessibleHeading'
import { AccessibleImage } from '@/components/ui/AccessibleImage'
import { AccessibleForm, FormField } from '@/components/ui/AccessibleForm'
import { AccessibleNavigation } from '@/components/ui/AccessibleNavigation'
import { AccessibleTabs } from '@/components/ui/AccessibleTabs'
import { AccessibleDialog } from '@/components/ui/AccessibleDialog'
import { AccessibleTooltip } from '@/components/ui/AccessibleTooltip'
import { AccessibleAccordion } from '@/components/ui/AccessibleAccordion'
import { AccessibleAlert } from '@/components/ui/AccessibleAlert'
import { AccessibleBreadcrumbs } from '@/components/ui/AccessibleBreadcrumbs'
import { AccessiblePagination } from '@/components/ui/AccessiblePagination'
import { AccessibleTable } from '@/components/ui/AccessibleTable'
import { Button } from '@/components/ui/Button'
import { Section } from '@/components/ui/Section'
import { useKeyboardNavigation } from '@/hooks/useKeyboardNavigation'
import { useMediaQuery } from '@/hooks/useMediaQuery'
import { Info, AlertTriangle, CheckCircle, AlertCircle } from 'lucide-react'
import ClientOnly from "@/components/ui/ClientOnly";

// Prevent prerendering of this page
export const dynamic = 'force-dynamic'

function AccessibilityDemoContent() {
  // Verwende die Barrierefreiheits-Einstellungen
  const { settings } = useAccessibility()
  
  // Verwende den Keyboard-Navigation-Hook
  useKeyboardNavigation()
  
  // Verwende den Media-Query-Hook
  const isMobile = useMediaQuery('(max-width: 768px)')
  
  // State für die Demo-Komponenten
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: '',
  })
  const [formErrors, setFormErrors] = useState<Record<string, string>>({})
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [alertType, setAlertType] = useState<'info' | 'success' | 'warning' | 'error'>('info')
  const [isAlertOpen, setIsAlertOpen] = useState(false)
  
  // Beispieldaten für die Tabelle
  const tableData = [
    { id: '1', name: 'Max Mustermann', email: '<EMAIL>', role: 'Admin' },
    { id: '2', name: 'Erika Musterfrau', email: '<EMAIL>', role: 'Editor' },
    { id: '3', name: 'John Doe', email: '<EMAIL>', role: 'User' },
    { id: '4', name: 'Jane Smith', email: '<EMAIL>', role: 'User' },
  ]
  
  // Spalten für die Tabelle
  const tableColumns = [
    {
      id: 'name',
      header: 'Name',
      accessor: (row: any) => row.name,
      sortable: true,
    },
    {
      id: 'email',
      header: 'E-Mail',
      accessor: (row: any) => row.email,
      sortable: true,
    },
    {
      id: 'role',
      header: 'Rolle',
      accessor: (row: any) => row.role,
      sortable: true,
    },
  ]
  
  // Formular-Handler
  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    // Einfache Validierung
    const errors: Record<string, string> = {}
    
    if (!formData.name) {
      errors.name = 'Name ist erforderlich'
    }
    
    if (!formData.email) {
      errors.email = 'E-Mail ist erforderlich'
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'E-Mail ist ungültig'
    }
    
    if (!formData.message) {
      errors.message = 'Nachricht ist erforderlich'
    }
    
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors)
      return
    }
    
    // Formular erfolgreich abgeschickt
    setFormErrors({})
    setAlertType('success')
    setIsAlertOpen(true)
    
    // Formular zurücksetzen
    setFormData({
      name: '',
      email: '',
      message: '',
    })
  }
  
  return (
    <main className="min-h-screen py-12">
      <Section>
        <AccessibleHeading level={1} className="text-center mb-8">
          Barrierefreiheits-Demo
        </AccessibleHeading>
        
        <AccessibleBreadcrumbs
          items={[
            { label: 'Barrierefreiheit', href: '/accessibility' },
            { label: 'Demo', href: '/accessibility-demo' },
          ]}
          className="mb-8"
        />
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
          <div className="col-span-1 md:col-span-2">
            <AccessibleHeading level={2} className="mb-4">
              Barrierefreie Komponenten
            </AccessibleHeading>
            
            <p className="mb-6">
              Diese Seite demonstriert die barrierefreien Komponenten, die für die Website implementiert wurden.
              Die Komponenten sind WCAG 2.1 AA-konform und unterstützen verschiedene Barrierefreiheits-Features.
            </p>
            
            <AccessibleTabs
              tabs={[
                {
                  id: 'components',
                  label: 'Komponenten',
                  content: (
                    <div className="space-y-6 py-4">
                      <div>
                        <AccessibleHeading level={3} className="mb-2">
                          Bilder
                        </AccessibleHeading>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <AccessibleImage
                            src="https://images.unsplash.com/photo-1617791160505-6f00504e3519?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=500&h=300&q=80"
                            alt="Barrierefreies Webdesign"
                            width={500}
                            height={300}
                            caption="Beispielbild mit Bildunterschrift"
                            longDescription="Ein Bild, das Barrierefreiheit im Webdesign symbolisiert. Es zeigt einen Laptop mit einer Website, die verschiedene Barrierefreiheits-Features aufweist."
                            showCaption
                          />
                          
                          <AccessibleImage
                            src="https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=500&h=300&q=80"
                            alt="Barrierefreie Technologie"
                            width={500}
                            height={300}
                          />
                        </div>
                      </div>
                      
                      <div>
                        <AccessibleHeading level={3} className="mb-2">
                          Akkordeon
                        </AccessibleHeading>
                        
                        <AccessibleAccordion
                          items={[
                            {
                              id: 'accordion-1',
                              title: 'Was ist Barrierefreiheit?',
                              content: (
                                <p>
                                  Barrierefreiheit bedeutet, dass Websites und Anwendungen so gestaltet sind,
                                  dass sie von allen Menschen, einschließlich Menschen mit Behinderungen,
                                  genutzt werden können.
                                </p>
                              ),
                              icon: <Info className="h-5 w-5" />,
                            },
                            {
                              id: 'accordion-2',
                              title: 'Warum ist Barrierefreiheit wichtig?',
                              content: (
                                <p>
                                  Barrierefreiheit ist wichtig, weil sie sicherstellt, dass alle Menschen
                                  gleichberechtigt auf Informationen und Dienste zugreifen können.
                                  Außerdem ist sie in vielen Ländern gesetzlich vorgeschrieben.
                                </p>
                              ),
                              icon: <AlertTriangle className="h-5 w-5" />,
                            },
                            {
                              id: 'accordion-3',
                              title: 'Wie kann ich Barrierefreiheit testen?',
                              content: (
                                <p>
                                  Es gibt verschiedene Tools und Methoden, um die Barrierefreiheit einer Website
                                  zu testen, darunter automatisierte Tools wie Lighthouse und manuelle Tests
                                  mit Screenreadern und Tastaturnavigation.
                                </p>
                              ),
                              icon: <CheckCircle className="h-5 w-5" />,
                            },
                          ]}
                        />
                      </div>
                      
                      <div>
                        <AccessibleHeading level={3} className="mb-2">
                          Tooltips
                        </AccessibleHeading>
                        
                        <div className="flex space-x-4">
                          <AccessibleTooltip content="Dies ist ein Tooltip mit Informationen">
                            <Button variant="outline">
                              <Info className="h-4 w-4 mr-2" />
                              Info-Tooltip
                            </Button>
                          </AccessibleTooltip>
                          
                          <AccessibleTooltip
                            content="Dies ist ein Tooltip mit einer Warnung"
                            position="bottom"
                          >
                            <Button variant="outline">
                              <AlertTriangle className="h-4 w-4 mr-2" />
                              Warnungs-Tooltip
                            </Button>
                          </AccessibleTooltip>
                        </div>
                      </div>
                      
                      <div>
                        <AccessibleHeading level={3} className="mb-2">
                          Paginierung
                        </AccessibleHeading>
                        
                        <AccessiblePagination
                          currentPage={currentPage}
                          totalPages={10}
                          onPageChange={setCurrentPage}
                        />
                      </div>
                      
                      <div>
                        <AccessibleHeading level={3} className="mb-2">
                          Tabelle
                        </AccessibleHeading>
                        
                        <AccessibleTable
                          columns={tableColumns}
                          data={tableData}
                          keyExtractor={(row) => row.id}
                          sortable
                          defaultSortColumn="name"
                          caption="Beispieltabelle mit Benutzerdaten"
                        />
                      </div>
                      
                      <div className="flex space-x-4">
                        <Button
                          onClick={() => {
                            setAlertType('info')
                            setIsAlertOpen(true)
                          }}
                        >
                          <Info className="h-4 w-4 mr-2" />
                          Info-Benachrichtigung
                        </Button>
                        
                        <Button
                          onClick={() => {
                            setAlertType('warning')
                            setIsAlertOpen(true)
                          }}
                        >
                          <AlertTriangle className="h-4 w-4 mr-2" />
                          Warnungs-Benachrichtigung
                        </Button>
                        
                        <Button
                          onClick={() => {
                            setAlertType('error')
                            setIsAlertOpen(true)
                          }}
                        >
                          <AlertCircle className="h-4 w-4 mr-2" />
                          Fehler-Benachrichtigung
                        </Button>
                      </div>
                      
                      <Button onClick={() => setIsDialogOpen(true)}>
                        Dialog öffnen
                      </Button>
                    </div>
                  ),
                },
                {
                  id: 'form',
                  label: 'Formular',
                  content: (
                    <div className="py-4">
                      <AccessibleHeading level={3} className="mb-4">
                        Barrierefreies Formular
                      </AccessibleHeading>
                      
                      <AccessibleForm onSubmit={handleFormSubmit} aria-label="Kontaktformular">
                        <FormField
                          id="name"
                          label="Name"
                          value={formData.name}
                          onChange={(value) => setFormData({ ...formData, name: value })}
                          required
                          error={formErrors.name}
                          hint="Bitte geben Sie Ihren vollständigen Namen ein"
                        />
                        
                        <FormField
                          id="email"
                          label="E-Mail"
                          type="email"
                          value={formData.email}
                          onChange={(value) => setFormData({ ...formData, email: value })}
                          required
                          error={formErrors.email}
                          autoComplete="email"
                        />
                        
                        <FormField
                          id="message"
                          label="Nachricht"
                          type="textarea"
                          value={formData.message}
                          onChange={(value) => setFormData({ ...formData, message: value })}
                          required
                          error={formErrors.message}
                          rows={5}
                        />
                        
                        <div className="mt-6">
                          <Button type="submit">
                            Absenden
                          </Button>
                        </div>
                      </AccessibleForm>
                    </div>
                  ),
                },
                {
                  id: 'navigation',
                  label: 'Navigation',
                  content: (
                    <div className="py-4">
                      <AccessibleHeading level={3} className="mb-4">
                        Barrierefreie Navigation
                      </AccessibleHeading>
                      
                      <div className="space-y-8">
                        <div>
                          <h4 className="text-lg font-medium mb-2">Horizontale Navigation</h4>
                          
                          <AccessibleNavigation
                            items={[
                              { label: 'Startseite', href: '/' },
                              { label: 'Über uns', href: '/about' },
                              { label: 'Dienstleistungen', href: '/services' },
                              { label: 'Kontakt', href: '/contact' },
                            ]}
                            orientation="horizontal"
                          />
                        </div>
                        
                        <div>
                          <h4 className="text-lg font-medium mb-2">Vertikale Navigation</h4>
                          
                          <AccessibleNavigation
                            items={[
                              { label: 'Startseite', href: '/' },
                              { label: 'Über uns', href: '/about' },
                              { label: 'Dienstleistungen', href: '/services' },
                              { label: 'Kontakt', href: '/contact' },
                            ]}
                            orientation="vertical"
                            className="w-48"
                          />
                        </div>
                      </div>
                    </div>
                  ),
                },
              ]}
            />
          </div>
          
          <div className="col-span-1">
            <AccessibleHeading level={2} className="mb-4">
              Barrierefreiheits-Info
            </AccessibleHeading>
            
            <div className="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg">
              <AccessibleHeading level={3} className="mb-2">
                Aktuelle Einstellungen
              </AccessibleHeading>
              
              <ul className="space-y-2 mb-6">
                <li>
                  <strong>Schriftgröße:</strong> {settings.fontSize}%
                </li>
                <li>
                  <strong>Hochkontrast:</strong> {settings.highContrast ? 'Aktiviert' : 'Deaktiviert'}
                </li>
                <li>
                  <strong>Reduzierte Bewegung:</strong> {settings.reducedMotion ? 'Aktiviert' : 'Deaktiviert'}
                </li>
                <li>
                  <strong>Vorlesen:</strong> {settings.textToSpeechEnabled ? 'Aktiviert' : 'Deaktiviert'}
                </li>
                <li>
                  <strong>Gerätetyp:</strong> {isMobile ? 'Mobil' : 'Desktop'}
                </li>
              </ul>
              
              <AccessibleHeading level={3} className="mb-2">
                WCAG 2.1 AA-Konformität
              </AccessibleHeading>
              
              <p className="mb-4">
                Diese Website erfüllt die WCAG 2.1 AA-Richtlinien für Barrierefreiheit.
                Die wichtigsten Aspekte sind:
              </p>
              
              <ul className="list-disc list-inside space-y-1 mb-6">
                <li>Wahrnehmbarkeit</li>
                <li>Bedienbarkeit</li>
                <li>Verständlichkeit</li>
                <li>Robustheit</li>
              </ul>
              
              <AccessibleHeading level={3} className="mb-2">
                Tastaturnavigation
              </AccessibleHeading>
              
              <p>
                Verwenden Sie die Tab-Taste, um durch die interaktiven Elemente zu navigieren.
                Verwenden Sie die Eingabetaste oder die Leertaste, um Elemente zu aktivieren.
              </p>
            </div>
          </div>
        </div>
      </Section>
      
      {/* Dialog */}
      <AccessibleDialog
        isOpen={isDialogOpen}
        onClose={() => setIsDialogOpen(false)}
        title="Barrierefreier Dialog"
      >
        <div className="space-y-4">
          <p>
            Dies ist ein barrierefreier Dialog, der mit der Tastatur bedient werden kann.
            Drücken Sie die Escape-Taste, um den Dialog zu schließen.
          </p>
          
          <div className="flex justify-end">
            <Button onClick={() => setIsDialogOpen(false)}>
              Schließen
            </Button>
          </div>
        </div>
      </AccessibleDialog>
      
      {/* Alert */}
      <AccessibleAlert
        isOpen={isAlertOpen}
        onClose={() => setIsAlertOpen(false)}
        type={alertType}
        title={
          alertType === 'info'
            ? 'Information'
            : alertType === 'success'
            ? 'Erfolg'
            : alertType === 'warning'
            ? 'Warnung'
            : 'Fehler'
        }
        message={
          alertType === 'info'
            ? 'Dies ist eine Informations-Benachrichtigung.'
            : alertType === 'success'
            ? 'Das Formular wurde erfolgreich abgeschickt.'
            : alertType === 'warning'
            ? 'Dies ist eine Warnungs-Benachrichtigung.'
            : 'Dies ist eine Fehler-Benachrichtigung.'
        }
        autoClose
      />
    </main>
  )
}

export default function AccessibilityDemoPage() {
  return (
    <ClientOnly>
      <AccessibilityDemoContent />
    </ClientOnly>
  );
}
