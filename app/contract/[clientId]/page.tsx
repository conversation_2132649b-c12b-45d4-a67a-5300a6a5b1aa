"use client";

import { useEffect, useState, useRef } from "react";
import { useSearchParams } from "next/navigation";
import SignatureCanvas from "react-signature-canvas";
import {
  Check,
  Clock,
  Info,
  Lock,
  Star,
  CheckCircle,
  Shield,
  Award,
} from "lucide-react";

interface ProposalData {
  clientName: string;
  clientEmail: string;
  company?: string;
  phone?: string;
  selectedService: string;
  selectedPackage?: {
    id: string;
    name: string;
    price: number;
    originalPrice?: number;
    timeframe: string;
    description: string;
    features: string[];
    trustIndicators: string[];
  };
  estimatedBudget: string;
  projectTimeline: string;
  message: string;
  heardAbout?: string;
  changeRequests?: string;
  changeRequestDate?: string | null;
  status?: string;
  urlPassword?: string;
  signature1?: string;
  signature2?: string;
  signature3?: string;
  signatureDate?: string | null;
}

interface ContractPageProps {
  params: Promise<{
    clientId: string;
  }>;
}

// Helper function to get status display information
const getStatusInfo = (status: string) => {
  const statusMap: {
    [key: string]: { label: string; color: string; bgColor: string; icon: any };
  } = {
    "New Lead": {
      label: "Neuer Lead",
      color: "text-blue-400",
      bgColor: "bg-blue-500/20 border-blue-500/30",
      icon: <Info className="w-4 h-4" />,
    },
    Qualified: {
      label: "Qualifiziert",
      color: "text-yellow-400",
      bgColor: "bg-yellow-500/20 border-yellow-500/30",
      icon: <Clock className="w-4 h-4" />,
    },
    Won: {
      label: "Vertrag abgeschlossen",
      color: "text-green-400",
      bgColor: "bg-green-500/20 border-green-500/30",
      icon: <CheckCircle className="w-4 h-4" />,
    },
    Lost: {
      label: "Abgelehnt",
      color: "text-red-400",
      bgColor: "bg-red-500/20 border-red-500/30",
      icon: <Info className="w-4 h-4" />,
    },
    "On Hold": {
      label: "Wartend",
      color: "text-orange-400",
      bgColor: "bg-orange-500/20 border-orange-500/30",
      icon: <Clock className="w-4 h-4" />,
    },
    new: {
      label: "Neu",
      color: "text-blue-400",
      bgColor: "bg-blue-500/20 border-blue-500/30",
      icon: <Info className="w-4 h-4" />,
    },
    unsigned: {
      label: "Noch nicht signiert",
      color: "text-orange-400",
      bgColor: "bg-orange-500/20 border-orange-500/30",
      icon: <Clock className="w-4 h-4" />,
    },
  };

  return (
    statusMap[status] || {
      label: status || "Unbekannt",
      color: "text-gray-400",
      bgColor: "bg-gray-500/20 border-gray-500/30",
      icon: <Info className="w-4 h-4" />,
    }
  );
};

export default function ContractPage({ params }: ContractPageProps) {
  const [clientId, setClientId] = useState<string>("");
  const [proposalData, setProposalData] = useState<ProposalData | null>(null);
  const [passwordInput, setPasswordInput] = useState("");
  const [isPasswordVerified, setIsPasswordVerified] = useState(false);
  const [showPasswordPrompt, setShowPasswordPrompt] = useState(false);

  // Resolve params async
  useEffect(() => {
    const resolveParams = async () => {
      const resolvedParams = await params;
      setClientId(resolvedParams.clientId);
    };
    resolveParams();
  }, [params]);

  const searchParams = useSearchParams();
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [loading, setLoading] = useState(true);
  const [hasSignature, setHasSignature] = useState(false);
  const [agreements, setAgreements] = useState({
    agb: false,
    dsgvo: false,
    widerruf: false,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const signatureRef = useRef<SignatureCanvas>(null);
  const auth = searchParams?.get("auth");

  useEffect(() => {
    if (auth && auth.length === 32 && clientId) {
      setIsAuthorized(true);

      // Load proposal data
      const fetchProposalData = async () => {
        try {
          const response = await fetch(`/api/get-proposal-data/${clientId}`);
          if (response.ok) {
            const data = await response.json();
            setProposalData(data);

            // Check if password is required
            if (data.urlPassword && data.urlPassword.length > 0) {
              setShowPasswordPrompt(true);
            } else {
              setIsPasswordVerified(true);
            }

            // If contract is already concluded (status = Won), load existing signature
            if (data.status === "Won" && data.signature1) {
              setHasSignature(true);
              setAgreements({
                agb: true,
                dsgvo: true,
                widerruf: true,
              });
            }
          } else {
            console.error("Failed to load proposal data:", response.status);
          }
        } catch (error) {
          console.error("Error fetching proposal data:", error);
        } finally {
          setLoading(false);
        }
      };

      fetchProposalData();
    } else if (clientId) {
      setLoading(false);
    }
  }, [auth, clientId]);

  const handlePasswordSubmit = () => {
    if (passwordInput === proposalData?.urlPassword) {
      setIsPasswordVerified(true);
      setShowPasswordPrompt(false);
    } else {
      alert("Incorrect password. Please try again.");
    }
  };

  const handleAgreementChange = (key: keyof typeof agreements) => {
    if (proposalData?.status === "Won") return; // Don't allow changes if already concluded

    setAgreements((prev) => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  const isFormValid = () => {
    return (
      agreements.agb && agreements.dsgvo && agreements.widerruf && hasSignature
    );
  };

  const clearSignature = () => {
    if (proposalData?.status === "Won") return; // Don't allow changes if already concluded

    signatureRef.current?.clear();
    setHasSignature(false);
  };

  const handleSignatureEnd = () => {
    if (proposalData?.status === "Won") return; // Don't allow changes if already concluded

    setHasSignature(!signatureRef.current?.isEmpty());
  };

  const handleConcludeContract = async () => {
    if (!isFormValid() || proposalData?.status === "Won") return;

    setIsSubmitting(true);
    try {
      const signatureDataURL = signatureRef.current?.toDataURL();
      if (!signatureDataURL) {
        alert("Please provide a signature before concluding the contract.");
        setIsSubmitting(false);
        return;
      }

      const response = await fetch("/api/contract/conclude", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          clientId: clientId,
          signature: signatureDataURL,
          agreements: agreements,
        }),
      });

      if (response.ok) {
        setShowSuccessModal(true);
        // Update local state to reflect contract conclusion
        setProposalData((prev) => (prev ? { ...prev, status: "Won" } : null));
      } else {
        alert("Failed to conclude contract. Please try again.");
      }
    } catch (error) {
      console.error("Error concluding contract:", error);
      alert("An error occurred. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!isAuthorized) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="max-w-md w-full bg-gray-800 shadow-lg rounded-lg p-8 text-center">
          <Lock className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-white mb-4">
            Unauthorized Access
          </h1>
          <p className="text-gray-300 mb-6">
            You don't have permission to view this contract.
          </p>
          <p className="text-sm text-gray-400">
            Please contact support if you believe this is an error.
          </p>
        </div>
      </div>
    );
  }

  // Password verification prompt
  if (showPasswordPrompt && !isPasswordVerified) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="max-w-md w-full bg-gray-800 shadow-lg rounded-lg p-8">
          <Lock className="w-12 h-12 text-blue-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-white mb-4 text-center">
            Contract Access
          </h1>
          <p className="text-gray-300 mb-6 text-center">
            Please enter the password to access this contract.
          </p>
          <div className="space-y-4">
            <input
              type="password"
              value={passwordInput}
              onChange={(e) => setPasswordInput(e.target.value)}
              placeholder="Enter password"
              className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-blue-500"
              onKeyPress={(e) => e.key === "Enter" && handlePasswordSubmit()}
            />
            <button
              onClick={handlePasswordSubmit}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-lg font-semibold transition-colors"
            >
              Access Contract
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!isPasswordVerified) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const isContractConcluded = proposalData?.status === "Won";

  return (
    <div className="min-h-screen bg-gray-900 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Success Modal */}
        {showSuccessModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-gray-800 rounded-lg p-8 max-w-md w-full mx-4">
              <div className="text-center">
                <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
                <h2 className="text-2xl font-bold text-white mb-4">
                  Vertrag erfolgreich abgeschlossen!
                </h2>
                <p className="text-gray-300 mb-6">
                  Vielen Dank für Ihr Vertrauen. Wir werden uns in Kürze bei
                  Ihnen melden, um die nächsten Schritte zu besprechen.
                </p>
                <button
                  onClick={() => setShowSuccessModal(false)}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg"
                >
                  Schließen
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Header */}
        <div
          className={`border rounded-lg p-6 mb-6 ${
            isContractConcluded
              ? "bg-green-800/20 border-green-500"
              : "bg-gray-800 border-green-500"
          }`}
        >
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div
                className={
                  isContractConcluded ? "text-green-500" : "text-green-500"
                }
              >
                {isContractConcluded ? (
                  <CheckCircle className="w-6 h-6" />
                ) : (
                  "📋"
                )}
              </div>
              <h1 className="text-2xl font-bold text-white">
                {isContractConcluded
                  ? "Vertrag abgeschlossen"
                  : "Vertragsabschluss"}{" "}
                - {proposalData?.clientName || "Kunde"}
              </h1>
            </div>

            {/* Status Badge */}
            {proposalData?.status && (
              <div
                className={`px-4 py-2 rounded-full text-sm font-semibold border flex items-center gap-2 ${getStatusInfo(proposalData.status).bgColor} ${getStatusInfo(proposalData.status).color}`}
              >
                {getStatusInfo(proposalData.status).icon}
                {getStatusInfo(proposalData.status).label}
              </div>
            )}
          </div>

          <p
            className={
              isContractConcluded ? "text-green-400" : "text-green-400"
            }
          >
            {proposalData?.selectedPackage?.name && proposalData.clientEmail
              ? `${proposalData.selectedPackage.name} • ${proposalData.clientEmail}`
              : `Client ID: ${clientId}`}
            {isContractConcluded
              ? " • Vertrag wurde erfolgreich digital abgeschlossen"
              : " • Bitte überprüfen Sie alle Details und schließen Sie den Vertrag ab"}
          </p>

          {/* Additional Status Information */}
          <div className="flex flex-wrap gap-4 mt-4 text-sm">
            <div className="flex items-center gap-2 text-gray-300">
              <Info className="w-4 h-4" />
              <span>Client ID: {clientId}</span>
            </div>
            {proposalData?.urlPassword && (
              <div className="flex items-center gap-2 text-gray-300">
                <Lock className="w-4 h-4" />
                <span>Passwort geschützt</span>
              </div>
            )}
            {isContractConcluded && proposalData?.signatureDate && (
              <div className="flex items-center gap-2 text-green-300">
                <CheckCircle className="w-4 h-4" />
                <span>
                  Abgeschlossen am:{" "}
                  {new Date(proposalData.signatureDate).toLocaleDateString(
                    "de-DE"
                  )}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Customer Data */}
        <div className="bg-gray-800 rounded-lg p-6 mb-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="text-white">👤</div>
            <h2 className="text-xl font-semibold text-white">Kundendaten</h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="flex items-center gap-3">
              <div className="text-gray-400">👤</div>
              <div>
                <span className="text-gray-400">Name: </span>
                <span className="text-white">
                  {proposalData?.clientName || "Wird geladen..."}
                </span>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <div className="text-gray-400">✉️</div>
              <div>
                <span className="text-gray-400">Email: </span>
                <span className="text-white">
                  {proposalData?.clientEmail || "Wird geladen..."}
                </span>
              </div>
            </div>
            {proposalData?.company && (
              <div className="flex items-center gap-3">
                <div className="text-gray-400">🏢</div>
                <div>
                  <span className="text-gray-400">Unternehmen: </span>
                  <span className="text-white">{proposalData.company}</span>
                </div>
              </div>
            )}
            {proposalData?.phone && (
              <div className="flex items-center gap-3">
                <div className="text-gray-400">📞</div>
                <div>
                  <span className="text-gray-400">Telefon: </span>
                  <span className="text-white">{proposalData.phone}</span>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Selected Package - Enhanced Display */}
        <div className="bg-gray-800 rounded-lg p-6 mb-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="text-white">📦</div>
            <h2 className="text-xl font-semibold text-white">
              Ausgewähltes Paket
            </h2>
          </div>
          <div className="border border-blue-500 rounded-lg p-6 bg-gray-700">
            <div className="flex justify-between items-start mb-4">
              <div>
                <h3 className="text-2xl font-bold text-white mb-2">
                  {proposalData?.selectedPackage?.name ||
                    "Paket wird geladen..."}
                </h3>
                {proposalData?.selectedPackage?.originalPrice && (
                  <div className="flex items-center gap-3 mb-2">
                    <span className="text-lg text-gray-400 line-through">
                      €{" "}
                      {proposalData.selectedPackage.originalPrice.toLocaleString(
                        "de-DE"
                      )}
                    </span>
                    <span className="text-sm bg-green-500/20 text-green-400 px-2 py-1 rounded">
                      Gespart: €{" "}
                      {(
                        proposalData.selectedPackage.originalPrice -
                        proposalData.selectedPackage.price
                      ).toLocaleString("de-DE")}
                    </span>
                  </div>
                )}
              </div>
              <div className="text-right">
                <div className="text-3xl font-bold text-white">
                  €{" "}
                  {proposalData?.selectedPackage?.price?.toLocaleString(
                    "de-DE"
                  ) || "0,00"}
                </div>
                {proposalData?.selectedPackage?.timeframe && (
                  <div className="text-sm text-blue-300 mt-1 flex items-center gap-1">
                    <Clock className="w-4 h-4" />
                    {proposalData.selectedPackage.timeframe}
                  </div>
                )}
              </div>
            </div>

            <p className="text-gray-300 mb-6 leading-relaxed">
              {proposalData?.selectedPackage?.description ||
                proposalData?.message ||
                "Projektbeschreibung wird geladen..."}
            </p>

            {/* Trust Indicators */}
            {proposalData?.selectedPackage?.trustIndicators && (
              <div className="flex flex-wrap gap-2 mb-6">
                {proposalData.selectedPackage.trustIndicators.map(
                  (indicator: string, idx: number) => (
                    <span
                      key={idx}
                      className="px-3 py-1 bg-blue-500/20 text-blue-300 rounded-full text-sm font-medium border border-blue-500/30"
                    >
                      {indicator}
                    </span>
                  )
                )}
              </div>
            )}

            {/* Features List */}
            {proposalData?.selectedPackage?.features && (
              <div>
                <h4 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                  <Star className="w-5 h-5 text-yellow-400" />
                  Enthaltene Leistungen
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {proposalData.selectedPackage.features.map(
                    (feature: string, idx: number) => (
                      <div key={idx} className="flex items-start gap-3">
                        <div className="w-5 h-5 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center flex-shrink-0 mt-0.5">
                          <Check className="w-3 h-3 text-green-600 dark:text-green-400" />
                        </div>
                        <span className="text-gray-300 text-sm leading-relaxed">
                          {feature}
                        </span>
                      </div>
                    )
                  )}
                </div>
              </div>
            )}

            {proposalData?.projectTimeline && (
              <div className="mt-6 p-4 bg-gray-600 rounded-lg">
                <div className="text-sm text-blue-300">
                  <strong>Projektzeitrahmen:</strong>{" "}
                  {(() => {
                    const timelineMap: { [key: string]: string } = {
                      asap: "So schnell wie möglich",
                      "1_3_months": "1-3 Monate",
                      "3_6_months": "3-6 Monate",
                      "6_12_months": "6-12 Monate",
                      flexible: "Flexibel",
                    };
                    return (
                      timelineMap[proposalData.projectTimeline] ||
                      proposalData.projectTimeline
                    );
                  })()}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Total Price */}
        <div className="bg-blue-600 rounded-lg p-6 mb-6">
          <div className="flex justify-between items-center">
            <div>
              <h3 className="text-xl font-bold text-white">Gesamtpreis:</h3>
              <p className="text-blue-100">
                Einmaliger Preis, keine versteckten Kosten
              </p>
            </div>
            <div className="text-3xl font-bold text-white">
              {proposalData?.selectedPackage?.price
                ? `${proposalData.selectedPackage.price.toLocaleString("de-DE")} €`
                : "0,00 €"}
            </div>
          </div>
        </div>

        {/* Legal Documents */}
        <div className="bg-gray-800 rounded-lg p-6 mb-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="text-white">⬇️</div>
            <h2 className="text-xl font-semibold text-white">
              Rechtliche Dokumente zum Download
            </h2>
          </div>
          <p className="text-gray-400 mb-6">
            Laden Sie die rechtlichen Dokumente herunter und lesen Sie diese vor
            der Vertragsunterzeichnung:
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex justify-between items-center bg-gray-700 rounded-lg p-4">
              <span className="text-gray-300">AGB (Deutsch)</span>
              <button className="text-blue-400 hover:text-blue-300 flex items-center gap-2">
                <span>⬇️</span> Download
              </button>
            </div>
            <div className="flex justify-between items-center bg-gray-700 rounded-lg p-4">
              <span className="text-gray-300">Terms (English)</span>
              <button className="text-blue-400 hover:text-blue-300 flex items-center gap-2">
                <span>⬇️</span> Download
              </button>
            </div>
            <div className="flex justify-between items-center bg-gray-700 rounded-lg p-4">
              <span className="text-gray-300">DSGVO (Deutsch)</span>
              <button className="text-blue-400 hover:text-blue-300 flex items-center gap-2">
                <span>⬇️</span> Download
              </button>
            </div>
            <div className="flex justify-between items-center bg-gray-700 rounded-lg p-4">
              <span className="text-gray-300">Privacy (English)</span>
              <button className="text-blue-400 hover:text-blue-300 flex items-center gap-2">
                <span>⬇️</span> Download
              </button>
            </div>
            <div className="flex justify-between items-center bg-gray-700 rounded-lg p-4">
              <span className="text-gray-300">Widerruf (Deutsch)</span>
              <button className="text-blue-400 hover:text-blue-300 flex items-center gap-2">
                <span>⬇️</span> Download
              </button>
            </div>
            <div className="flex justify-between items-center bg-gray-700 rounded-lg p-4">
              <span className="text-gray-300">Revocation (English)</span>
              <button className="text-blue-400 hover:text-blue-300 flex items-center gap-2">
                <span>⬇️</span> Download
              </button>
            </div>
          </div>
        </div>

        {/* Legal Agreements */}
        <div className="bg-gray-800 rounded-lg p-6 mb-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="text-white">📋</div>
            <h2 className="text-xl font-semibold text-white">
              Rechtliche Vereinbarungen
            </h2>
          </div>
          <div className="space-y-4">
            <label
              className={`flex items-start gap-3 ${isContractConcluded ? "cursor-not-allowed opacity-60" : "cursor-pointer"}`}
            >
              <input
                type="checkbox"
                className="mt-1 rounded border-gray-600 bg-gray-700 text-blue-600"
                checked={agreements.agb}
                onChange={() => handleAgreementChange("agb")}
                disabled={isContractConcluded}
              />
              <span className="text-gray-300">
                Ich akzeptiere die{" "}
                <span className="text-blue-400">
                  Allgemeinen Geschäftsbedingungen (AGB)
                </span>{" "}
                und bestätige, dass ich diese gelesen und verstanden habe.
              </span>
            </label>
            <label
              className={`flex items-start gap-3 ${isContractConcluded ? "cursor-not-allowed opacity-60" : "cursor-pointer"}`}
            >
              <input
                type="checkbox"
                className="mt-1 rounded border-gray-600 bg-gray-700 text-blue-600"
                checked={agreements.dsgvo}
                onChange={() => handleAgreementChange("dsgvo")}
                disabled={isContractConcluded}
              />
              <span className="text-gray-300">
                Ich akzeptiere die{" "}
                <span className="text-blue-400">
                  Datenschutzerklärung (DSGVO)
                </span>{" "}
                und stimme der Verarbeitung meiner Daten zu.
              </span>
            </label>
            <label
              className={`flex items-start gap-3 ${isContractConcluded ? "cursor-not-allowed opacity-60" : "cursor-pointer"}`}
            >
              <input
                type="checkbox"
                className="mt-1 rounded border-gray-600 bg-gray-700 text-blue-600"
                checked={agreements.widerruf}
                onChange={() => handleAgreementChange("widerruf")}
                disabled={isContractConcluded}
              />
              <span className="text-gray-300">
                Ich habe die{" "}
                <span className="text-blue-400">Widerrufsbelehrung</span> zur
                Kenntnis genommen und verstanden.
              </span>
            </label>
          </div>
        </div>

        {/* Digital Signature */}
        <div className="bg-gray-800 rounded-lg p-6 mb-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="text-white">✍️</div>
            <h2 className="text-xl font-semibold text-white">
              Digitale Unterschrift
            </h2>
            {isContractConcluded && (
              <div className="flex items-center gap-2 text-green-400">
                <CheckCircle className="w-5 h-5" />
                <span className="text-sm">Vertrag unterzeichnet</span>
              </div>
            )}
          </div>
          <p className="text-gray-400 mb-4">
            {isContractConcluded
              ? "Dieser Vertrag wurde bereits digital unterzeichnet."
              : "Bitte unterschreiben Sie mit Ihrer Maus oder Ihrem Finger (auf Touch-Geräten) im untenstehenden Feld:"}
          </p>
          <div className="border-2 border-gray-600 rounded-lg p-4 bg-white">
            <SignatureCanvas
              ref={signatureRef}
              canvasProps={{
                className: `w-full h-48 border border-gray-300 rounded ${isContractConcluded ? "pointer-events-none" : ""}`,
                style: { width: "100%", height: "192px" },
              }}
              onEnd={handleSignatureEnd}
            />
            <div className="flex gap-4 mt-4">
              <button
                className={`px-4 py-2 rounded transition-colors ${
                  isContractConcluded
                    ? "bg-gray-500 text-gray-400 cursor-not-allowed"
                    : "bg-gray-600 hover:bg-gray-500 text-white"
                }`}
                onClick={clearSignature}
                disabled={isContractConcluded}
              >
                Unterschrift löschen
              </button>
              {hasSignature && (
                <span className="text-green-600 flex items-center gap-2 px-4 py-2">
                  <span>✓</span> Unterschrift gespeichert
                </span>
              )}
            </div>
          </div>
        </div>

        {/* Final Contract Button */}
        <div className="bg-gray-800 rounded-lg p-6">
          {isContractConcluded ? (
            <div className="text-center">
              <div className="w-full py-4 px-6 rounded-lg text-lg font-semibold bg-green-600 text-white flex items-center justify-center gap-3 mb-4">
                <CheckCircle className="w-6 h-6" />
                Vertrag erfolgreich abgeschlossen
              </div>
              <p className="text-green-400">
                Vielen Dank für Ihr Vertrauen! Wir werden uns in Kürze bei Ihnen
                melden.
              </p>
            </div>
          ) : (
            <>
              <button
                className={`w-full py-4 px-6 rounded-lg text-lg font-semibold transition-colors flex items-center justify-center gap-3 ${
                  isFormValid() && !isSubmitting
                    ? "bg-blue-600 hover:bg-blue-700 text-white"
                    : "bg-gray-600 text-gray-400 cursor-not-allowed"
                }`}
                disabled={!isFormValid() || isSubmitting}
                onClick={handleConcludeContract}
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                    Wird verarbeitet...
                  </>
                ) : (
                  <>
                    <span>✓</span> Vertrag digital abschließen
                  </>
                )}
              </button>
              <p className="text-gray-400 text-center mt-4">
                Mit dem Abschließen des Vertrags bestätigen Sie alle Angaben und
                gehen eine rechtsgültige Vereinbarung ein.
              </p>
            </>
          )}
        </div>
      </div>
    </div>
  );
}
