"use client";

import React from 'react'
import Script from "next/script";

type OrganizationProps = {
  name: string;
  url: string;
  logo: string;
  sameAs: string[];
  address: {
    streetAddress: string;
    addressLocality: string;
    addressRegion: string;
    postalCode: string;
    addressCountry: string;
  };
  description: string;
  email: string;
  telephone: string;
  foundingDate: string;
  founders: Array<{
    name: string;
    url?: string;
  }>;
  services: Array<{
    name: string;
    description: string;
    url: string;
  }>;
};

export default function OrganizationJsonLd({
  name = "Innovatio",
  url = "https://innovatio-pro.com",
  logo = "https://innovatio-pro.com/logo.png",
  sameAs = [
    "https://facebook.com/innovatio",
    "https://twitter.com/innovatio",
    "https://instagram.com/innovatio",
    "https://linkedin.com/company/innovatio",
  ],
  address = {
    streetAddress: "123 Innovation Street",
    addressLocality: "Tech City",
    addressRegion: "TC",
    postalCode: "12345",
    addressCountry: "US",
  },
  description = "Innovatio develops cutting-edge mobile and web applications that transform businesses through innovative technology solutions, optimized UX design, and AI integration.",
  email = "<EMAIL>",
  telephone = "******-456-7890",
  foundingDate = "2020-01-01",
  founders = [
    {
      name: "Viktor Hermann",
      url: "https://innovatio-pro.com/about",
    },
  ],
  services = [
    {
      name: "Mobile App Development",
      description:
        "Custom Flutter mobile applications with beautiful UI and powerful functionality",
      url: "https://innovatio-pro.com/services#mobile",
    },
    {
      name: "Web Development",
      description: "Modern, responsive Next.js websites and web applications",
      url: "https://innovatio-pro.com/services#web",
    },
    {
      name: "UI/UX Design",
      description: "User-centered design services for digital products",
      url: "https://innovatio-pro.com/services#design",
    },
    {
      name: "AI Integration",
      description:
        "Implement AI and machine learning solutions to enhance your products",
      url: "https://innovatio-pro.com/services#ai",
    },
  ],
}: Partial<OrganizationProps>) {
  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "Organization",
    name,
    url,
    logo: {
      "@type": "ImageObject",
      url: logo,
      width: "180",
      height: "180",
    },
    sameAs,
    description,
    email,
    foundingDate,
    founder: founders.map((founder) => ({
      "@type": "Person",
      name: founder.name,
      url: founder.url,
    })),
    address: {
      "@type": "PostalAddress",
      ...address,
    },
    contactPoint: {
      "@type": "ContactPoint",
      telephone,
      contactType: "customer service",
      email,
      availableLanguage: ["English", "German", "Russian", "Turkish", "Arabic"],
    },
    hasOfferCatalog: {
      "@type": "OfferCatalog",
      name: "Digital Services",
      itemListElement: services.map((service, index) => ({
        "@type": "Offer",
        itemOffered: {
          "@type": "Service",
          name: service.name,
          description: service.description,
          url: service.url,
        },
        position: index + 1,
      })),
    },
  };

  return (
    <Script
      id="organization-jsonld"
      type="application/ld+json"
      strategy="beforeInteractive"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
    />
  );
}
