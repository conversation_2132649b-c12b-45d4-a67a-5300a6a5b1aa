import { Metadata } from "next";
import Link from "next/link";
import {
  ArrowLeft,
  Shield,
  FileText,
  CheckCircle,
  Scale,
  Globe,
} from "lucide-react";

interface TermsAndConditionsProps {
  params: Promise<{ locale: string }>;
}

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  return {
    title: "Terms and Conditions | Innovatio-Pro",
    description:
      "Terms and Conditions for Innovatio-Pro services including mobile app development, web development, MVP development, and technical consulting.",
    keywords:
      "terms, conditions, legal, agreement, mobile app development, web development, consulting",
  };
}

export default async function TermsAndConditions({
  params,
}: TermsAndConditionsProps) {
  const { locale } = await params;

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-blue-50/30 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Header */}
      <div className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border-b border-gray-200 dark:border-gray-700 pt-20">
        <div className="max-w-4xl mx-auto px-4 py-8">
          <Link
            href={`/${locale}`}
            className="inline-flex items-center gap-3 text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 transition-all duration-300 mb-6 px-4 py-2 rounded-lg bg-blue-50 hover:bg-blue-100 dark:bg-blue-900/30 dark:hover:bg-blue-900/50 border border-blue-200 dark:border-blue-700"
          >
            <ArrowLeft className="w-5 h-5" />
            <span className="font-medium">Back to Home</span>
          </Link>

          <div className="flex items-center gap-4">
            <div className="p-3 bg-blue-100 dark:bg-blue-900/50 rounded-xl">
              <Scale className="w-8 h-8 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h1 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white">
                Terms and Conditions
              </h1>
              <p className="text-gray-600 dark:text-gray-300 mt-2">
                Innovatio-Pro Service Agreement
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 py-12">
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
          {/* Effective Date Banner */}
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-3">
              <FileText className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              <div>
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  Effective Date: April 5, 2025
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Last updated: April 5, 2025
                </p>
              </div>
            </div>
          </div>

          <div className="p-8 md:p-12">
            {/* Introduction */}
            <div className="prose prose-gray dark:prose-invert max-w-none">
              <p className="text-lg leading-relaxed text-gray-700 dark:text-gray-300 mb-8">
                These Terms and Conditions ("Terms") govern your use of
                Innovatio-Pro's services, including but not limited to mobile
                app development, web development, MVP development, technical
                consulting, and any related services (collectively, the
                "Services") provided by Viktor Hermann, doing business as
                Innovatio-Pro ("Innovatio-Pro," "we," "us," or "our"). By
                engaging Innovatio-Pro for any Services, you ("Client," "you,"
                or "your") agree to be bound by these Terms.
              </p>

              {/* Services Section */}
              <section className="mb-12">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 flex items-center gap-3">
                  <div className="p-2 bg-blue-100 dark:bg-blue-900/50 rounded-lg">
                    <CheckCircle className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  1. Services Offered
                </h2>
                <p className="mb-4 text-gray-700 dark:text-gray-300">
                  Innovatio-Pro provides the following Services:
                </p>

                <div className="grid gap-4 md:grid-cols-2 mb-6">
                  <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg border border-gray-200 dark:border-gray-600">
                    <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
                      MVP Development
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                      Development of a Minimum Viable Product based on Client
                      specifications.
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      <strong className="text-gray-900 dark:text-white">
                        Timeframe:
                      </strong>{" "}
                      3-4 weeks
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      <strong className="text-gray-900 dark:text-white">
                        Cost:
                      </strong>{" "}
                      €8,500
                    </p>
                  </div>

                  <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg border border-gray-200 dark:border-gray-600">
                    <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
                      Rapid Prototyping
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                      Creation of a functional prototype to demonstrate key
                      features and functionality.
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      <strong className="text-gray-900 dark:text-white">
                        Timeframe:
                      </strong>{" "}
                      1-2 weeks
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      <strong className="text-gray-900 dark:text-white">
                        Cost:
                      </strong>{" "}
                      €4,200
                    </p>
                  </div>
                </div>

                <ul className="space-y-2 mb-6">
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-green-500 dark:text-green-400 mt-1 flex-shrink-0" />
                    <span className="text-gray-700 dark:text-gray-300">
                      <strong className="text-gray-900 dark:text-white">
                        Cross-Platform Mobile App Development:
                      </strong>{" "}
                      Development of mobile applications for iOS and Android
                      platforms using Flutter.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-green-500 dark:text-green-400 mt-1 flex-shrink-0" />
                    <span className="text-gray-700 dark:text-gray-300">
                      <strong className="text-gray-900 dark:text-white">
                        Landing Page Development:
                      </strong>{" "}
                      Creation of web landing pages, adhering to WCAG 2.0
                      accessibility guidelines.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-green-500 dark:text-green-400 mt-1 flex-shrink-0" />
                    <span className="text-gray-700 dark:text-gray-300">
                      <strong className="text-gray-900 dark:text-white">
                        Technical Consulting:
                      </strong>{" "}
                      Provision of expert technical advice and guidance. Rate:
                      €110 per hour.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-green-500 dark:text-green-400 mt-1 flex-shrink-0" />
                    <span className="text-gray-700 dark:text-gray-300">
                      <strong className="text-gray-900 dark:text-white">
                        Full-Stack Development:
                      </strong>{" "}
                      Comprehensive development services encompassing front-end,
                      back-end, and database management.
                    </span>
                  </li>
                </ul>
              </section>

              {/* Agreement Formation */}
              <section className="mb-12">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  2. Agreement Formation
                </h2>
                <p className="text-gray-700 dark:text-gray-300">
                  A binding agreement between Innovatio-Pro and the Client is
                  formed upon the Client's acceptance of a written proposal or
                  statement of work (SOW) provided by Innovatio-Pro. The
                  proposal or SOW will outline the specific Services to be
                  provided, the project scope, the estimated timeline, and the
                  agreed-upon fees.
                </p>
              </section>

              {/* Project Scope */}
              <section className="mb-12">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  3. Project Scope and Changes
                </h2>
                <p className="text-gray-700 dark:text-gray-300">
                  The scope of the Services is defined in the accepted proposal
                  or SOW. Any changes to the scope of work requested by the
                  Client after the agreement is formed may result in additional
                  fees and adjustments to the project timeline. All change
                  requests must be submitted in writing and approved by both
                  Innovatio-Pro and the Client. Innovatio-Pro reserves the right
                  to decline change requests that are deemed technically
                  infeasible or outside the scope of our expertise.
                </p>
              </section>

              {/* Fees and Payment */}
              <section className="mb-12">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  4. Fees and Payment
                </h2>
                <ul className="space-y-3 text-gray-700 dark:text-gray-300">
                  <li>
                    <strong className="text-gray-900 dark:text-white">
                      Fees:
                    </strong>{" "}
                    The fees for the Services will be outlined in the accepted
                    proposal or SOW. Fees are quoted in Euros (€) unless
                    otherwise specified.
                  </li>
                  <li>
                    <strong className="text-gray-900 dark:text-white">
                      Payment Schedule:
                    </strong>{" "}
                    Payment terms will be detailed in the proposal or SOW.
                    Typically, payment schedules involve upfront deposits,
                    milestone payments, and final payments upon project
                    completion.
                  </li>
                  <li>
                    <strong className="text-gray-900 dark:text-white">
                      Late Payments:
                    </strong>{" "}
                    Late payments may be subject to interest charges at a rate
                    of 1.5% per month or the highest rate permitted by
                    applicable law, whichever is lower.
                  </li>
                  <li>
                    <strong className="text-gray-900 dark:text-white">
                      Expenses:
                    </strong>{" "}
                    The Client is responsible for reimbursing Innovatio-Pro for
                    any reasonable out-of-pocket expenses incurred in connection
                    with the provision of the Services, such as travel, software
                    licenses, or third-party services, provided such expenses
                    are pre-approved by the Client in writing.
                  </li>
                  <li>
                    <strong className="text-gray-900 dark:text-white">
                      Taxes:
                    </strong>{" "}
                    The Client is responsible for all applicable taxes, duties,
                    and levies associated with the Services.
                  </li>
                </ul>
              </section>

              {/* Client Responsibilities */}
              <section className="mb-12">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  5. Client Responsibilities
                </h2>
                <p className="mb-4 text-gray-700 dark:text-gray-300">
                  The Client is responsible for:
                </p>
                <ul className="space-y-2">
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-blue-500 dark:text-blue-400 mt-1 flex-shrink-0" />
                    <span className="text-gray-700 dark:text-gray-300">
                      Providing clear and accurate project requirements and
                      specifications.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-blue-500 dark:text-blue-400 mt-1 flex-shrink-0" />
                    <span className="text-gray-700 dark:text-gray-300">
                      Providing timely feedback and approvals.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-blue-500 dark:text-blue-400 mt-1 flex-shrink-0" />
                    <span className="text-gray-700 dark:text-gray-300">
                      Providing access to necessary resources and information.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-blue-500 dark:text-blue-400 mt-1 flex-shrink-0" />
                    <span className="text-gray-700 dark:text-gray-300">
                      Ensuring the Client has the necessary rights and licenses
                      to any content or materials provided to Innovatio-Pro for
                      use in the project.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-blue-500 dark:text-blue-400 mt-1 flex-shrink-0" />
                    <span className="text-gray-700 dark:text-gray-300">
                      Adhering to the agreed-upon payment schedule.
                    </span>
                  </li>
                </ul>
              </section>

              {/* Innovatio-Pro Responsibilities */}
              <section className="mb-12">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  6. Innovatio-Pro Responsibilities
                </h2>
                <p className="mb-4 text-gray-700 dark:text-gray-300">
                  Innovatio-Pro is responsible for:
                </p>
                <ul className="space-y-2">
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-green-500 dark:text-green-400 mt-1 flex-shrink-0" />
                    <span className="text-gray-700 dark:text-gray-300">
                      Providing the Services in a professional and timely
                      manner.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-green-500 dark:text-green-400 mt-1 flex-shrink-0" />
                    <span className="text-gray-700 dark:text-gray-300">
                      Maintaining open communication with the Client.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-green-500 dark:text-green-400 mt-1 flex-shrink-0" />
                    <span className="text-gray-700 dark:text-gray-300">
                      Adhering to industry best practices and standards.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-green-500 dark:text-green-400 mt-1 flex-shrink-0" />
                    <span className="text-gray-700 dark:text-gray-300">
                      Maintaining the confidentiality of Client information.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-green-500 dark:text-green-400 mt-1 flex-shrink-0" />
                    <span className="text-gray-700 dark:text-gray-300">
                      Providing a 100% implementation guarantee, meaning we will
                      deliver the agreed-upon functionality as described in the
                      accepted proposal or SOW.
                    </span>
                  </li>
                </ul>
              </section>

              {/* GDPR Section */}
              <section className="mb-12">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 flex items-center gap-3">
                  <div className="p-2 bg-blue-100 dark:bg-blue-900/50 rounded-lg">
                    <Shield className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  21. GDPR and Data Protection (EU Clients)
                </h2>
                <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-6">
                  <p className="mb-4 text-gray-700 dark:text-gray-300">
                    If you are a resident of the European Union or your business
                    is located in the EU, Innovatio-Pro processes personal data
                    according to the General Data Protection Regulation (GDPR).
                  </p>
                  <ul className="space-y-2">
                    <li className="flex items-start gap-2">
                      <CheckCircle className="w-4 h-4 text-blue-500 dark:text-blue-400 mt-1 flex-shrink-0" />
                      <span className="text-gray-700 dark:text-gray-300">
                        Data collected through our website, including but not
                        limited to emails, names, and company information
                        submitted via contact forms or pricing calculators, will
                        be stored securely as potential sales leads.
                      </span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="w-4 h-4 text-blue-500 dark:text-blue-400 mt-1 flex-shrink-0" />
                      <span className="text-gray-700 dark:text-gray-300">
                        Clients may request access, correction, or deletion of
                        their data at any time by contacting{" "}
                        <a
                          href="mailto:<EMAIL>"
                          className="text-blue-600 dark:text-blue-400 hover:underline"
                        >
                          <EMAIL>
                        </a>
                        .
                      </span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="w-4 h-4 text-blue-500 dark:text-blue-400 mt-1 flex-shrink-0" />
                      <span className="text-gray-700 dark:text-gray-300">
                        Innovatio-Pro is willing to sign a Data Processing
                        Agreement (DPA) upon request.
                      </span>
                    </li>
                  </ul>
                </div>
              </section>

              {/* Contact Information */}
              <section className="mb-8">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                  25. Contact Information
                </h2>
                <div className="bg-gray-50 dark:bg-gray-700/50 rounded-xl p-6 border border-gray-200 dark:border-gray-600">
                  <p className="mb-4 font-medium text-gray-700 dark:text-gray-300">
                    If you have any questions, contact:
                  </p>
                  <div className="space-y-2">
                    <p className="font-semibold text-lg text-gray-900 dark:text-white">
                      Innovatio-Pro
                    </p>
                    <p className="text-gray-700 dark:text-gray-300">
                      Viktor Hermann
                    </p>
                    <p className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                      📞{" "}
                      <a
                        href="tel:+491759918357"
                        className="text-blue-600 dark:text-blue-400 hover:underline"
                      >
                        +49 175 9918357
                      </a>
                    </p>
                    <p className="flex items-center gap-2 text-gray-700 dark:text-gray-300">
                      ✉️{" "}
                      <a
                        href="mailto:<EMAIL>"
                        className="text-blue-600 dark:text-blue-400 hover:underline"
                      >
                        <EMAIL>
                      </a>
                    </p>
                  </div>
                </div>
              </section>

              {/* Agreement Acknowledgment */}
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-700 rounded-xl p-6">
                <p className="text-center font-medium text-gray-900 dark:text-white">
                  <strong>
                    By engaging Innovatio-Pro for Services, you acknowledge that
                    you have read, understood, and agree to be bound by these
                    Terms and Conditions.
                  </strong>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
