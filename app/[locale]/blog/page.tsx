import { Suspense } from "react";
import { getDictionary } from "@/lib/dictionaries";
import { BlogHero } from "@/components/blog/BlogHero";
import { BlogGrid } from "@/components/blog/BlogGrid";
import { BlogSubscription } from "@/components/blog/BlogSubscription";
import { BlogLoading } from "@/components/blog/BlogLoading";

interface BlogPageProps {
  params: Promise<{ locale: string }>;
  searchParams?: Promise<{ category?: string; tag?: string; page?: string }>;
}

export default async function BlogPage({
  params,
  searchParams,
}: BlogPageProps) {
  const { locale } = await params;
  const searchParamsResolved = await searchParams;
  const dict = await getDictionary(locale);

  const currentCategory = searchParamsResolved?.category || "all";
  const currentPage = parseInt(searchParamsResolved?.page || "1");
  const currentTag = searchParamsResolved?.tag;

  return (
    <main className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Blog Hero Section */}
      <BlogHero dictionary={dict.blog} />

      {/* Newsletter Subscription - Full Width */}
      <BlogSubscription dictionary={dict.blog} />

      {/* Blog Content */}
      <div className="max-w-7xl mx-auto px-4 py-16">
        <div className="w-full">
          {/* Main Blog Grid - Full Width */}
          <Suspense fallback={<BlogLoading />}>
            <BlogGrid
              dictionary={dict.blog}
              category={currentCategory}
              page={currentPage}
              tag={currentTag}
              locale={locale}
            />
          </Suspense>
        </div>
      </div>
    </main>
  );
}

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const dict = await getDictionary(locale);

  return {
    title: `${dict.blog?.title || "Blog"} | Innovatio`,
    description:
      dict.blog?.description ||
      "Discover the latest insights in mobile development, Flutter, and technology innovation.",
    openGraph: {
      title: `${dict.blog?.title || "Blog"} | Innovatio`,
      description:
        dict.blog?.description ||
        "Discover the latest insights in mobile development, Flutter, and technology innovation.",
      type: "website",
    },
  };
}
