"use client";

import React from "react";
import Script from "next/script";

type FAQItem = {
  question: string;
  answer: string;
};

type FAQProps = {
  mainEntity?: FAQItem[];
};

export default function FAQJsonLd({
  mainEntity = [
    {
      question: "What services does Innovatio offer?",
      answer:
        "Innovatio specializes in Flutter mobile app development, Next.js web development, UI/UX design, and AI integration solutions for businesses across various industries.",
    },
    {
      question: "How much does a typical mobile app development project cost?",
      answer:
        "Mobile app development costs vary based on complexity, features, and scope. Basic apps start around $10,000, while more complex solutions may range from $30,000 to $100,000+. We provide detailed estimates after understanding your specific requirements.",
    },
    {
      question: "How long does it take to develop a custom web application?",
      answer:
        "Development timelines depend on project complexity. A simple website might take 4-6 weeks, while complex web applications can take 3-6 months. We provide timeline estimates during the initial consultation based on your specific needs.",
    },
    {
      question: "Do you provide ongoing maintenance and support after launch?",
      answer:
        "Yes, we offer comprehensive maintenance and support packages to ensure your digital products continue to function optimally. This includes bug fixes, security updates, feature enhancements, and technical support.",
    },
    {
      question: "What technologies do you use for web development?",
      answer:
        "We primarily use Next.js, React, and TypeScript for frontend development, along with modern backend technologies. Our tech stack is chosen based on each project's specific requirements and performance needs.",
    },
    {
      question: "Can you help with redesigning an existing application?",
      answer:
        "Absolutely! We specialize in redesigning and modernizing existing applications to improve user experience, performance, and functionality while maintaining brand consistency.",
    },
    {
      question: "Do you provide UI/UX design services separately?",
      answer:
        "Yes, we offer standalone UI/UX design services including user research, wireframing, prototyping, and visual design. These services can be engaged independently of development.",
    },
  ],
}: FAQProps) {
  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    mainEntity: mainEntity.map((item) => ({
      "@type": "Question",
      name: item.question,
      acceptedAnswer: {
        "@type": "Answer",
        text: item.answer,
      },
    })),
  };

  return (
    <Script
      id="faq-jsonld"
      type="application/ld+json"
      strategy="beforeInteractive"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
    />
  );
}
