'use client'

import { useEffect } from 'react'
import Link from 'next/link'

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Unhandled error:', error)
  }, [error])

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gradient-to-b from-[#162760] to-[#11142b] text-white p-4">
      <div className="max-w-md w-full text-center">
        <h1 className="text-4xl font-bold mb-4">Something went wrong</h1>
        <p className="mb-8 text-gray-300">
          We apologize for the inconvenience. Please try again later.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <button
            onClick={() => reset()}
            className="px-6 py-3 bg-blue-600 hover:bg-blue-700 rounded-md text-white font-medium transition-colors"
          >
            Try again
          </button>
          <Link 
            href="/"
            className="px-6 py-3 bg-gray-700 hover:bg-gray-800 rounded-md text-white font-medium transition-colors"
          >
            Return to Home
          </Link>
        </div>
      </div>
    </div>
  )
}
