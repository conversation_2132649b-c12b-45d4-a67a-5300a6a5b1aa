// Critical components (loaded immediately)
import { HeroSection } from "@/components/sections/HeroSection";
import { getDictionary } from "@/lib/dictionaries";
import { CriticalResourcePreloader } from "@/components/performance/CriticalResourcePreloader";
import { preloadCriticalImages } from "@/components/ui/MobileOptimizedImage";

// Direct imports for all components temporarily to isolate webpack issue
import { TrustedBySection } from "@/components/sections/TrustedBySection";
import { SolutionsPortfolioSection } from "@/components/sections/SolutionsPortfolioSection";
import { ServiceSection } from "@/components/sections/ServiceSection";
import { AboutSection } from "@/components/sections/AboutSection";
import { FooterSection } from "@/components/sections/FooterSection";
import { TestimonialsSection } from "@/components/sections/TestimonialsSection";
import { ContactSection } from "@/components/sections/ContactSection";
import PricingSectionWrapper from "@/components/pricing/PricingSectionWrapper";

// Client-side wrapper for dynamic components
import { DynamicComponentWrapper } from "@/components/ui/DynamicComponentWrapper";

interface HomeProps {
  params: Promise<{ locale: string }>;
}

export default async function Home({ params }: HomeProps) {
  const { locale } = await params;
  // Get the dictionary for the current locale
  const dict = await getDictionary(locale);

  // Critical hero images for preloading (LCP optimization)
  const heroImages = [
    "/images/hero/togg.png",
    "/images/hero/aigo.png",
    "/images/hero/menu.png",
    "/images/hero/detoxme.png",
    "/images/hero/reserv.png",
  ];

  // Preload critical images for better LCP
  if (typeof window !== "undefined") {
    preloadCriticalImages(heroImages);
  }

  // Use the actual products data
  const products = [
    {
      title: "TOGG App",
      link: "#",
      thumbnail: "/images/mockups/app_togg.png",
    },
    {
      title: "Spotz App 3",
      link: "#",
      thumbnail: "/images/mockups/app_spotz3.png",
    },
    {
      title: "Spotz App 2",
      link: "#",
      thumbnail: "/images/mockups/app_spotz2.png",
    },
    {
      title: "Spotz App",
      link: "#",
      thumbnail: "/images/mockups/app_spotz.png",
    },
    {
      title: "Spotz App 1",
      link: "#",
      thumbnail: "/images/mockups/app_spotz_1.png",
    },
    {
      title: "Room App",
      link: "#",
      thumbnail: "/images/mockups/app_room.png",
    },
    {
      title: "Plan App",
      link: "#",
      thumbnail: "/images/mockups/app_plan.png",
    },
    {
      title: "Medical App",
      link: "#",
      thumbnail: "/images/mockups/app_medical.png",
    },
    {
      title: "HostIQ App",
      link: "#",
      thumbnail: "/images/mockups/app_hostiq_1.png",
    },
    {
      title: "Food App",
      link: "#",
      thumbnail: "/images/mockups/app_food.png",
    },
    {
      title: "TOGG App 2",
      link: "#",
      thumbnail: "/images/mockups/app_togg.png",
    },
    {
      title: "Spotz App 4",
      link: "#",
      thumbnail: "/images/mockups/app_spotz3.png",
    },
    {
      title: "Room App 2",
      link: "#",
      thumbnail: "/images/mockups/app_room.png",
    },
    {
      title: "Plan App 2",
      link: "#",
      thumbnail: "/images/mockups/app_plan.png",
    },
    {
      title: "Medical App 2",
      link: "#",
      thumbnail: "/images/mockups/app_medical.png",
    },
  ];

  return (
    <>
      {/* Critical resource preloader for LCP/TBT optimization */}
      <CriticalResourcePreloader
        heroImages={heroImages}
        enablePreloading={true}
      />

      <main
        className={`flex min-h-screen flex-col items-center w-full overflow-x-hidden justify-between ${
          locale === "ar" ? "rtl" : ""
        }`}
      >
        {/* Critical above-the-fold content */}
        <HeroSection dictionary={dict.hero} />

        {/* Load all components directly to test webpack issue */}
        <TrustedBySection dictionary={dict.hero.trustedBy} />
        <SolutionsPortfolioSection
          dictionary={dict.solutionsPortfolio}
          products={products}
        />
        <ServiceSection dictionary={dict.serviceSection} />
        <AboutSection
          dictionary={dict.about}
          clientsDictionary={dict.clients}
        />
        <PricingSectionWrapper dictionary={dict.prices} locale={locale} />
        <TestimonialsSection dictionary={dict.testimonials} />
        <ContactSection dictionary={dict.contact} />

        {/* Dynamic components with client-side rendering */}
        <DynamicComponentWrapper dictionary={dict.contact} locale={locale} />

        {/* Footer */}
        <FooterSection dictionary={dict.footer} locale={locale} />
      </main>
    </>
  );
}
