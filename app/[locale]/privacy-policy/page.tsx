import { getDictionary } from '@/lib/dictionaries'
import Link from 'next/link'
import { ChevronLeft, Shield, Eye, Lock, Globe, FileText, Users, Database, Clock, Scale, Phone, Mail, MapPin } from 'lucide-react'

interface PrivacyPolicyProps {
  params: Promise<{ locale: string }>
}

export async function generateMetadata({ params }: PrivacyPolicyProps) {
  const { locale } = await params
  
  return {
    title: 'Privacy Policy - Innovatio-Pro',
    description: 'Learn how Innovatio-Pro collects, uses, and protects your personal data in compliance with GDPR and CCPA regulations.',
    alternates: {
      canonical: `/${locale}/privacy-policy`,
      languages: {
        'en': '/en/privacy-policy',
        'de': '/de/privacy-policy',
        'ar': '/ar/privacy-policy',
        'ru': '/ru/privacy-policy',
        'tr': '/tr/privacy-policy',
      }
    }
  }
}

export default async function PrivacyPolicyPage({ params }: PrivacyPolicyProps) {
  const { locale } = await params

  const sections = [
    {
      id: 'introduction',
      title: '1. Introduction',
      icon: <Shield className="w-5 h-5" />,
      content: (
        <div className="space-y-4">
          <p>
            Innovatio-Pro ("we," "us," or "our") is committed to protecting the privacy of your personal data. 
            This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you 
            visit our website innovatio-pro.com (the "Site") and use our services. This policy complies with 
            the General Data Protection Regulation (GDPR) and the California Consumer Privacy Act (CCPA), as applicable.
          </p>
        </div>
      )
    },
    {
      id: 'data-controller',
      title: '2. Data Controller',
      icon: <Users className="w-5 h-5" />,
      content: (
        <div className="space-y-4">
          <p>The data controller responsible for the processing of your personal data is:</p>
          <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border-l-4 border-blue-500">
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <strong>Innovatio-Pro</strong>
              </div>
              <div className="flex items-center gap-2 text-gray-600 dark:text-gray-300">
                <MapPin className="w-4 h-4" />
                <span>Wyoming, USA</span>
              </div>
              <div className="flex items-center gap-2 text-gray-600 dark:text-gray-300">
                <Phone className="w-4 h-4" />
                <span>+49 175 9918357</span>
              </div>
              <div className="flex items-center gap-2 text-gray-600 dark:text-gray-300">
                <Mail className="w-4 h-4" />
                <span><EMAIL></span>
              </div>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'information-collection',
      title: '3. Information We Collect',
      icon: <Database className="w-5 h-5" />,
      content: (
        <div className="space-y-6">
          <p>We collect the following types of information:</p>
          
          <div className="space-y-4">
            <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
              <h4 className="font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                <FileText className="w-4 h-4 text-blue-500" />
                Personal Data Collected Through Forms:
              </h4>
              <ul className="list-disc list-inside space-y-1 text-gray-700 dark:text-gray-300 ml-6">
                <li><strong>Contact Form:</strong> Name, Email Address, Subject, Message</li>
                <li><strong>Newsletter Subscription:</strong> Email Address</li>
                <li><strong>Price Calculator (Lead Capture):</strong> Name, Email Address, Company Name, Agreement to Terms and Conditions</li>
                <li><strong>Calculators:</strong> Project Details, Technical Requirements, Budget Information</li>
              </ul>
            </div>

            <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
              <h4 className="font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                <Eye className="w-4 h-4 text-green-500" />
                Technical Data:
              </h4>
              <ul className="list-disc list-inside space-y-1 text-gray-700 dark:text-gray-300 ml-6">
                <li><strong>Cookies:</strong> Necessary Cookies, Analytics Cookies, Marketing Cookies, Functional Cookies</li>
                <li><strong>Browser Information:</strong> User Agent, Screen Resolution, Language</li>
                <li><strong>Usage Behavior:</strong> Page Views, Click Behavior, Time Spent on Pages</li>
                <li><strong>Device Information:</strong> Operating System, Browser Version</li>
              </ul>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'data-purposes',
      title: '4. Purposes of Data Collection and Legal Basis for Processing',
      icon: <Scale className="w-5 h-5" />,
      content: (
        <div className="space-y-6">
          <p>We collect and process your personal data for the following purposes, based on the following legal bases:</p>
          
          <div className="space-y-4">
            <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
              <h4 className="font-semibold text-gray-900 dark:text-white mb-3">Business Communication:</h4>
              <ul className="space-y-2 text-gray-700 dark:text-gray-300">
                <li><strong>Processing Customer Inquiries via Contact Form:</strong> <em>Legal Basis:</em> Legitimate Interest (responding to inquiries and providing customer service) and/or Contractual Necessity</li>
                <li><strong>Providing Personalized Quotations:</strong> <em>Legal Basis:</em> Legitimate Interest (providing tailored services) and/or Contractual Necessity</li>
                <li><strong>Scheduling Appointments for Consultations:</strong> <em>Legal Basis:</em> Legitimate Interest (managing appointments) and/or Contractual Necessity</li>
                <li><strong>Sending Newsletters with Tech Insights:</strong> <em>Legal Basis:</em> Consent (where required by law, such as GDPR) or Legitimate Interest</li>
              </ul>
            </div>

            <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
              <h4 className="font-semibold text-gray-900 dark:text-white mb-3">Website Optimization:</h4>
              <ul className="space-y-2 text-gray-700 dark:text-gray-300">
                <li><strong>Improving User Experience:</strong> <em>Legal Basis:</em> Legitimate Interest (improving our website and services)</li>
                <li><strong>Performance Analysis and Optimization of Loading Times:</strong> <em>Legal Basis:</em> Legitimate Interest (ensuring website performance and security)</li>
                <li><strong>A/B Testing for Improved Conversion Rates:</strong> <em>Legal Basis:</em> Legitimate Interest (optimizing marketing and website effectiveness)</li>
              </ul>
            </div>

            <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
              <h4 className="font-semibold text-gray-900 dark:text-white mb-3">Marketing and Analytics:</h4>
              <ul className="space-y-2 text-gray-700 dark:text-gray-300">
                <li><strong>Understanding User Behavior:</strong> <em>Legal Basis:</em> Consent (for Marketing Cookies) and Legitimate Interest (for aggregated, anonymized analytics)</li>
                <li><strong>Personalized Content and Advertising:</strong> <em>Legal Basis:</em> Consent (explicit consent for personalized advertising)</li>
                <li><strong>Conversion Tracking and ROI Measurement:</strong> <em>Legal Basis:</em> Consent (for Marketing Cookies) and Legitimate Interest</li>
              </ul>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'third-party-services',
      title: '5. Third-Party Services',
      icon: <Globe className="w-5 h-5" />,
      content: (
        <div className="space-y-6">
          <p>We use the following third-party services, which may also collect and process your personal data:</p>
          
          <div className="grid gap-4 md:grid-cols-2">
            <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
              <h4 className="font-semibold text-gray-900 dark:text-white mb-3">Analytics and Tracking:</h4>
              <ul className="space-y-2 text-sm text-gray-700 dark:text-gray-300">
                <li><strong>Google Analytics:</strong> Comprehensive website tracking</li>
                <li><strong>Vercel Analytics:</strong> Performance metrics and usage statistics</li>
                <li><strong>Vercel Speed Insights:</strong> Website speed measurement</li>
              </ul>
            </div>

            <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
              <h4 className="font-semibold text-gray-900 dark:text-white mb-3">External Booking Systems:</h4>
              <ul className="space-y-2 text-sm text-gray-700 dark:text-gray-300">
                <li><strong>Calendly:</strong> Appointment booking for consultations (calendly.com/v-hermann-it)</li>
              </ul>
            </div>

            <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
              <h4 className="font-semibold text-gray-900 dark:text-white mb-3">Email Services:</h4>
              <ul className="space-y-2 text-sm text-gray-700 dark:text-gray-300">
                <li><strong>Google Workspace SMTP:</strong> Contact form emails</li>
                <li><strong>Resend API:</strong> Newsletter distribution and backup email service</li>
                <li><strong>Nodemailer:</strong> Email processing with multiple SMTP servers</li>
              </ul>
            </div>

            <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
              <h4 className="font-semibold text-gray-900 dark:text-white mb-3">Communication:</h4>
              <ul className="space-y-2 text-sm text-gray-700 dark:text-gray-300">
                <li><strong>WhatsApp:</strong> Direct communication via phone number</li>
                <li><strong>LinkedIn/GitHub:</strong> External profile links</li>
              </ul>
            </div>
          </div>

          <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg border-l-4 border-yellow-500">
            <p className="text-sm">
              We encourage you to review the privacy policies of these third-party services to understand 
              how they collect, use, and share your information.
            </p>
          </div>
        </div>
      )
    },
    {
      id: 'data-retention',
      title: '6. Data Retention',
      icon: <Clock className="w-5 h-5" />,
      content: (
        <div className="space-y-4">
          <p>
            We will retain your personal data only for as long as necessary to fulfill the purposes for which 
            it was collected, including for the purposes of satisfying any legal, accounting, or reporting requirements.
          </p>
          
          <div className="grid gap-4 md:grid-cols-2">
            <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
              <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Contact Form Data:</h4>
              <p className="text-sm text-gray-700 dark:text-gray-300">
                We retain contact form data for 12 months to manage inquiries and maintain communication records.
              </p>
            </div>

            <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
              <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Newsletter Subscription Data:</h4>
              <p className="text-sm text-gray-700 dark:text-gray-300">
                We retain your email address for as long as you remain subscribed. You can unsubscribe at any time.
              </p>
            </div>

            <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
              <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Price Calculator Data:</h4>
              <p className="text-sm text-gray-700 dark:text-gray-300">
                We retain price calculator data for 24 months for lead management and sales follow-up.
              </p>
            </div>

            <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
              <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Analytics Data:</h4>
              <p className="text-sm text-gray-700 dark:text-gray-300">
                Data collected by analytics services is subject to their retention policies. We configure these services for the shortest period necessary.
              </p>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'data-security',
      title: '7. Data Security',
      icon: <Lock className="w-5 h-5" />,
      content: (
        <div className="space-y-4">
          <p>
            We have implemented appropriate technical and organizational measures to protect your personal data 
            against unauthorized access, use, disclosure, alteration, or destruction. These measures include:
          </p>
          
          <div className="grid gap-3 md:grid-cols-2">
            <div className="flex items-center gap-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <Shield className="w-5 h-5 text-green-600" />
              <span className="text-sm font-medium">Encryption of data in transit and at rest</span>
            </div>
            <div className="flex items-center gap-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <Lock className="w-5 h-5 text-green-600" />
              <span className="text-sm font-medium">Secure server infrastructure</span>
            </div>
            <div className="flex items-center gap-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <Eye className="w-5 h-5 text-green-600" />
              <span className="text-sm font-medium">Regular security assessments and audits</span>
            </div>
            <div className="flex items-center gap-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <Users className="w-5 h-5 text-green-600" />
              <span className="text-sm font-medium">Access controls to limit data access</span>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'gdpr-rights',
      title: '8. Your Rights Under GDPR',
      icon: <Scale className="w-5 h-5" />,
      content: (
        <div className="space-y-4">
          <p>
            If you are a resident of the European Economic Area (EEA), you have the following rights under the GDPR:
          </p>
          
          <div className="space-y-3">
            {[
              { title: 'Right to Access', desc: 'You have the right to request access to your personal data that we hold.' },
              { title: 'Right to Rectification', desc: 'You have the right to request that we correct any inaccurate or incomplete personal data.' },
              { title: 'Right to Erasure ("Right to be Forgotten")', desc: 'You have the right to request that we erase your personal data under certain circumstances.' },
              { title: 'Right to Restriction of Processing', desc: 'You have the right to request that we restrict the processing of your personal data under certain circumstances.' },
              { title: 'Right to Data Portability', desc: 'You have the right to receive your personal data in a structured, commonly used, and machine-readable format.' },
              { title: 'Right to Object', desc: 'You have the right to object to the processing of your personal data under certain circumstances.' },
              { title: 'Right to Withdraw Consent', desc: 'If we are processing your personal data based on your consent, you have the right to withdraw your consent at any time.' },
              { title: 'Right to Lodge a Complaint', desc: 'You have the right to lodge a complaint with a supervisory authority if you believe that we have infringed your rights under the GDPR.' }
            ].map((right, index) => (
              <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <h4 className="font-semibold text-gray-900 dark:text-white mb-2">{right.title}</h4>
                <p className="text-sm text-gray-700 dark:text-gray-300">{right.desc}</p>
              </div>
            ))}
          </div>

          <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border-l-4 border-blue-500">
            <p className="text-sm">
              To exercise any of these rights, please contact <NAME_EMAIL>.
            </p>
          </div>
        </div>
      )
    },
    {
      id: 'ccpa-rights',
      title: '9. Your Rights Under CCPA',
      icon: <Scale className="w-5 h-5" />,
      content: (
        <div className="space-y-4">
          <p>If you are a California resident, you have the following rights under the CCPA:</p>
          
          <div className="space-y-3">
            {[
              { title: 'Right to Know', desc: 'You have the right to request information about the categories and specific pieces of personal information we have collected about you.' },
              { title: 'Right to Delete', desc: 'You have the right to request that we delete your personal information, subject to certain exceptions.' },
              { title: 'Right to Opt-Out of Sale', desc: 'We do not sell your personal information.' },
              { title: 'Right to Non-Discrimination', desc: 'We will not discriminate against you for exercising your CCPA rights.' }
            ].map((right, index) => (
              <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <h4 className="font-semibold text-gray-900 dark:text-white mb-2">{right.title}</h4>
                <p className="text-sm text-gray-700 dark:text-gray-300">{right.desc}</p>
              </div>
            ))}
          </div>

          <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border-l-4 border-blue-500">
            <p className="text-sm">
              To exercise your CCPA rights, please contact <NAME_EMAIL>. We will require 
              verification of your identity before processing your request.
            </p>
          </div>
        </div>
      )
    },
    {
      id: 'cookie-policy',
      title: '10. Cookie Policy',
      icon: <Database className="w-5 h-5" />,
      content: (
        <div className="space-y-4">
          <p>
            We use cookies and similar technologies to collect information about your browsing activity on our Site. 
            Cookies are small text files that are stored on your device when you visit a website.
          </p>
          
          <div className="space-y-3">
            {[
              { title: 'Necessary Cookies', desc: 'These cookies are essential for the operation of our Site and enable you to use its features. They cannot be disabled.', color: 'red' },
              { title: 'Analytics Cookies', desc: 'These cookies allow us to analyze how visitors use our Site and to improve its performance. We use Google Analytics, Vercel Analytics, and Vercel Speed Insights for this purpose.', color: 'blue' },
              { title: 'Marketing Cookies', desc: 'These cookies are used to track your browsing activity across different websites and to show you personalized advertisements. We will only use these cookies with your explicit consent.', color: 'purple' },
              { title: 'Functional Cookies', desc: 'These cookies allow us to remember your preferences and to provide you with enhanced functionality.', color: 'green' }
            ].map((cookie, index) => (
              <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <h4 className={`font-semibold mb-2 text-${cookie.color}-600 dark:text-${cookie.color}-400`}>{cookie.title}</h4>
                <p className="text-sm text-gray-700 dark:text-gray-300">{cookie.desc}</p>
              </div>
            ))}
          </div>

          <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg border-l-4 border-yellow-500">
            <p className="text-sm">
              You can control cookies through your browser settings. However, please note that disabling cookies 
              may affect the functionality of our Site.
            </p>
          </div>
        </div>
      )
    },
    {
      id: 'data-transfers',
      title: '11. Data Transfers',
      icon: <Globe className="w-5 h-5" />,
      content: (
        <div className="space-y-4">
          <p>
            Your personal data may be transferred to and processed in countries outside of your country of residence, 
            including countries that may not have data protection laws that are equivalent to those in your country. 
            We will take appropriate safeguards to ensure that your personal data is protected in accordance with 
            this Privacy Policy and applicable law.
          </p>
          <p>
            These safeguards may include using standard contractual clauses approved by the European Commission 
            or relying on other legally recognized transfer mechanisms.
          </p>
        </div>
      )
    },
    {
      id: 'childrens-privacy',
      title: '12. Children\'s Privacy',
      icon: <Shield className="w-5 h-5" />,
      content: (
        <div className="space-y-4">
          <p>
            Our Site is not intended for children under the age of 16. We do not knowingly collect personal data 
            from children under the age of 16.
          </p>
          <p>
            If you are a parent or guardian and believe that your child has provided us with personal data, 
            please contact <NAME_EMAIL>.
          </p>
        </div>
      )
    },
    {
      id: 'policy-changes',
      title: '13. Changes to this Privacy Policy',
      icon: <FileText className="w-5 h-5" />,
      content: (
        <div className="space-y-4">
          <p>
            We may update this Privacy Policy from time to time. We will post any changes on our Site and update 
            the "Effective Date" at the top of this policy.
          </p>
          <p>
            We encourage you to review this Privacy Policy periodically. Your continued use of our Site after 
            any changes to this Privacy Policy constitutes your acceptance of the revised policy.
          </p>
        </div>
      )
    },
    {
      id: 'contact-info',
      title: '14. Contact Us',
      icon: <Mail className="w-5 h-5" />,
      content: (
        <div className="space-y-4">
          <p>If you have any questions or concerns about this Privacy Policy, please contact us at:</p>
          <div className="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg">
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <Users className="w-5 h-5 text-blue-500" />
                <span className="font-semibold">Innovatio-Pro</span>
              </div>
              <div className="flex items-center gap-3 text-gray-600 dark:text-gray-300">
                <MapPin className="w-5 h-5 text-gray-500" />
                <span>Wyoming, USA</span>
              </div>
              <div className="flex items-center gap-3 text-gray-600 dark:text-gray-300">
                <Phone className="w-5 h-5 text-gray-500" />
                <span>+49 175 9918357</span>
              </div>
              <div className="flex items-center gap-3 text-gray-600 dark:text-gray-300">
                <Mail className="w-5 h-5 text-gray-500" />
                <a href="mailto:<EMAIL>" className="text-blue-600 dark:text-blue-400 hover:underline">
                  <EMAIL>
                </a>
              </div>
            </div>
          </div>
        </div>
      )
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-blue-50/30 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Header Section */}
      <div className="pt-20 pb-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto">
          {/* Back Button */}
          <Link 
            href={`/${locale}`}
            className="inline-flex items-center gap-2 text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors mb-8 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl px-4 py-2 shadow-sm border border-white/20 dark:border-gray-700/30 hover:shadow-md"
          >
            <ChevronLeft className="w-5 h-5" />
            <span className="font-medium">Back to Home</span>
          </Link>

          {/* Header Content */}
          <div className="text-center space-y-6">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl shadow-lg">
              <Shield className="w-8 h-8 text-white" />
            </div>
            
            <div className="space-y-4">
              <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 dark:from-white dark:via-blue-200 dark:to-purple-200 bg-clip-text text-transparent">
                Privacy Policy
              </h1>
              <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                Your privacy is our priority. Learn how we collect, use, and protect your personal data.
              </p>
            </div>

            {/* Effective Date Card */}
            <div className="inline-flex flex-col sm:flex-row items-center gap-4 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 dark:border-gray-700/30">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
                  <Clock className="w-5 h-5 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    Effective Date: April 5, 2025
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    Last updated: April 5, 2025
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content Section */}
      <div className="pb-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto">
          <div className="space-y-12">
            {sections.map((section, index) => (
              <div 
                key={section.id}
                className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-6 md:p-8 shadow-lg border border-white/20 dark:border-gray-700/30 hover:shadow-xl transition-all duration-300"
              >
                {/* Section Header */}
                <div className="flex items-start gap-4 mb-6">
                  <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center text-white shadow-lg flex-shrink-0">
                    {section.icon}
                  </div>
                  <div className="flex-1">
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                      {section.title}
                    </h2>
                  </div>
                </div>

                {/* Section Content */}
                <div className="text-gray-700 dark:text-gray-300 leading-relaxed pl-14">
                  {section.content}
                </div>
              </div>
            ))}
          </div>

          {/* Important Notice */}
          <div className="mt-12 bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 border-l-4 border-yellow-500 rounded-lg p-6">
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="text-white text-sm font-bold">!</span>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Important Legal Notice</h3>
                <div className="space-y-2 text-sm text-gray-700 dark:text-gray-300">
                  <p>
                    This Privacy Policy has been drafted to comply with GDPR and CCPA regulations. 
                    However, it is recommended to have this reviewed by qualified legal counsel to ensure 
                    full compliance with all applicable laws and regulations in your specific jurisdiction.
                  </p>
                  <p>
                    Laws and interpretations may change, and specific jurisdictional requirements may apply. 
                    We regularly review and update our privacy practices to maintain the highest standards 
                    of data protection.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
