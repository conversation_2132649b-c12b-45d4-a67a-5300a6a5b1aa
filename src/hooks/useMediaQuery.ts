'use client'

import { useState, useEffect } from 'react'

/**
 * Hook für die Verwendung von Medienabfragen (Media Queries)
 * 
 * @param query Die Medienabfrage als String (z.B. '(min-width: 768px)')
 * @returns Boolean, der angibt, ob die Medienabfrage zutrifft
 */
export function useMediaQuery(query: string): boolean {
  // Initialisiere mit false, um serverseitiges Rendering zu unterstützen
  const [matches, setMatches] = useState(false)
  
  useEffect(() => {
    // Prüfe, ob window verfügbar ist (Client-Side)
    if (typeof window !== 'undefined') {
      const mediaQuery = window.matchMedia(query)
      
      // Setze den initialen Wert
      setMatches(mediaQuery.matches)
      
      // Funktion zum Aktualisieren des Zustands
      const handleChange = (event: MediaQueryListEvent) => {
        setMatches(event.matches)
      }
      
      // Füge den Event-Listener hinzu
      mediaQuery.addEventListener('change', handleChange)
      
      // Bereinige den Event-Listener
      return () => {
        mediaQuery.removeEventListener('change', handleChange)
      }
    }
  }, [query])
  
  return matches
}

/**
 * Vordefinierte Breakpoints für gängige Bildschirmgrößen
 */
export const breakpoints = {
  sm: '(min-width: 640px)',
  md: '(min-width: 768px)',
  lg: '(min-width: 1024px)',
  xl: '(min-width: 1280px)',
  '2xl': '(min-width: 1536px)',
  
  // Spezielle Medienabfragen
  dark: '(prefers-color-scheme: dark)',
  light: '(prefers-color-scheme: light)',
  reducedMotion: '(prefers-reduced-motion: reduce)',
  highContrast: '(prefers-contrast: more)',
}

/**
 * Hook für die Verwendung vordefinierter Breakpoints
 * 
 * @param breakpoint Der Name des Breakpoints (sm, md, lg, xl, 2xl)
 * @returns Boolean, der angibt, ob der Breakpoint zutrifft
 */
export function useBreakpoint(breakpoint: keyof typeof breakpoints): boolean {
  return useMediaQuery(breakpoints[breakpoint])
}

/**
 * Hook für die Erkennung von Gerätetypen
 * 
 * @returns Objekt mit Booleans für verschiedene Gerätetypen
 */
export function useDeviceDetection() {
  const isMobile = useMediaQuery('(max-width: 767px)')
  const isTablet = useMediaQuery('(min-width: 768px) and (max-width: 1023px)')
  const isDesktop = useMediaQuery('(min-width: 1024px)')
  const isTouch = useMediaQuery('(hover: none) and (pointer: coarse)')
  
  return {
    isMobile,
    isTablet,
    isDesktop,
    isTouch,
  }
}

/**
 * Hook für die Erkennung von Benutzereinstellungen
 * 
 * @returns Objekt mit Booleans für verschiedene Benutzereinstellungen
 */
export function useUserPreferences() {
  const prefersDarkMode = useMediaQuery('(prefers-color-scheme: dark)')
  const prefersReducedMotion = useMediaQuery('(prefers-reduced-motion: reduce)')
  const prefersHighContrast = useMediaQuery('(prefers-contrast: more)')
  
  return {
    prefersDarkMode,
    prefersReducedMotion,
    prefersHighContrast,
  }
}
