'use client'

import { useEffect, useState } from 'react'

/**
 * Hook zur Erkennung der Tastaturnavigation
 * Fügt die Klasse 'keyboard-user' zum body hinzu, wenn der Benutzer die Tastatur verwendet
 */
export function useKeyboardNavigation() {
  const [isKeyboardUser, setIsKeyboardUser] = useState(false)
  
  useEffect(() => {
    // Funktion zum Erkennen von Tastatureingaben
    const handleKeyDown = (e: KeyboardEvent) => {
      // Nur bestimmte Tasten berücksichtigen (Tab, Pfeiltasten, Enter, Space)
      if (
        e.key === 'Tab' ||
        e.key === 'ArrowUp' ||
        e.key === 'ArrowDown' ||
        e.key === 'ArrowLeft' ||
        e.key === 'ArrowRight' ||
        e.key === 'Enter' ||
        e.key === ' '
      ) {
        if (!isKeyboardUser) {
          setIsKeyboardUser(true)
          document.body.classList.add('keyboard-user')
        }
      }
    }
    
    // Funktion zum Erkennen von Ma<PERSON>ingaben
    const handleMouseDown = () => {
      if (isKeyboardUser) {
        setIsKeyboardUser(false)
        document.body.classList.remove('keyboard-user')
      }
    }
    
    // Event-Listener hinzufügen
    document.addEventListener('keydown', handleKeyDown)
    document.addEventListener('mousedown', handleMouseDown)
    
    // Event-Listener entfernen beim Unmount
    return () => {
      document.removeEventListener('keydown', handleKeyDown)
      document.removeEventListener('mousedown', handleMouseDown)
    }
  }, [isKeyboardUser])
  
  return isKeyboardUser
}

/**
 * Hook für die Tastatursteuerung von Listen
 * @param itemCount Anzahl der Elemente in der Liste
 * @param onSelect Callback-Funktion, die aufgerufen wird, wenn ein Element ausgewählt wird
 * @param orientation Orientierung der Liste (horizontal oder vertikal)
 * @param loop Ob die Navigation am Ende der Liste wieder von vorne beginnen soll
 */
export function useListKeyboardNavigation(
  itemCount: number,
  onSelect: (index: number) => void,
  orientation: 'horizontal' | 'vertical' = 'vertical',
  loop: boolean = true
) {
  const [focusedIndex, setFocusedIndex] = useState<number | null>(null)
  
  const handleKeyDown = (e: React.KeyboardEvent) => {
    let newIndex = focusedIndex
    
    // Bestimme die neue Index-Position basierend auf der Taste und Orientierung
    if (orientation === 'horizontal') {
      if (e.key === 'ArrowRight') {
        newIndex = focusedIndex !== null ? focusedIndex + 1 : 0
      } else if (e.key === 'ArrowLeft') {
        newIndex = focusedIndex !== null ? focusedIndex - 1 : itemCount - 1
      }
    } else {
      if (e.key === 'ArrowDown') {
        newIndex = focusedIndex !== null ? focusedIndex + 1 : 0
      } else if (e.key === 'ArrowUp') {
        newIndex = focusedIndex !== null ? focusedIndex - 1 : itemCount - 1
      }
    }
    
    // Home und End für beide Orientierungen
    if (e.key === 'Home') {
      newIndex = 0
    } else if (e.key === 'End') {
      newIndex = itemCount - 1
    }
    
    // Wenn Enter oder Space gedrückt wird, wähle das Element aus
    if ((e.key === 'Enter' || e.key === ' ') && focusedIndex !== null) {
      e.preventDefault()
      onSelect(focusedIndex)
      return
    }
    
    // Wenn keine Änderung, beende
    if (newIndex === focusedIndex) return
    
    // Stelle sicher, dass der Index im gültigen Bereich liegt
    if (newIndex !== null) {
      if (newIndex < 0) {
        newIndex = loop ? itemCount - 1 : 0
      } else if (newIndex >= itemCount) {
        newIndex = loop ? 0 : itemCount - 1
      }
      
      // Verhindere Standard-Scrollverhalten
      e.preventDefault()
      
      // Aktualisiere den fokussierten Index
      setFocusedIndex(newIndex)
      
      // Rufe den Callback auf
      onSelect(newIndex)
    }
  }
  
  return {
    focusedIndex,
    setFocusedIndex,
    handleKeyDown,
  }
}
