'use client'

import { useEffect, useRef } from 'react'

/**
 * Hook zum Einfangen des Fokus innerhalb eines Elements
 * Nützlich für Modals, Dialoge und andere Overlays
 * 
 * @param isActive Ob die Fokus-Falle aktiv ist
 * @param initialFocusRef Optional: Refer<PERSON>z auf das Element, das initial fokussiert werden soll
 * @param returnFocusRef Optional: Referenz auf das Element, zu dem der Fokus zurückkehren soll
 */
export function useFocusTrap(
  isActive: boolean,
  initialFocusRef?: React.RefObject<HTMLElement>,
  returnFocusRef?: React.RefObject<HTMLElement>
) {
  const containerRef = useRef<HTMLDivElement>(null)
  const previousFocusRef = useRef<HTMLElement | null>(null)
  
  // Speichere das vorherige fokussierte Element
  useEffect(() => {
    if (isActive) {
      previousFocusRef.current = document.activeElement as HTMLElement
    }
  }, [isActive])
  
  // Setze den initialen Fokus
  useEffect(() => {
    if (isActive) {
      // Warte einen Moment, damit das DOM aktualisiert werden kann
      const timeoutId = setTimeout(() => {
        if (initialFocusRef && initialFocusRef.current) {
          initialFocusRef.current.focus()
        } else if (containerRef.current) {
          // Finde das erste fokussierbare Element
          const focusableElements = getFocusableElements(containerRef.current)
          if (focusableElements.length > 0) {
            focusableElements[0].focus()
          } else {
            // Wenn kein fokussierbares Element gefunden wurde, fokussiere den Container selbst
            containerRef.current.focus()
          }
        }
      }, 100)
      
      return () => clearTimeout(timeoutId)
    }
  }, [isActive, initialFocusRef])
  
  // Setze den Fokus zurück, wenn die Falle deaktiviert wird
  useEffect(() => {
    if (!isActive) {
      const timeoutId = setTimeout(() => {
        if (returnFocusRef && returnFocusRef.current) {
          returnFocusRef.current.focus()
        } else if (previousFocusRef.current) {
          previousFocusRef.current.focus()
        }
      }, 100)
      
      return () => clearTimeout(timeoutId)
    }
  }, [isActive, returnFocusRef])
  
  // Fange den Fokus ein
  useEffect(() => {
    if (!isActive || !containerRef.current) return
    
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key !== 'Tab' || !containerRef.current) return
      
      const focusableElements = getFocusableElements(containerRef.current)
      if (focusableElements.length === 0) return
      
      const firstElement = focusableElements[0]
      const lastElement = focusableElements[focusableElements.length - 1]
      
      // Shift+Tab: Wenn am Anfang, gehe zum Ende
      if (e.shiftKey && document.activeElement === firstElement) {
        e.preventDefault()
        lastElement.focus()
      }
      // Tab: Wenn am Ende, gehe zum Anfang
      else if (!e.shiftKey && document.activeElement === lastElement) {
        e.preventDefault()
        firstElement.focus()
      }
    }
    
    document.addEventListener('keydown', handleKeyDown)
    return () => {
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [isActive])
  
  return containerRef
}

/**
 * Hilfsfunktion zum Finden aller fokussierbaren Elemente innerhalb eines Containers
 */
function getFocusableElements(container: HTMLElement): HTMLElement[] {
  const selector = [
    'a[href]',
    'button:not([disabled])',
    'input:not([disabled])',
    'select:not([disabled])',
    'textarea:not([disabled])',
    '[tabindex]:not([tabindex="-1"])',
    '[contenteditable]',
  ].join(',')
  
  return Array.from(container.querySelectorAll(selector)) as HTMLElement[]
}
