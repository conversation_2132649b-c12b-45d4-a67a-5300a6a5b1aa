/* Mobile Performance Optimizations */

/* Critical CSS for above-the-fold content */
.critical-above-fold {
  contain: layout style paint;
  will-change: auto;
}

/* Optimize animations for mobile */
@media (max-width: 768px) {
  /* Reduce animation complexity on mobile */
  * {
    animation-duration: 0.2s !important;
    transition-duration: 0.2s !important;
  }
  
  /* Disable complex animations on mobile */
  .complex-animation {
    animation: none !important;
    transform: none !important;
  }
  
  /* Optimize parallax effects */
  .parallax-element {
    transform: none !important;
    will-change: auto !important;
  }
  
  /* Reduce motion for better performance */
  .motion-reduce-mobile {
    animation: none !important;
    transition: none !important;
  }
}

/* Optimize images for mobile */
.mobile-optimized-image {
  content-visibility: auto;
  contain-intrinsic-size: 300px 200px;
}

/* Lazy loading optimization */
.lazy-load-container {
  content-visibility: auto;
  contain-intrinsic-size: 0 500px;
}

/* Optimize text rendering */
.optimized-text {
  text-rendering: optimizeSpeed;
  font-display: swap;
}

/* GPU acceleration for critical elements only */
.gpu-critical {
  transform: translateZ(0);
  will-change: transform;
  backface-visibility: hidden;
}

/* Remove GPU acceleration from non-critical elements */
.no-gpu {
  will-change: auto;
  transform: none;
  backface-visibility: visible;
}

/* Optimize scrolling performance */
.scroll-optimized {
  scroll-behavior: auto;
  overscroll-behavior: contain;
}

/* Reduce repaints and reflows */
.layout-optimized {
  contain: layout style;
}

/* Critical font loading */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url('/fonts/inter-var.woff2') format('woff2');
}

/* Mobile-first responsive utilities */
.mobile-hidden {
  display: none;
}

@media (min-width: 768px) {
  .mobile-hidden {
    display: block;
  }
}

.desktop-hidden {
  display: block;
}

@media (min-width: 768px) {
  .desktop-hidden {
    display: none;
  }
}

/* Optimize hero section for mobile */
.hero-mobile-optimized {
  min-height: 100vh;
  contain: layout style;
}

@media (max-width: 768px) {
  .hero-mobile-optimized {
    min-height: 80vh; /* Reduce height on mobile */
  }
}

/* Optimize button interactions */
.button-optimized {
  touch-action: manipulation;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

/* Optimize form elements */
.form-optimized input,
.form-optimized textarea,
.form-optimized select {
  touch-action: manipulation;
  font-size: 16px; /* Prevent zoom on iOS */
}

/* Optimize modal/dialog performance */
.modal-optimized {
  contain: layout style paint;
  will-change: opacity, transform;
}

/* Optimize card components */
.card-optimized {
  contain: layout style;
  content-visibility: auto;
  contain-intrinsic-size: 0 200px;
}

/* Critical loading states */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Optimize intersection observer targets */
.intersection-target {
  content-visibility: auto;
  contain-intrinsic-size: 0 300px;
}

/* Performance monitoring utilities */
.perf-monitor {
  contain: strict;
  content-visibility: auto;
}

/* Optimize backdrop blur for mobile */
@media (max-width: 768px) {
  .backdrop-blur-sm {
    backdrop-filter: none !important;
    background-color: rgba(255, 255, 255, 0.9) !important;
  }
  
  .backdrop-blur-md {
    backdrop-filter: none !important;
    background-color: rgba(255, 255, 255, 0.95) !important;
  }
}

/* Optimize gradients for mobile */
@media (max-width: 768px) {
  .gradient-complex {
    background: solid !important;
  }
}

/* Critical path optimization */
.critical-path {
  contain: layout style paint;
  content-visibility: auto;
}

/* Optimize video elements */
.video-optimized {
  content-visibility: auto;
  contain-intrinsic-size: 16 / 9;
}

@media (max-width: 768px) {
  .video-optimized {
    display: none; /* Hide videos on mobile for performance */
  }
}

/* Optimize carousel/slider components */
.carousel-optimized {
  contain: layout style;
  scroll-snap-type: x mandatory;
  overscroll-behavior-x: contain;
}

.carousel-optimized .slide {
  scroll-snap-align: start;
  content-visibility: auto;
  contain-intrinsic-size: 300px 200px;
}

/* Optimize navigation */
.nav-optimized {
  contain: layout style;
  will-change: transform;
}

@media (max-width: 768px) {
  .nav-optimized {
    will-change: auto;
  }
}

/* Optimize footer */
.footer-optimized {
  content-visibility: auto;
  contain-intrinsic-size: 0 400px;
}

/* Performance-first utilities */
.perf-first {
  contain: layout style paint;
  content-visibility: auto;
}

/* Mobile touch optimizations */
.touch-optimized {
  touch-action: manipulation;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}

/* Optimize for low-end devices */
@media (max-width: 768px) and (max-resolution: 1.5dppx) {
  .high-res-only {
    display: none !important;
  }
  
  .low-end-optimized {
    animation: none !important;
    transition: none !important;
    transform: none !important;
    filter: none !important;
    backdrop-filter: none !important;
  }
}
