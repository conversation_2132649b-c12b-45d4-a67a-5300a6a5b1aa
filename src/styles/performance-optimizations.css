/* Performance Optimizations for Anti-Flickering */

/* Hardware acceleration for smooth animations */
.performance-optimized {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Optimize scrolling performance */
* {
  scroll-behavior: smooth;
}

/* Reduce repaints and reflows */
.anti-flicker {
  will-change: transform, opacity;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Optimize for animations */
.motion-optimized {
  will-change: transform;
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
}

/* Header specific optimizations */
.header-optimized {
  will-change: transform, backdrop-filter, box-shadow;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Background elements optimizations */
.background-optimized {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
  contain: layout style paint;
}

/* Hero section optimizations */
.hero-optimized {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
  contain: layout style;
}

/* Reduce animation complexity on low-end devices */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Force GPU acceleration for common elements */
.gpu-accelerated {
  transform: translate3d(0, 0, 0);
  will-change: transform;
  backface-visibility: hidden;
}

/* Optimize backdrop-blur */
.backdrop-blur-optimized {
  will-change: backdrop-filter;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Optimize gradients */
.gradient-optimized {
  will-change: background;
  transform: translateZ(0);
  backface-visibility: hidden;
} 