/* Critical CSS for Above-the-Fold Content - Mobile First */

/* Reset and base styles */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  text-rendering: optimizeSpeed;
}

body {
  background-color: #ffffff;
  color: #1f2937;
  font-size: 16px;
  line-height: 1.6;
  overflow-x: hidden;
}

/* Critical layout containers */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 640px) {
  .container {
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container {
    padding: 0 2rem;
  }
}

/* Header styles - critical for LCP */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
  border-bottom: 1px solid rgba(229, 231, 235, 0.8);
  height: 64px;
  display: flex;
  align-items: center;
  contain: layout style;
}

@media (max-width: 768px) {
  .header {
    backdrop-filter: none;
    background-color: #ffffff;
    height: 56px;
  }
}

/* Logo styles */
.logo {
  height: 32px;
  width: auto;
  display: block;
}

@media (min-width: 768px) {
  .logo {
    height: 40px;
  }
}

/* Hero section - critical for LCP */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #162760 0%, #11142b 100%);
  color: white;
  text-align: center;
  padding: 80px 1rem 2rem;
  position: relative;
  contain: layout style;
}

@media (max-width: 768px) {
  .hero {
    min-height: 80vh;
    padding: 60px 1rem 2rem;
  }
}

/* Hero content */
.hero-content {
  max-width: 800px;
  margin: 0 auto;
  z-index: 2;
  position: relative;
}

.hero-title {
  font-size: 2rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1rem;
  letter-spacing: -0.025em;
}

@media (min-width: 640px) {
  .hero-title {
    font-size: 2.5rem;
  }
}

@media (min-width: 768px) {
  .hero-title {
    font-size: 3rem;
  }
}

@media (min-width: 1024px) {
  .hero-title {
    font-size: 3.5rem;
  }
}

.hero-subtitle {
  font-size: 1.125rem;
  opacity: 0.9;
  margin-bottom: 2rem;
  line-height: 1.6;
}

@media (min-width: 768px) {
  .hero-subtitle {
    font-size: 1.25rem;
  }
}

/* Button styles - critical for interaction */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  border-radius: 0.5rem;
  text-decoration: none;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
  touch-action: manipulation;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.btn-primary {
  background-color: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background-color: #2563eb;
  transform: translateY(-1px);
}

.btn-primary:active {
  transform: translateY(0);
}

@media (max-width: 768px) {
  .btn {
    padding: 0.875rem 2rem;
    font-size: 1.125rem;
    min-height: 48px; /* Better touch target */
  }
}

/* Navigation styles */
.nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 100%;
}

.nav-links {
  display: none;
  align-items: center;
  gap: 2rem;
  list-style: none;
}

@media (min-width: 768px) {
  .nav-links {
    display: flex;
  }
}

.nav-link {
  color: #374151;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s ease;
}

.nav-link:hover {
  color: #3b82f6;
}

/* Mobile menu button */
.mobile-menu-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: none;
  background: none;
  cursor: pointer;
  color: #374151;
}

@media (min-width: 768px) {
  .mobile-menu-btn {
    display: none;
  }
}

/* Loading states */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 0.25rem;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Utility classes */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.sr-only:focus {
  position: static;
  width: auto;
  height: auto;
  padding: 0.5rem 1rem;
  margin: 0;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* Performance optimizations */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
  backface-visibility: hidden;
}

.no-animation {
  animation: none !important;
  transition: none !important;
}

/* Mobile-specific optimizations */
@media (max-width: 768px) {
  /* Reduce motion for better performance */
  .reduce-motion {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  /* Optimize touch interactions */
  .touch-optimized {
    touch-action: manipulation;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  body {
    background-color: #111827;
    color: #f9fafb;
  }
  
  .header {
    background-color: rgba(17, 24, 39, 0.95);
    border-bottom-color: rgba(75, 85, 99, 0.8);
  }
  
  .nav-link {
    color: #d1d5db;
  }
  
  .nav-link:hover {
    color: #60a5fa;
  }
  
  .mobile-menu-btn {
    color: #d1d5db;
  }
}

/* Print styles */
@media print {
  .header,
  .mobile-menu-btn,
  .btn {
    display: none !important;
  }
  
  .hero {
    min-height: auto;
    background: white !important;
    color: black !important;
  }
}
