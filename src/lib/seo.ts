import { Metadata } from 'next'
import { generateSeoMetadata, structuredDataGenerators, SeoProps } from '@/components/Seo'

export const DEFAULT_DOMAIN = 'https://innovatio-pro.com'

// Server-side metadata generation function
function generateServerSeoMetadata({
    title,
    description,
    keywords,
    canonical,
    locale,
    alternateLanguages = [],
    openGraph,
    twitter,
    noIndex = false,
    noFollow = false,
    robotsDirectives = [],
}: SeoProps): Metadata {
    // Build robots meta content
    const robotsContent: string[] = [];
    if (noIndex) robotsContent.push("noindex");
    if (noFollow) robotsContent.push("nofollow");
    if (!noIndex) robotsContent.push("index");
    if (!noFollow) robotsContent.push("follow");
    robotsContent.push(...robotsDirectives);

    // Default OpenGraph values
    const ogTitle = openGraph?.title || title;
    const ogDescription = openGraph?.description || description;
    const ogImage = openGraph?.image || `${DEFAULT_DOMAIN}/images/og-default.jpg`;
    const ogType = openGraph?.type || "website";
    const ogUrl = openGraph?.url || canonical || DEFAULT_DOMAIN;

    // Default Twitter values
    const twitterTitle = twitter?.title || title;
    const twitterDescription = twitter?.description || description;
    const twitterImage =
        twitter?.image || `${DEFAULT_DOMAIN}/images/twitter-default.jpg`;
    const twitterCard = twitter?.card || "summary_large_image";

    // Generate hreflang based on locale
    const hreflangCode =
        locale === "en"
            ? "en-US"
            : locale === "de"
                ? "de-DE"
                : locale === "ru"
                    ? "ru-RU"
                    : locale === "tr"
                        ? "tr-TR"
                        : locale === "ar"
                            ? "ar-AE"
                            : "en-US";

    // Build alternate languages object
    const languages: Record<string, string> = {};
    alternateLanguages.forEach(({ locale: altLocale, href }) => {
        const altHreflang =
            altLocale === "en"
                ? "en-US"
                : altLocale === "de"
                    ? "de-DE"
                    : altLocale === "ru"
                        ? "ru-RU"
                        : altLocale === "tr"
                            ? "tr-TR"
                            : altLocale === "ar"
                                ? "ar-AE"
                                : altLocale;
        languages[altHreflang] = href;
    });

    // Add x-default
    languages["x-default"] = `${DEFAULT_DOMAIN}/en`;

    return {
        title,
        description,
        keywords: keywords?.split(",").map((k) => k.trim()),
        robots: {
            index: !noIndex,
            follow: !noFollow,
            googleBot: {
                index: !noIndex,
                follow: !noFollow,
                "max-image-preview": "large",
                "max-video-preview": -1,
                "max-snippet": -1,
            },
        },
        alternates: {
            canonical,
            languages,
        },
        openGraph: {
            title: ogTitle,
            description: ogDescription,
            url: ogUrl,
            siteName: "Innovatio",
            locale: hreflangCode,
            type: ogType as any,
            images: [
                {
                    url: ogImage,
                    width: 1200,
                    height: 630,
                    alt: ogTitle,
                },
            ],
        },
        twitter: {
            card: twitterCard as any,
            title: twitterTitle,
            description: twitterDescription,
            images: [twitterImage],
        },
        metadataBase: new URL(DEFAULT_DOMAIN),
        verification: {
            google: "google-site-verification-code", // Replace with actual verification code
        },
        other: {
            "format-detection": "telephone=no",
            "theme-color": "#3b82f6",
        },
    };
}

// Utility function to create SEO metadata for pages
export function createPageMetadata(
    page: string,
    locale: string,
    dictionary: any,
    additionalProps?: Partial<SeoProps>
): Metadata {
    const seoData = dictionary.seo?.meta?.[page]

    if (!seoData) {
        console.warn(`SEO data not found for page: ${page} in locale: ${locale}`)
        return {}
    }

    const baseUrl = DEFAULT_DOMAIN
    const localePrefix = locale === 'en' ? '' : `/${locale}`
    const pageUrl = page === 'home' ? `${baseUrl}${localePrefix}` : `${baseUrl}${localePrefix}/${page}`

    // Generate alternate languages
    const alternateLanguages = ['en', 'de', 'ru', 'tr', 'ar'].map(altLocale => ({
        locale: altLocale,
        href: altLocale === 'en' ?
            (page === 'home' ? baseUrl : `${baseUrl}/${page}`) :
            (page === 'home' ? `${baseUrl}/${altLocale}` : `${baseUrl}/${altLocale}/${page}`)
    }))

    return generateServerSeoMetadata({
        title: seoData.title,
        description: seoData.description,
        keywords: seoData.keywords,
        canonical: pageUrl,
        locale,
        alternateLanguages,
        openGraph: {
            title: seoData.title,
            description: seoData.description,
            url: pageUrl,
            image: `${baseUrl}/images/og-${page}-${locale}.jpg`,
            type: 'website'
        },
        twitter: {
            title: seoData.title,
            description: seoData.description,
            image: `${baseUrl}/images/twitter-${page}-${locale}.jpg`,
            card: 'summary_large_image'
        },
        ...additionalProps
    })
}

// Generate structured data for different page types
export function generateWebsiteStructuredData(locale: string, dictionary: any) {
    return structuredDataGenerators.website(locale)
}

export function generateOrganizationStructuredData(dictionary: any) {
    const orgData = dictionary.seo?.jsonLd?.organization

    if (!orgData) {
        return structuredDataGenerators.organization()
    }

    return {
        '@context': 'https://schema.org',
        '@type': 'Organization',
        name: orgData.name,
        alternateName: orgData.alternateName,
        description: orgData.description,
        url: orgData.url,
        telephone: orgData.telephone,
        email: orgData.email,
        address: {
            '@type': 'PostalAddress',
            streetAddress: orgData.address.streetAddress,
            addressLocality: orgData.address.addressLocality,
            addressRegion: orgData.address.addressRegion,
            postalCode: orgData.address.postalCode,
            addressCountry: orgData.address.addressCountry,
        },
        sameAs: orgData.sameAs,
        logo: `${DEFAULT_DOMAIN}/images/logo.png`,
        foundingDate: '2025',
        numberOfEmployees: '10-50',
        legalName: orgData.name,
        contactPoint: {
            '@type': 'ContactPoint',
            telephone: orgData.telephone,
            contactType: 'customer service',
            availableLanguage: ['English', 'German', 'Russian', 'Turkish', 'Arabic'],
        },
    }
}

export function generateBreadcrumbStructuredData(items: Array<{ name: string; url: string }>) {
    return structuredDataGenerators.breadcrumb(items)
}

export function generateProductStructuredData(
    name: string,
    description: string,
    price?: string,
    currency?: string,
    locale?: string
) {
    return {
        '@context': 'https://schema.org',
        '@type': 'Product',
        name,
        description,
        brand: {
            '@type': 'Brand',
            name: 'Innovatio',
        },
        category: 'Web Development Service',
        inLanguage: locale || 'en',
        ...(price && currency && {
            offers: {
                '@type': 'Offer',
                price,
                priceCurrency: currency,
                availability: 'https://schema.org/InStock',
                seller: {
                    '@type': 'Organization',
                    name: 'Innovatio',
                },
            },
        }),
    }
}

export function generateServiceStructuredData(
    serviceName: string,
    description: string,
    locale: string,
    price?: { amount: string; currency: string }
) {
    return {
        '@context': 'https://schema.org',
        '@type': 'Service',
        name: serviceName,
        description,
        provider: {
            '@type': 'Organization',
            name: 'Innovatio',
            url: DEFAULT_DOMAIN,
        },
        areaServed: {
            '@type': 'Place',
            name: 'Worldwide',
        },
        serviceType: 'Web Development',
        inLanguage: locale,
        ...(price && {
            offers: {
                '@type': 'Offer',
                price: price.amount,
                priceCurrency: price.currency,
            },
        }),
    }
}

export function generateFAQStructuredData(questions: Array<{ question: string; answer: string }>) {
    return structuredDataGenerators.faq(questions)
}

// Template-specific structured data generators
export function generateTemplateStructuredData(
    templateName: string,
    description: string,
    category: string,
    locale: string,
    price?: { amount: string; currency: string }
) {
    return {
        '@context': 'https://schema.org',
        '@type': 'DigitalDocument',
        name: templateName,
        description,
        category: `${category} Website Template`,
        inLanguage: locale,
        creator: {
            '@type': 'Organization',
            name: 'Innovatio',
            url: DEFAULT_DOMAIN,
        },
        publisher: {
            '@type': 'Organization',
            name: 'Innovatio',
            url: DEFAULT_DOMAIN,
        },
        ...(price && {
            offers: {
                '@type': 'Offer',
                price: price.amount,
                priceCurrency: price.currency,
                availability: 'https://schema.org/InStock',
            },
        }),
    }
}

// SEO URL generation utilities
export function generateCanonicalUrl(locale: string, path: string): string {
    const localePrefix = locale === 'en' ? '' : `/${locale}`
    return `${DEFAULT_DOMAIN}${localePrefix}${path}`
}

export function generateAlternateUrls(path: string): Array<{ locale: string; href: string }> {
    const locales = ['en', 'de', 'ru', 'tr', 'ar']

    return locales.map(locale => ({
        locale,
        href: locale === 'en' ?
            `${DEFAULT_DOMAIN}${path}` :
            `${DEFAULT_DOMAIN}/${locale}${path}`
    }))
}

// SEO validation utilities
export function validateSeoData(seoData: any): boolean {
    if (!seoData.title || seoData.title.length < 10 || seoData.title.length > 60) {
        console.warn('SEO title should be between 10-60 characters')
        return false
    }

    if (!seoData.description || seoData.description.length < 120 || seoData.description.length > 160) {
        console.warn('SEO description should be between 120-160 characters')
        return false
    }

    return true
}

// Generate robots meta content
export function generateRobotsContent(options: {
    index?: boolean
    follow?: boolean
    imageIndex?: boolean
    maxSnippet?: number
    maxImagePreview?: 'none' | 'standard' | 'large'
    maxVideoPreview?: number | -1
}): string {
    const robots: string[] = []

    robots.push(options.index !== false ? 'index' : 'noindex')
    robots.push(options.follow !== false ? 'follow' : 'nofollow')

    if (options.imageIndex === false) robots.push('noimageindex')
    if (options.maxSnippet) robots.push(`max-snippet:${options.maxSnippet}`)
    if (options.maxImagePreview) robots.push(`max-image-preview:${options.maxImagePreview}`)
    if (options.maxVideoPreview) robots.push(`max-video-preview:${options.maxVideoPreview}`)

    return robots.join(', ')
} 