import { 
  CMSType, 
  Locale, 
  HeroContent, 
  ServiceContent, 
  PortfolioItem, 
  PricingConfig, 
  Testimonial, 
  FAQ, 
  SiteSettings, 
  ImageAsset, 
  CMSUser 
} from './types';

// Interface für den CMS-Adapter
export interface CMSAdapter {
  // Verbindung und Konfiguration
  initialize(): Promise<void>;
  getType(): CMSType;
  isConnected(): boolean;
  
  // Inhaltsabfragen
  getHeroContent(locale: Locale): Promise<HeroContent>;
  getServices(locale: Locale): Promise<ServiceContent[]>;
  getPortfolioItems(locale: Locale, category?: string): Promise<PortfolioItem[]>;
  getPricingConfig(locale: Locale): Promise<PricingConfig>;
  getTestimonials(locale: Locale): Promise<Testimonial[]>;
  getFAQs(locale: Locale, category?: string): Promise<FAQ[]>;
  getSiteSettings(locale: Locale): Promise<SiteSettings>;
  
  // Inhaltsbearbeitung
  updateHeroContent(locale: Locale, content: Partial<HeroContent>): Promise<HeroContent>;
  updateService(locale: Locale, id: string, content: Partial<ServiceContent>): Promise<ServiceContent>;
  createService(locale: Locale, content: Omit<ServiceContent, 'id' | 'createdAt' | 'updatedAt' | 'locale'>): Promise<ServiceContent>;
  deleteService(locale: Locale, id: string): Promise<boolean>;
  
  updatePortfolioItem(locale: Locale, id: string, content: Partial<PortfolioItem>): Promise<PortfolioItem>;
  createPortfolioItem(locale: Locale, content: Omit<PortfolioItem, 'id' | 'createdAt' | 'updatedAt' | 'locale'>): Promise<PortfolioItem>;
  deletePortfolioItem(locale: Locale, id: string): Promise<boolean>;
  
  updatePricingConfig(locale: Locale, config: Partial<PricingConfig>): Promise<PricingConfig>;
  
  updateTestimonial(locale: Locale, id: string, content: Partial<Testimonial>): Promise<Testimonial>;
  createTestimonial(locale: Locale, content: Omit<Testimonial, 'id' | 'createdAt' | 'updatedAt' | 'locale'>): Promise<Testimonial>;
  deleteTestimonial(locale: Locale, id: string): Promise<boolean>;
  
  updateFAQ(locale: Locale, id: string, content: Partial<FAQ>): Promise<FAQ>;
  createFAQ(locale: Locale, content: Omit<FAQ, 'id' | 'createdAt' | 'updatedAt' | 'locale'>): Promise<FAQ>;
  deleteFAQ(locale: Locale, id: string): Promise<boolean>;
  
  updateSiteSettings(locale: Locale, settings: Partial<SiteSettings>): Promise<SiteSettings>;
  
  // Medien-Management
  uploadImage(file: File, alt: string): Promise<ImageAsset>;
  getImages(): Promise<ImageAsset[]>;
  deleteImage(id: string): Promise<boolean>;
  
  // Benutzer-Management
  getCurrentUser(): Promise<CMSUser | null>;
  login(email: string, password: string): Promise<CMSUser>;
  logout(): Promise<boolean>;
  
  // Übersetzungen
  getAvailableLocales(): Promise<Locale[]>;
  translateContent(contentType: string, contentId: string, fromLocale: Locale, toLocale: Locale): Promise<boolean>;
}
