// CMS-Typen und Interfaces

// Unterstützte CMS-Systeme
export type CMSType = 'sanity' | 'contentful' | 'strapi' | 'local';

// Unterstützte Sprachen
export type Locale = 'de' | 'en' | 'ru' | 'ar' | 'tr';

// Basis-Interface für alle CMS-Inhalte
export interface CMSContent {
  id: string;
  createdAt: string;
  updatedAt: string;
  locale: Locale;
}

// Hero-Sektion
export interface HeroContent extends CMSContent {
  title: string;
  subtitle: string;
  description: string;
  ctaPrimary: string;
  bookConsultation: string;
  typing: {
    businessGrowth: string;
    digitalSolution: string;
    userExperience: string;
    technologyInnovation: string;
  };
  solutions: {
    streamlinedOperations: {
      title: string;
      description: string;
    };
    enhancedUserExperience: {
      title: string;
      description: string;
    };
    dataInsights: {
      title: string;
      description: string;
    };
    scalableArchitecture: {
      title: string;
      description: string;
    };
  };
}

// Dienstleistungen/Pakete
export interface ServiceContent extends CMSContent {
  title: string;
  description: string;
  icon: string;
  features: string[];
  technologies: {
    name: string;
    icon: string;
    description: string;
  }[];
}

// Portfolio-Items
export interface PortfolioItem extends CMSContent {
  title: string;
  category: string;
  description: string;
  images: string[];
  technologies: string[];
  features: string[];
  clientName?: string;
  clientLogo?: string;
  testimonial?: string;
  link?: string;
}

// Preisrechner-Konfiguration
export interface PricingConfig extends CMSContent {
  basePrices: {
    mvp: number;
    prototype: number;
    homepage: number;
    consulting: number;
  };
  features: {
    [key: string]: {
      name: string;
      price: number;
      description: string;
    };
  };
  industryFactors: {
    [key: string]: number;
  };
  timelineFactors: {
    [key: string]: number;
  };
  complexityFactors: {
    [key: string]: number;
  };
}

// Testimonials
export interface Testimonial extends CMSContent {
  name: string;
  position: string;
  company: string;
  text: string;
  rating: number;
  image?: string;
}

// FAQ
export interface FAQ extends CMSContent {
  question: string;
  answer: string;
  category: string;
}

// Allgemeine Seiteneinstellungen
export interface SiteSettings extends CMSContent {
  siteName: string;
  siteDescription: string;
  contactEmail: string;
  contactPhone: string;
  socialLinks: {
    platform: string;
    url: string;
    icon: string;
  }[];
  address: {
    street: string;
    city: string;
    postalCode: string;
    country: string;
  };
  legalPages: {
    privacyPolicy: string;
    termsOfService: string;
    imprint: string;
  };
}

// Bild-Asset
export interface ImageAsset {
  id: string;
  url: string;
  alt: string;
  width: number;
  height: number;
  mimeType: string;
  fileSize: number;
}

// Benutzer für Admin-Bereich
export interface CMSUser {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'editor' | 'viewer';
  avatar?: string;
}

// Audit-Log für Änderungen
export interface AuditLogEntry {
  id: string;
  userId: string;
  action: 'create' | 'update' | 'delete';
  contentType: string;
  contentId: string;
  timestamp: string;
  changes?: Record<string, any>;
}
