import { 
  CMSAdapter 
} from '../CMSAdapter';
import { 
  CMSType, 
  Locale, 
  HeroContent, 
  ServiceContent, 
  PortfolioItem, 
  PricingConfig, 
  Testimonial, 
  FAQ, 
  SiteSettings, 
  ImageAsset, 
  CMSUser 
} from '../types';

/**
 * <PERSON><PERSON><PERSON> CMS-Ada<PERSON>er, der Daten im localStorage speichert
 * Nützlich für Entwicklung und als Fallback
 */
export class LocalCMSAdapter implements CMSAdapter {
  private connected: boolean = false;
  private currentUser: CMSUser | null = null;
  private storagePrefix: string = 'local_cms_';

  constructor() {
    // Prüfen, ob localStorage verfügbar ist
    this.connected = typeof window !== 'undefined' && !!window.localStorage;
  }

  // Hilfsfunktion zum Speichern von Daten im localStorage
  private saveToStorage<T>(key: string, data: T): void {
    if (!this.connected) return;
    localStorage.setItem(this.storagePrefix + key, JSON.stringify(data));
  }

  // Hilfsfunktion zum Laden von Daten aus dem localStorage
  private loadFromStorage<T>(key: string, defaultValue: T): T {
    if (!this.connected) return defaultValue;
    const data = localStorage.getItem(this.storagePrefix + key);
    if (!data) return defaultValue;
    try {
      return JSON.parse(data) as T;
    } catch (error) {
      console.error(`Error parsing data for key ${key}:`, error);
      return defaultValue;
    }
  }

  // Hilfsfunktion zum Generieren einer eindeutigen ID
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
  }

  // Hilfsfunktion zum Aktualisieren eines Elements in einem Array
  private updateItemInArray<T extends { id: string }>(
    array: T[],
    id: string,
    updates: Partial<T>
  ): T[] {
    return array.map(item => 
      item.id === id ? { ...item, ...updates, updatedAt: new Date().toISOString() } : item
    );
  }

  // Implementierung der CMSAdapter-Methoden
  async initialize(): Promise<void> {
    // Nichts zu tun für lokalen Adapter
    return Promise.resolve();
  }

  getType(): CMSType {
    return 'local';
  }

  isConnected(): boolean {
    return this.connected;
  }

  // Hero-Content
  async getHeroContent(locale: Locale): Promise<HeroContent> {
    const key = `hero_${locale}`;
    return this.loadFromStorage<HeroContent>(key, {
      id: 'hero',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      locale,
      title: 'Default Hero Title',
      subtitle: 'Default Hero Subtitle',
      description: 'Default hero description text.',
      ctaPrimary: 'Get Started',
      bookConsultation: 'Book a Consultation',
      typing: {
        businessGrowth: 'Business Growth',
        digitalSolution: 'Digital Solution',
        userExperience: 'User Experience',
        technologyInnovation: 'Technology Innovation',
      },
      solutions: {
        streamlinedOperations: {
          title: 'Streamlined Operations',
          description: 'Optimize your business processes',
        },
        enhancedUserExperience: {
          title: 'Enhanced User Experience',
          description: 'Create delightful user experiences',
        },
        dataInsights: {
          title: 'Data Insights',
          description: 'Make data-driven decisions',
        },
        scalableArchitecture: {
          title: 'Scalable Architecture',
          description: 'Build systems that grow with you',
        },
      },
    });
  }

  async updateHeroContent(locale: Locale, content: Partial<HeroContent>): Promise<HeroContent> {
    const key = `hero_${locale}`;
    const currentContent = await this.getHeroContent(locale);
    const updatedContent = {
      ...currentContent,
      ...content,
      updatedAt: new Date().toISOString(),
    };
    this.saveToStorage(key, updatedContent);
    return updatedContent;
  }

  // Services
  async getServices(locale: Locale): Promise<ServiceContent[]> {
    const key = `services_${locale}`;
    return this.loadFromStorage<ServiceContent[]>(key, []);
  }

  async updateService(locale: Locale, id: string, content: Partial<ServiceContent>): Promise<ServiceContent> {
    const key = `services_${locale}`;
    const services = await this.getServices(locale);
    const service = services.find(s => s.id === id);
    
    if (!service) {
      throw new Error(`Service with id ${id} not found`);
    }
    
    const updatedServices = this.updateItemInArray(services, id, content);
    this.saveToStorage(key, updatedServices);
    
    return {
      ...service,
      ...content,
      updatedAt: new Date().toISOString(),
    };
  }

  async createService(
    locale: Locale, 
    content: Omit<ServiceContent, 'id' | 'createdAt' | 'updatedAt' | 'locale'>
  ): Promise<ServiceContent> {
    const key = `services_${locale}`;
    const services = await this.getServices(locale);
    const now = new Date().toISOString();
    
    const newService: ServiceContent = {
      ...content as any,
      id: this.generateId(),
      createdAt: now,
      updatedAt: now,
      locale,
    };
    
    services.push(newService);
    this.saveToStorage(key, services);
    
    return newService;
  }

  async deleteService(locale: Locale, id: string): Promise<boolean> {
    const key = `services_${locale}`;
    const services = await this.getServices(locale);
    const filteredServices = services.filter(s => s.id !== id);
    
    if (filteredServices.length === services.length) {
      return false; // Nichts wurde gelöscht
    }
    
    this.saveToStorage(key, filteredServices);
    return true;
  }

  // Portfolio-Items
  async getPortfolioItems(locale: Locale, category?: string): Promise<PortfolioItem[]> {
    const key = `portfolio_${locale}`;
    const items = this.loadFromStorage<PortfolioItem[]>(key, []);
    
    if (category) {
      return items.filter(item => item.category === category);
    }
    
    return items;
  }

  async updatePortfolioItem(locale: Locale, id: string, content: Partial<PortfolioItem>): Promise<PortfolioItem> {
    const key = `portfolio_${locale}`;
    const items = await this.getPortfolioItems(locale);
    const item = items.find(i => i.id === id);
    
    if (!item) {
      throw new Error(`Portfolio item with id ${id} not found`);
    }
    
    const updatedItems = this.updateItemInArray(items, id, content);
    this.saveToStorage(key, updatedItems);
    
    return {
      ...item,
      ...content,
      updatedAt: new Date().toISOString(),
    };
  }

  async createPortfolioItem(
    locale: Locale, 
    content: Omit<PortfolioItem, 'id' | 'createdAt' | 'updatedAt' | 'locale'>
  ): Promise<PortfolioItem> {
    const key = `portfolio_${locale}`;
    const items = await this.getPortfolioItems(locale);
    const now = new Date().toISOString();
    
    const newItem: PortfolioItem = {
      ...content as any,
      id: this.generateId(),
      createdAt: now,
      updatedAt: now,
      locale,
    };
    
    items.push(newItem);
    this.saveToStorage(key, items);
    
    return newItem;
  }

  async deletePortfolioItem(locale: Locale, id: string): Promise<boolean> {
    const key = `portfolio_${locale}`;
    const items = await this.getPortfolioItems(locale);
    const filteredItems = items.filter(i => i.id !== id);
    
    if (filteredItems.length === items.length) {
      return false; // Nichts wurde gelöscht
    }
    
    this.saveToStorage(key, filteredItems);
    return true;
  }

  // Pricing Config
  async getPricingConfig(locale: Locale): Promise<PricingConfig> {
    const key = `pricing_${locale}`;
    return this.loadFromStorage<PricingConfig>(key, {
      id: 'pricing',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      locale,
      basePrices: {
        mvp: 10000,
        prototype: 4500,
        homepage: 1500,
        consulting: 85,
      },
      features: {
        auth_email: {
          name: 'Email Authentication',
          price: 300,
          description: 'User authentication via email',
        },
        // Weitere Features hier...
      },
      industryFactors: {
        ecommerce: 1.2,
        health: 1.3,
        finance: 1.4,
        social: 1.1,
        productivity: 1.0,
        other: 1.0,
      },
      timelineFactors: {
        normal: 1.0,
        fast: 1.3,
        urgent: 1.6,
      },
      complexityFactors: {
        simple: 0.8,
        medium: 1.0,
        complex: 1.4,
      },
    });
  }

  async updatePricingConfig(locale: Locale, config: Partial<PricingConfig>): Promise<PricingConfig> {
    const key = `pricing_${locale}`;
    const currentConfig = await this.getPricingConfig(locale);
    const updatedConfig = {
      ...currentConfig,
      ...config,
      updatedAt: new Date().toISOString(),
    };
    this.saveToStorage(key, updatedConfig);
    return updatedConfig;
  }

  // Testimonials
  async getTestimonials(locale: Locale): Promise<Testimonial[]> {
    const key = `testimonials_${locale}`;
    return this.loadFromStorage<Testimonial[]>(key, []);
  }

  async updateTestimonial(locale: Locale, id: string, content: Partial<Testimonial>): Promise<Testimonial> {
    const key = `testimonials_${locale}`;
    const testimonials = await this.getTestimonials(locale);
    const testimonial = testimonials.find(t => t.id === id);
    
    if (!testimonial) {
      throw new Error(`Testimonial with id ${id} not found`);
    }
    
    const updatedTestimonials = this.updateItemInArray(testimonials, id, content);
    this.saveToStorage(key, updatedTestimonials);
    
    return {
      ...testimonial,
      ...content,
      updatedAt: new Date().toISOString(),
    };
  }

  async createTestimonial(
    locale: Locale, 
    content: Omit<Testimonial, 'id' | 'createdAt' | 'updatedAt' | 'locale'>
  ): Promise<Testimonial> {
    const key = `testimonials_${locale}`;
    const testimonials = await this.getTestimonials(locale);
    const now = new Date().toISOString();
    
    const newTestimonial: Testimonial = {
      ...content as any,
      id: this.generateId(),
      createdAt: now,
      updatedAt: now,
      locale,
    };
    
    testimonials.push(newTestimonial);
    this.saveToStorage(key, testimonials);
    
    return newTestimonial;
  }

  async deleteTestimonial(locale: Locale, id: string): Promise<boolean> {
    const key = `testimonials_${locale}`;
    const testimonials = await this.getTestimonials(locale);
    const filteredTestimonials = testimonials.filter(t => t.id !== id);
    
    if (filteredTestimonials.length === testimonials.length) {
      return false; // Nichts wurde gelöscht
    }
    
    this.saveToStorage(key, filteredTestimonials);
    return true;
  }

  // FAQs
  async getFAQs(locale: Locale, category?: string): Promise<FAQ[]> {
    const key = `faqs_${locale}`;
    const faqs = this.loadFromStorage<FAQ[]>(key, []);
    
    if (category) {
      return faqs.filter(faq => faq.category === category);
    }
    
    return faqs;
  }

  async updateFAQ(locale: Locale, id: string, content: Partial<FAQ>): Promise<FAQ> {
    const key = `faqs_${locale}`;
    const faqs = await this.getFAQs(locale);
    const faq = faqs.find(f => f.id === id);
    
    if (!faq) {
      throw new Error(`FAQ with id ${id} not found`);
    }
    
    const updatedFAQs = this.updateItemInArray(faqs, id, content);
    this.saveToStorage(key, updatedFAQs);
    
    return {
      ...faq,
      ...content,
      updatedAt: new Date().toISOString(),
    };
  }

  async createFAQ(
    locale: Locale, 
    content: Omit<FAQ, 'id' | 'createdAt' | 'updatedAt' | 'locale'>
  ): Promise<FAQ> {
    const key = `faqs_${locale}`;
    const faqs = await this.getFAQs(locale);
    const now = new Date().toISOString();
    
    const newFAQ: FAQ = {
      ...content as any,
      id: this.generateId(),
      createdAt: now,
      updatedAt: now,
      locale,
    };
    
    faqs.push(newFAQ);
    this.saveToStorage(key, faqs);
    
    return newFAQ;
  }

  async deleteFAQ(locale: Locale, id: string): Promise<boolean> {
    const key = `faqs_${locale}`;
    const faqs = await this.getFAQs(locale);
    const filteredFAQs = faqs.filter(f => f.id !== id);
    
    if (filteredFAQs.length === faqs.length) {
      return false; // Nichts wurde gelöscht
    }
    
    this.saveToStorage(key, filteredFAQs);
    return true;
  }

  // Site Settings
  async getSiteSettings(locale: Locale): Promise<SiteSettings> {
    const key = `settings_${locale}`;
    return this.loadFromStorage<SiteSettings>(key, {
      id: 'settings',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      locale,
      siteName: 'Innovatio',
      siteDescription: 'Digital Solutions Company',
      contactEmail: '<EMAIL>',
      contactPhone: '+49 **********',
      socialLinks: [
        {
          platform: 'twitter',
          url: 'https://twitter.com/example',
          icon: 'twitter',
        },
        {
          platform: 'linkedin',
          url: 'https://linkedin.com/company/example',
          icon: 'linkedin',
        },
      ],
      address: {
        street: 'Example Street 123',
        city: 'Berlin',
        postalCode: '10115',
        country: 'Germany',
      },
      legalPages: {
        privacyPolicy: '',
        termsOfService: '',
        imprint: '',
      },
    });
  }

  async updateSiteSettings(locale: Locale, settings: Partial<SiteSettings>): Promise<SiteSettings> {
    const key = `settings_${locale}`;
    const currentSettings = await this.getSiteSettings(locale);
    const updatedSettings = {
      ...currentSettings,
      ...settings,
      updatedAt: new Date().toISOString(),
    };
    this.saveToStorage(key, updatedSettings);
    return updatedSettings;
  }

  // Medien-Management
  async uploadImage(file: File, alt: string): Promise<ImageAsset> {
    // In einem echten Adapter würde hier die Datei hochgeladen werden
    // Für den lokalen Adapter simulieren wir das
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        const url = e.target?.result as string;
        const images = this.loadFromStorage<ImageAsset[]>('images', []);
        const newImage: ImageAsset = {
          id: this.generateId(),
          url,
          alt,
          width: 800, // Dummy-Werte
          height: 600,
          mimeType: file.type,
          fileSize: file.size,
        };
        images.push(newImage);
        this.saveToStorage('images', images);
        resolve(newImage);
      };
      reader.readAsDataURL(file);
    });
  }

  async getImages(): Promise<ImageAsset[]> {
    return this.loadFromStorage<ImageAsset[]>('images', []);
  }

  async deleteImage(id: string): Promise<boolean> {
    const images = await this.getImages();
    const filteredImages = images.filter(img => img.id !== id);
    
    if (filteredImages.length === images.length) {
      return false; // Nichts wurde gelöscht
    }
    
    this.saveToStorage('images', filteredImages);
    return true;
  }

  // Benutzer-Management
  async getCurrentUser(): Promise<CMSUser | null> {
    return this.currentUser;
  }

  async login(email: string, password: string): Promise<CMSUser> {
    // Simulierte Anmeldung für den lokalen Adapter
    // In einem echten Adapter würde hier eine Authentifizierung stattfinden
    const user: CMSUser = {
      id: '1',
      name: 'Admin User',
      email,
      role: 'admin',
    };
    
    this.currentUser = user;
    this.saveToStorage('current_user', user);
    
    return user;
  }

  async logout(): Promise<boolean> {
    this.currentUser = null;
    localStorage.removeItem(this.storagePrefix + 'current_user');
    return true;
  }

  // Übersetzungen
  async getAvailableLocales(): Promise<Locale[]> {
    return ['de', 'en', 'ru', 'ar', 'tr'];
  }

  async translateContent(
    contentType: string, 
    contentId: string, 
    fromLocale: Locale, 
    toLocale: Locale
  ): Promise<boolean> {
    // Simulierte Übersetzung für den lokalen Adapter
    // In einem echten Adapter würde hier eine Übersetzung stattfinden
    
    // Wir laden den Inhalt aus der Quellsprache
    const sourceKey = `${contentType}_${fromLocale}`;
    let sourceContent: any;
    
    switch (contentType) {
      case 'hero':
        sourceContent = await this.getHeroContent(fromLocale);
        break;
      case 'services':
        const services = await this.getServices(fromLocale);
        sourceContent = services.find(s => s.id === contentId);
        break;
      // Weitere Fälle für andere Inhaltstypen...
      default:
        return false;
    }
    
    if (!sourceContent) {
      return false;
    }
    
    // Wir kopieren den Inhalt in die Zielsprache
    const targetContent = {
      ...sourceContent,
      locale: toLocale,
      id: contentId, // ID beibehalten
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    
    // Speichern des übersetzten Inhalts
    switch (contentType) {
      case 'hero':
        await this.updateHeroContent(toLocale, targetContent);
        break;
      case 'services':
        const services = await this.getServices(toLocale);
        const existingIndex = services.findIndex(s => s.id === contentId);
        
        if (existingIndex >= 0) {
          services[existingIndex] = targetContent as ServiceContent;
        } else {
          services.push(targetContent as ServiceContent);
        }
        
        this.saveToStorage(`services_${toLocale}`, services);
        break;
      // Weitere Fälle für andere Inhaltstypen...
      default:
        return false;
    }
    
    return true;
  }
}
