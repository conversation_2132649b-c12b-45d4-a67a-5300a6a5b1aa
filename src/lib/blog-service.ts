import blogData from '@/data/blog-posts.json';

export interface BlogPostData {
    id: string;
    slug: string;
    category: string;
    tags: string[];
    publishedAt: string;
    readingTime: number;
    views: number;
    author: {
        name: string;
        avatar: string;
        bio: string;
    };
    featuredImage: string;
    translations: {
        [locale: string]: {
            title: string;
            excerpt: string;
            content: string;
        };
    };
}

export interface BlogPost extends BlogPostData {
    title: string;
    excerpt: string;
    content: string;
}

export interface BlogMetadata {
    totalPosts: number;
    categories: Record<string, number>;
    languages: string[];
    lastUpdated: string;
}

class BlogService {
    private posts: BlogPostData[];
    private metadata: BlogMetadata;

    constructor() {
        this.posts = blogData.posts as BlogPostData[];
        this.metadata = blogData.metadata as BlogMetadata;
    }

    // Get all posts for a specific locale
    getAllPosts(locale: string = 'en'): any[] {
        return this.posts.map(post => ({
            ...post,
            title: post.translations[locale]?.title || post.translations.en.title,
            excerpt: post.translations[locale]?.excerpt || post.translations.en.excerpt,
            content: post.translations[locale]?.content || post.translations.en.content,
        }));
    }

    // Get a single post by slug
    getPostBySlug(slug: string, locale: string = 'en'): BlogPost | null {
        const post = this.posts.find(p => p.slug === slug);
        if (!post) return null;

        return {
            ...post,
            title: post.translations[locale]?.title || post.translations.en.title,
            excerpt: post.translations[locale]?.excerpt || post.translations.en.excerpt,
            content: post.translations[locale]?.content || post.translations.en.content,
        };
    }

    // Get posts by category
    getPostsByCategory(category: string, locale: string = 'en'): BlogPost[] {
        const filtered = category === 'all'
            ? this.posts
            : this.posts.filter(post => post.category === category);

        return filtered.map(post => ({
            ...post,
            title: post.translations[locale]?.title || post.translations.en.title,
            excerpt: post.translations[locale]?.excerpt || post.translations.en.excerpt,
            content: post.translations[locale]?.content || post.translations.en.content,
        }));
    }

    // Get posts by tag
    getPostsByTag(tag: string, locale: string = 'en'): BlogPost[] {
        const filtered = this.posts.filter(post =>
            post.tags.some(t => t.toLowerCase().includes(tag.toLowerCase()))
        );

        return filtered.map(post => ({
            ...post,
            title: post.translations[locale]?.title || post.translations.en.title,
            excerpt: post.translations[locale]?.excerpt || post.translations.en.excerpt,
            content: post.translations[locale]?.content || post.translations.en.content,
        }));
    }

    // Get recent posts
    getRecentPosts(limit: number = 5, locale: string = 'en'): BlogPost[] {
        const sorted = this.posts
            .sort((a, b) => new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime())
            .slice(0, limit);

        return sorted.map(post => ({
            ...post,
            title: post.translations[locale]?.title || post.translations.en.title,
            excerpt: post.translations[locale]?.excerpt || post.translations.en.excerpt,
            content: post.translations[locale]?.content || post.translations.en.content,
        }));
    }

    // Get related posts
    getRelatedPosts(currentPostId: string, limit: number = 3, locale: string = 'en'): BlogPost[] {
        const currentPost = this.posts.find(p => p.id === currentPostId);
        if (!currentPost) return [];

        // Find posts with similar tags or category
        const related = this.posts
            .filter(post => post.id !== currentPostId)
            .filter(post =>
                post.category === currentPost.category ||
                post.tags.some(tag => currentPost.tags.includes(tag))
            )
            .slice(0, limit);

        return related.map(post => ({
            ...post,
            title: post.translations[locale]?.title || post.translations.en.title,
            excerpt: post.translations[locale]?.excerpt || post.translations.en.excerpt,
            content: post.translations[locale]?.content || post.translations.en.content,
        }));
    }

    // Get blog metadata
    getMetadata(): BlogMetadata {
        return this.metadata;
    }

    // Get categories with post counts
    getCategories(): Record<string, number> {
        return this.metadata.categories;
    }

    // Search posts
    searchPosts(query: string, locale: string = 'en'): BlogPost[] {
        const searchTerm = query.toLowerCase();
        const filtered = this.posts.filter(post => {
            const translation = post.translations[locale] || post.translations.en;
            return (
                translation.title.toLowerCase().includes(searchTerm) ||
                translation.excerpt.toLowerCase().includes(searchTerm) ||
                translation.content.toLowerCase().includes(searchTerm) ||
                post.tags.some(tag => tag.toLowerCase().includes(searchTerm))
            );
        });

        return filtered.map(post => ({
            ...post,
            title: post.translations[locale]?.title || post.translations.en.title,
            excerpt: post.translations[locale]?.excerpt || post.translations.en.excerpt,
            content: post.translations[locale]?.content || post.translations.en.content,
        }));
    }

    // Check if translation exists for a locale
    hasTranslation(postId: string, locale: string): boolean {
        const post = this.posts.find(p => p.id === postId);
        return post ? !!post.translations[locale] : false;
    }

    // Get available languages for a post
    getAvailableLanguages(postId: string): string[] {
        const post = this.posts.find(p => p.id === postId);
        return post ? Object.keys(post.translations) : [];
    }

    // Auto-translate using Google Translate API (placeholder)
    async autoTranslate(text: string, targetLanguage: string): Promise<string> {
        // This would integrate with Google Translate API
        // For now, return a placeholder
        console.log(`Auto-translating to ${targetLanguage}: ${text.substring(0, 50)}...`);

        // Placeholder implementation
        const translations: Record<string, Record<string, string>> = {
            'de': {
                'The Future of Mobile Development': 'Die Zukunft der Mobile-Entwicklung',
                'Artificial Intelligence': 'Künstliche Intelligenz',
                'Flutter Development': 'Flutter-Entwicklung',
            },
            'ru': {
                'The Future of Mobile Development': 'Будущее мобильной разработки',
                'Artificial Intelligence': 'Искусственный интеллект',
                'Flutter Development': 'Разработка Flutter',
            },
            'tr': {
                'The Future of Mobile Development': 'Mobil Geliştirmenin Geleceği',
                'Artificial Intelligence': 'Yapay Zeka',
                'Flutter Development': 'Flutter Geliştirme',
            },
            'ar': {
                'The Future of Mobile Development': 'مستقبل تطوير الهواتف المحمولة',
                'Artificial Intelligence': 'الذكاء الاصطناعي',
                'Flutter Development': 'تطوير Flutter',
            }
        };

        return translations[targetLanguage]?.[text] || text;
    }

    // Generate missing translations for a post
    async generateMissingTranslations(postId: string): Promise<void> {
        const post = this.posts.find(p => p.id === postId);
        if (!post) return;

        const requiredLanguages = this.metadata.languages;
        const existingLanguages = Object.keys(post.translations);
        const missingLanguages = requiredLanguages.filter(lang => !existingLanguages.includes(lang));

        for (const lang of missingLanguages) {
            const baseTranslation = post.translations.en;

            // Auto-translate title, excerpt, and content
            const translatedTitle = await this.autoTranslate(baseTranslation.title, lang);
            const translatedExcerpt = await this.autoTranslate(baseTranslation.excerpt, lang);
            const translatedContent = await this.autoTranslate(baseTranslation.content, lang);

            post.translations[lang] = {
                title: translatedTitle,
                excerpt: translatedExcerpt,
                content: translatedContent,
            };
        }
    }
}

// Singleton instance
export const blogService = new BlogService();

// Export types are already exported above 