// Notion CRM Integration für Innovatio-Pro Proposal & Contract System
import { Client } from '@notionhq/client';
import { CustomerProposal, CreateProposalRequest, UpdateProposalRequest } from '@/types/proposal';
import { formatPrice } from '@/lib/config/services';
import { v4 as uuidv4 } from 'uuid';
import * as crypto from 'crypto';

// Initialize Notion client
const notion = new Client({
    auth: process.env.NOTION_TOKEN,
});

const CRM_DATABASE_ID = process.env.NOTION_CRM_DATABASE_ID || '';

if (!CRM_DATABASE_ID) {
    console.warn('NOTION_CRM_DATABASE_ID not set in environment variables');
}

// Helper function to generate secure URL password
export function generateUrlPassword(): string {
    // Define character sets
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const numbers = '0123456789';
    const specialChars = '!@#$%&*';

    // Combine all character sets
    const allChars = lowercase + uppercase + numbers + specialChars;

    let password = '';

    // Ensure at least one character from each set
    password += lowercase[Math.floor(Math.random() * lowercase.length)];
    password += uppercase[Math.floor(Math.random() * uppercase.length)];
    password += numbers[Math.floor(Math.random() * numbers.length)];
    password += specialChars[Math.floor(Math.random() * specialChars.length)];

    // Fill remaining 12 characters randomly
    for (let i = 4; i < 16; i++) {
        password += allChars[Math.floor(Math.random() * allChars.length)];
    }

    // Shuffle the password to avoid predictable patterns
    return password.split('').sort(() => Math.random() - 0.5).join('');
}

// Helper function to generate URLs
export function generateProposalUrls(clientId: string) {
    return {
        proposalUrl: `/create-proposal/${clientId}`,
        contractUrl: `/contract/${clientId}`,
    };
}

// Map Notion page to CustomerProposal
export function mapCrmLeadToCustomerProposal(page: any): CustomerProposal {
    const properties = page.properties;

    // Helper function to get property value safely
    const getPropertyValue = (propName: string, type: string) => {
        try {
            const prop = properties[propName];
            if (!prop) return null;

            switch (type) {
                case 'title':
                    return prop.title?.[0]?.plain_text || '';
                case 'rich_text':
                    return prop.rich_text?.[0]?.plain_text || '';
                case 'email':
                    return prop.email || '';
                case 'phone_number':
                    return prop.phone_number || '';
                case 'select':
                    return prop.select?.name || '';
                case 'multi_select':
                    return prop.multi_select?.map((item: any) => item.name) || [];
                case 'date':
                    return prop.date?.start || null;
                case 'url':
                    return prop.url || '';
                case 'number':
                    return prop.number || 0;
                default:
                    return null;
            }
        } catch (error) {
            console.error(`Error getting property ${propName}:`, error);
            return null;
        }
    };

    // Parse add-ons from notes field
    const notes = getPropertyValue('Notes', 'rich_text') || '';
    const selectedAddOns: string[] = [];

    // Extract add-ons from notes if formatted properly
    const addOnMatches = notes.match(/Add-ons?:\s*(.*?)(?:\n|$)/i);
    if (addOnMatches) {
        const addOnString = addOnMatches[1];
        // Parse add-on list (assumes format like "addon1, addon2, addon3")
        selectedAddOns.push(...addOnString.split(',').map((addon: string) => addon.trim()).filter(Boolean));
    }

    // Reconstruct signatures from multiple parts
    const signature1 = getPropertyValue('Signature1', 'rich_text') || '';
    const signature2 = getPropertyValue('Signature2', 'rich_text') || '';
    const signature3 = getPropertyValue('Signature3', 'rich_text') || '';

    return {
        clientId: getPropertyValue('ClientID', 'rich_text') || '',
        name: getPropertyValue('Name', 'title') || '',
        email: getPropertyValue('Email', 'email') || '',
        phone: getPropertyValue('Phone', 'phone_number') || '',
        companyName: getPropertyValue('Company', 'rich_text') || undefined,
        interestedService: getPropertyValue('Selected Package', 'select') || 'mvp',
        servicePrice: getPropertyValue('Package Price', 'number') || 0,
        estimatedBudget: getPropertyValue('Estimated Budget', 'number') || undefined,
        projectTimeline: getPropertyValue('Project Timeline', 'rich_text') || undefined,
        heardAbout: getPropertyValue('Heard About', 'rich_text') || undefined,
        description: getPropertyValue('Description', 'rich_text') || undefined,
        selectedAddOns,
        totalPrice: getPropertyValue('Package Price', 'number') || 0,
        proposalUrl: getPropertyValue('ProposalURL', 'url') || '',
        contractUrl: getPropertyValue('ContractURL', 'url') || '',
        urlPassword: getPropertyValue('URLPassword', 'rich_text') || '',
        status: (getPropertyValue('Status', 'select') || 'New Lead') as CustomerProposal['status'],
        priority: (getPropertyValue('Priority', 'select') || 'Medium') as CustomerProposal['priority'],
        createdDate: getPropertyValue('Created Date', 'date') || new Date().toISOString(),
        signatureDate: getPropertyValue('AcceptanceDate', 'date'),
        notes: getPropertyValue('Notes', 'rich_text') || undefined,
        internalComments: getPropertyValue('Internal Comments', 'rich_text') || undefined,
        signature1: signature1 || undefined,
        signature2: signature2 || undefined,
        signature3: signature3 || undefined
    };
}

// Create a new customer proposal in Notion
export async function createCustomerProposal(data: CreateProposalRequest): Promise<CustomerProposal> {
    try {
        const clientId = data.clientId || uuidv4();
        const urlPassword = generateUrlPassword();
        const { proposalUrl, contractUrl } = generateProposalUrls(clientId);

        const response = await notion.pages.create({
            parent: { database_id: CRM_DATABASE_ID },
            properties: {
                // Basic Information
                'Name': {
                    title: [{ text: { content: data.name } }]
                },
                'E-mail': {
                    email: data.email
                },
                'Company Name': {
                    rich_text: data.companyName ? [{ text: { content: data.companyName } }] : []
                },
                'Phone': {
                    phone_number: data.phone || null
                },

                // URLs and Security
                'Proposal URL': {
                    url: proposalUrl
                },
                'Contract URL': {
                    url: contractUrl
                },
                'URL Password': {
                    rich_text: [{ text: { content: urlPassword } }]
                },

                // Service Information
                'Interested Service': {
                    select: { name: data.interestedService }
                },
                'Service Price': {
                    number: data.estimatedBudget || 0
                },
                'Estimated Budget': {
                    number: data.estimatedBudget || null
                },
                'Project Timeline': {
                    rich_text: data.projectTimeline ? [{ text: { content: data.projectTimeline } }] : []
                },
                'Heard About': {
                    rich_text: data.heardAbout ? [{ text: { content: data.heardAbout } }] : []
                },
                'Description': {
                    rich_text: data.description ? [{ text: { content: data.description } }] : []
                },

                // Status and Priority
                'Status': {
                    select: { name: 'New Lead' }
                },
                'Priority': {
                    select: { name: 'Medium' }
                },

                // Timestamps
                'Created Date': {
                    date: { start: new Date().toISOString() }
                },

                // Notes
                'Notes': {
                    rich_text: data.notes ? [{ text: { content: data.notes } }] : []
                },

                // Client ID for tracking
                'ClientID': {
                    rich_text: [{ text: { content: clientId } }]
                }
            }
        });

        return {
            clientId,
            name: data.name,
            email: data.email,
            companyName: data.companyName,
            phone: data.phone,
            proposalUrl,
            contractUrl,
            urlPassword,
            interestedService: data.interestedService,
            servicePrice: data.estimatedBudget || 0,
            estimatedBudget: data.estimatedBudget,
            projectTimeline: data.projectTimeline,
            heardAbout: data.heardAbout,
            description: data.description,
            status: 'New Lead',
            priority: 'Medium',
            createdDate: new Date().toISOString(),
            notes: data.notes,
            selectedAddOns: data.selectedAddOns
        };
    } catch (error) {
        console.error('Error creating customer proposal:', error);
        throw new Error('Failed to create customer proposal');
    }
}

// Get customer proposal by Client ID
export async function getLeadByClientId(clientId: string): Promise<CustomerProposal | null> {
    try {
        const response = await notion.databases.query({
            database_id: CRM_DATABASE_ID,
            filter: {
                property: 'ClientID',
                rich_text: {
                    equals: clientId
                }
            }
        });

        if (response.results.length === 0) {
            return null;
        }

        const page = response.results[0] as any;
        const props = page.properties;

        return {
            clientId,
            name: props['Name']?.title?.[0]?.text?.content || '',
            email: props['E-mail']?.email || '',
            companyName: props['Company Name']?.rich_text?.[0]?.text?.content || undefined,
            phone: props['Phone']?.phone_number || undefined,
            proposalUrl: props['Proposal URL']?.url || '',
            contractUrl: props['Contract URL']?.url || '',
            urlPassword: props['URL Password']?.rich_text?.[0]?.text?.content || '',
            interestedService: props['Interested Service']?.select?.name || '',
            servicePrice: props['Service Price']?.number || 0,
            estimatedBudget: props['Estimated Budget']?.number || undefined,
            projectTimeline: props['Project Timeline']?.rich_text?.[0]?.text?.content || undefined,
            heardAbout: props['Heard About']?.rich_text?.[0]?.text?.content || undefined,
            description: props['Description']?.rich_text?.[0]?.text?.content || undefined,
            status: props['Status']?.select?.name as CustomerProposal['status'] || 'New Lead',
            priority: props['Priority']?.select?.name as CustomerProposal['priority'] || 'Medium',
            createdDate: props['Created Date']?.date?.start || '',
            signatureDate: props['Signature Date']?.date?.start || undefined,
            notes: props['Notes']?.rich_text?.[0]?.text?.content || undefined,
            internalComments: props['Internal Comments']?.rich_text?.[0]?.text?.content || undefined,
            signature1: props['Signature 1']?.rich_text?.[0]?.text?.content || undefined,
            signature2: props['Signature 2']?.rich_text?.[0]?.text?.content || undefined,
            signature3: props['Signature 3']?.rich_text?.[0]?.text?.content || undefined
        };
    } catch (error) {
        console.error('Error getting lead by client ID:', error);
        throw new Error('Failed to retrieve lead');
    }
}

// Update customer proposal
export async function updateCustomerProposal(
    clientId: string,
    updates: UpdateProposalRequest
): Promise<CustomerProposal> {
    try {
        // First, get the page ID
        const lead = await getLeadByClientId(clientId);
        if (!lead) {
            throw new Error('Lead not found');
        }

        // Find the page ID
        const response = await notion.databases.query({
            database_id: CRM_DATABASE_ID,
            filter: {
                property: 'ClientID',
                rich_text: {
                    equals: clientId
                }
            }
        });

        if (response.results.length === 0) {
            throw new Error('Lead not found');
        }

        const pageId = response.results[0].id;

        // Build update properties
        const updateProperties: any = {};

        if (updates.companyName !== undefined) {
            updateProperties['Company Name'] = {
                rich_text: updates.companyName ? [{ text: { content: updates.companyName } }] : []
            };
        }

        if (updates.phone !== undefined) {
            updateProperties['Phone'] = {
                phone_number: updates.phone || null
            };
        }

        if (updates.interestedService !== undefined) {
            updateProperties['Interested Service'] = {
                select: { name: updates.interestedService }
            };
        }

        if (updates.estimatedBudget !== undefined) {
            updateProperties['Estimated Budget'] = {
                number: updates.estimatedBudget
            };
            updateProperties['Service Price'] = {
                number: updates.estimatedBudget
            };
        }

        if (updates.projectTimeline !== undefined) {
            updateProperties['Project Timeline'] = {
                rich_text: updates.projectTimeline ? [{ text: { content: updates.projectTimeline } }] : []
            };
        }

        if (updates.heardAbout !== undefined) {
            updateProperties['Heard About'] = {
                rich_text: updates.heardAbout ? [{ text: { content: updates.heardAbout } }] : []
            };
        }

        if (updates.description !== undefined) {
            updateProperties['Description'] = {
                rich_text: updates.description ? [{ text: { content: updates.description } }] : []
            };
        }

        if (updates.notes !== undefined) {
            updateProperties['Notes'] = {
                rich_text: updates.notes ? [{ text: { content: updates.notes } }] : []
            };
        }

        if (updates.internalComments !== undefined) {
            updateProperties['Internal Comments'] = {
                rich_text: updates.internalComments ? [{ text: { content: updates.internalComments } }] : []
            };
        }

        if (updates.status !== undefined) {
            updateProperties['Status'] = {
                select: { name: updates.status }
            };
        }

        if (updates.priority !== undefined) {
            updateProperties['Priority'] = {
                select: { name: updates.priority }
            };
        }

        // Update the page
        await notion.pages.update({
            page_id: pageId,
            properties: updateProperties
        });

        // Return updated lead
        return await getLeadByClientId(clientId) as CustomerProposal;
    } catch (error) {
        console.error('Error updating customer proposal:', error);
        throw new Error('Failed to update customer proposal');
    }
}

// Upload signature to Notion (split into parts due to size limits)
export async function uploadSignatureToNotion(clientId: string, signature: string): Promise<void> {
    try {
        // Split signature into 3 parts (Notion has text limits)
        const chunkSize = Math.ceil(signature.length / 3);
        const signature1 = signature.slice(0, chunkSize);
        const signature2 = signature.slice(chunkSize, chunkSize * 2);
        const signature3 = signature.slice(chunkSize * 2);

        await updateCustomerProposal(clientId, {
            clientId,
            status: 'Won'
        });

        // Find the page ID and update signatures
        const response = await notion.databases.query({
            database_id: CRM_DATABASE_ID,
            filter: {
                property: 'ClientID',
                rich_text: {
                    equals: clientId
                }
            }
        });

        if (response.results.length === 0) {
            throw new Error('Lead not found');
        }

        const pageId = response.results[0].id;

        await notion.pages.update({
            page_id: pageId,
            properties: {
                'Signature 1': {
                    rich_text: [{ text: { content: signature1 } }]
                },
                'Signature 2': {
                    rich_text: [{ text: { content: signature2 } }]
                },
                'Signature 3': {
                    rich_text: [{ text: { content: signature3 } }]
                },
                'Signature Date': {
                    date: { start: new Date().toISOString() }
                }
            }
        });
    } catch (error) {
        console.error('Error uploading signature:', error);
        throw new Error('Failed to upload signature');
    }
}

// Verify URL password
export async function verifyUrlPassword(clientId: string, password: string): Promise<boolean> {
    try {
        const lead = await getLeadByClientId(clientId);
        return lead?.urlPassword === password;
    } catch (error) {
        console.error('Error verifying password:', error);
        return false;
    }
}

// Session management for password protection
export function createSession(clientId: string, password: string): string {
    const sessionData = { clientId, password, timestamp: Date.now() };
    return Buffer.from(JSON.stringify(sessionData)).toString('base64');
}

export function validateSession(sessionToken: string, clientId: string): boolean {
    try {
        const sessionData = JSON.parse(Buffer.from(sessionToken, 'base64').toString());
        const isValid = sessionData.clientId === clientId &&
            (Date.now() - sessionData.timestamp) < 24 * 60 * 60 * 1000; // 24 hours
        return isValid;
    } catch {
        return false;
    }
}

// Get all customer proposals (for admin panel)
export async function getAllCustomerProposals(): Promise<CustomerProposal[]> {
    if (!CRM_DATABASE_ID) {
        throw new Error('Notion CRM database ID is not configured');
    }

    try {
        const response = await notion.databases.query({
            database_id: CRM_DATABASE_ID,
            sorts: [
                {
                    property: 'Created Date',
                    direction: 'descending',
                },
            ],
        });

        return response.results.map(mapCrmLeadToCustomerProposal);
    } catch (error) {
        console.error('Error getting all customer proposals:', error);
        throw new Error('Failed to retrieve customer proposals');
    }
}

// Update lead status (legacy support)
export async function updateLeadStatus(
    clientId: string,
    status: string,
    addOns?: string[],
    comments?: string,
    contractUrl?: string
): Promise<void> {
    const updates: Partial<CustomerProposal> = {
        status: status as CustomerProposal['status'],
    };

    if (addOns) {
        updates.selectedAddOns = addOns;
    }

    if (comments) {
        updates.internalComments = comments;
    }

    if (contractUrl) {
        updates.contractUrl = contractUrl;
    }

    await updateCustomerProposal(clientId, updates as UpdateProposalRequest);
}

// Calculate total price utility
export function calculateTotalPrice(packageName: string, selectedAddOns: string[]): number {
    // This will be imported from services config
    // For now, return a placeholder
    return 0;
}

// Verify password for client access
export async function verifyClientPassword(clientId: string, password: string): Promise<boolean> {
    try {
        const proposal = await getLeadByClientId(clientId);
        return proposal?.urlPassword === password;
    } catch (error) {
        console.error('Error verifying password:', error);
        return false;
    }
}

// Create lead from contact form (enhanced version)
export async function createLeadFromContact(formData: {
    name: string;
    email: string;
    phone?: string;
    company?: string;
    message: string;
    selectedService?: string;
    estimatedBudget?: string;
    projectTimeline?: string;
    additionalServices?: string[];
    source?: string;
}): Promise<CustomerProposal> {
    const clientId = uuidv4();
    const urlPassword = generateUrlPassword();
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';

    const proposalUrl = `${baseUrl}/create-proposal/${clientId}`;
    const contractUrl = `${baseUrl}/contract/${clientId}`;

    const notesText = [
        formData.message,
        formData.estimatedBudget ? `Budget: ${formData.estimatedBudget}` : '',
        formData.projectTimeline ? `Timeline: ${formData.projectTimeline}` : '',
        formData.additionalServices?.length ? `Additional Services: ${formData.additionalServices.join(', ')}` : '',
        formData.source ? `Source: ${formData.source}` : ''
    ].filter(Boolean).join('\n\n');

    if (!CRM_DATABASE_ID) {
        throw new Error('Notion CRM database ID is not configured');
    }

    try {
        const response = await notion.pages.create({
            parent: {
                database_id: CRM_DATABASE_ID,
            },
            properties: {
                'Name': {
                    title: [{ text: { content: formData.name } }],
                },
                'Email': {
                    email: formData.email,
                },
                'Phone': {
                    phone_number: formData.phone || '',
                },
                'Company': {
                    rich_text: [{ text: { content: formData.company || '' } }],
                },
                'ClientID': {
                    rich_text: [{ text: { content: clientId } }],
                },
                'Selected Package': {
                    select: { name: formData.selectedService || 'mvp' },
                },
                'ProposalURL': {
                    url: proposalUrl,
                },
                'ContractURL': {
                    url: contractUrl,
                },
                'URLPassword': {
                    rich_text: [{ text: { content: urlPassword } }],
                },
                'Status': {
                    select: { name: 'New Lead' },
                },
                'Notes': {
                    rich_text: [{ text: { content: notesText } }],
                },
                'Project Description': {
                    rich_text: [{ text: { content: formData.message } }],
                },
                'Created Date': {
                    date: { start: new Date().toISOString() },
                },
                'Last Contact': {
                    date: { start: new Date().toISOString() },
                },
                'Priority': {
                    select: { name: 'Medium' },
                }
            },
        });

        return mapCrmLeadToCustomerProposal(response);
    } catch (error) {
        console.error('Error creating lead from contact:', error);
        throw new Error('Failed to create lead in CRM');
    }
} 