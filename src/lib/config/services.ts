// Innovatio-Pro Service Configuration
import { ServicePackage, AddOn } from '@/types/proposal';

// Hauptservices basierend auf den innovatio-pro Paketen
export const SERVICES = {
    mvp: {
        id: 'mvp',
        name: 'MVP Development',
        description: 'Launch your idea quickly with a Minimum Viable Product',
        basePrice: 8500,
        originalPrice: 10000,
        timeframe: '3-4 weeks',
        category: 'development',
        features: [
            'Core functionality implementation',
            'Modern UI/UX design',
            'User authentication & security',
            'Scalable data storage solution',
            'Cross-platform deployment',
            'Worldwide Compliance Ready',
            '3 months support included',
            'Performance optimization'
        ]
    },
    prototype: {
        id: 'prototype',
        name: 'Rapid Prototype',
        description: 'Test your concept with a functional prototype',
        basePrice: 4200,
        originalPrice: 5000,
        timeframe: '1-2 weeks',
        category: 'development',
        features: [
            'Interactive UI mockups',
            'Core functionality demo',
            'User flow implementation',
            'Stakeholder presentation',
            'Flutter-driven development',
            'Iteration feedback loops',
            'Technical documentation'
        ]
    },
    landingpage: {
        id: 'landingpage',
        name: 'Landing Page Development',
        description: 'WCAG 2.0 compliant landing pages with the latest web technologies',
        basePrice: 3000,
        originalPrice: 3500,
        timeframe: '2-4 weeks',
        category: 'web',
        features: [
            'WCAG 2.0 AA compliance',
            'Inclusive design for all users',
            'Screen reader compatibility',
            'High-performance metrics',
            'SEO optimized structure',
            'Responsive design',
            'Analytics integration',
            'Conversion optimization'
        ]
    },
    architecture: {
        id: 'architecture',
        name: 'Project Architecture',
        description: 'Solid foundation for your project\'s success',
        basePrice: 3800,
        originalPrice: 4500,
        timeframe: '1-2 weeks',
        category: 'consulting',
        features: [
            'Technical specifications',
            'System architecture design',
            'Database schema design',
            'API documentation',
            'Development roadmap',
            'Security planning',
            'Scalability guidelines',
            'Technology recommendations'
        ]
    },
    consulting: {
        id: 'consulting',
        name: 'Technical Consulting',
        description: 'Expert guidance for your technical decisions',
        basePrice: 110,
        originalPrice: 130,
        timeframe: 'Ongoing',
        category: 'consulting',
        isHourly: true,
        features: [
            'Technology stack recommendations',
            'Code reviews & quality assurance',
            'Performance optimization',
            'Security assessment',
            'Scalability planning',
            'Payment gateway integration',
            'Team mentoring',
            'Best practices implementation'
        ]
    },
    fullstack: {
        id: 'fullstack',
        name: 'Full-Stack Development',
        price: 12000,
        timeframe: '6-8 weeks',
        description: 'Complete end-to-end development solution with frontend, backend, and deployment.',
        features: [
            'Frontend & Backend development',
            'Database design & implementation',
            'API development & integration',
            'User authentication system',
            'Admin panel development',
            'Third-party integrations',
            'Cloud deployment & hosting',
            'Performance optimization'
        ],
        badge: 'Complete Solution',
        category: 'development'
    },
    homepage: {
        id: 'homepage',
        name: 'Professional Homepage',
        price: 4500,
        timeframe: '2-3 weeks',
        description: 'Professional corporate website with modern design and excellent user experience.',
        features: [
            'Responsive design',
            'SEO optimization',
            'Content management',
            'Contact forms',
            'Social media integration',
            'Analytics setup',
            'Performance optimization',
            'Security implementation'
        ],
        badge: 'Business Ready',
        category: 'development'
    }
} as const;

// Add-ons für innovatio-pro Services
export const ADD_ONS = [
    {
        id: 'professionalPhotos',
        name: 'Professional Photos & Media',
        description: 'High-quality photography and media content creation',
        price: 800,
        category: 'design' as const,
        compatibleServices: ['mvp', 'prototype', 'landingpage', 'architecture', 'consulting']
    },
    {
        id: 'crmAutomation',
        name: 'CRM & Email Automation',
        description: 'Complete customer relationship management setup',
        price: 1200,
        category: 'technical' as const,
        compatibleServices: ['mvp', 'prototype', 'landingpage', 'architecture', 'consulting']
    },
    {
        id: 'aiIntegration',
        name: 'AI Integration & Chatbots',
        description: 'Smart AI features and automated customer support',
        price: 2500,
        category: 'technical' as const,
        compatibleServices: ['mvp', 'prototype', 'landingpage', 'consulting']
    },
    {
        id: 'mobileOptimization',
        name: 'Advanced Mobile Optimization',
        description: 'Enhanced mobile performance and user experience',
        price: 600,
        category: 'technical' as const,
        compatibleServices: ['mvp', 'prototype', 'landingpage']
    },
    {
        id: 'seoPackage',
        name: 'Complete SEO Package',
        description: 'Comprehensive search engine optimization',
        price: 900,
        category: 'technical' as const,
        compatibleServices: ['mvp', 'prototype', 'landingpage']
    },
    {
        id: 'socialMediaIntegration',
        name: 'Social Media Integration',
        description: 'Full social media platform integration',
        price: 500,
        category: 'integration' as const,
        compatibleServices: ['mvp', 'prototype', 'landingpage']
    },
    {
        id: 'analyticsSetup',
        name: 'Advanced Analytics Setup',
        description: 'Comprehensive tracking and reporting setup',
        price: 700,
        category: 'technical' as const,
        compatibleServices: ['mvp', 'prototype', 'landingpage', 'consulting']
    },
    {
        id: 'securityAudit',
        name: 'Security Audit & Hardening',
        description: 'Complete security assessment and improvements',
        price: 1500,
        category: 'technical' as const,
        compatibleServices: ['mvp', 'prototype', 'landingpage', 'architecture', 'consulting']
    },
    {
        id: 'performanceOptimization',
        name: 'Performance Optimization',
        description: 'Speed and performance enhancements',
        price: 800,
        category: 'technical' as const,
        compatibleServices: ['mvp', 'prototype', 'landingpage', 'consulting']
    },
    {
        id: 'maintenancePackage',
        name: '6-Month Maintenance Package',
        description: 'Extended support and maintenance coverage',
        price: 1800,
        category: 'support' as const,
        compatibleServices: ['mvp', 'prototype', 'landingpage', 'architecture', 'consulting']
    },
    {
        id: 'trainingWorkshop',
        name: 'Team Training Workshop',
        description: 'Comprehensive training for your team',
        price: 1200,
        category: 'support' as const,
        compatibleServices: ['mvp', 'prototype', 'landingpage', 'architecture', 'consulting']
    },
    {
        id: 'customIntegration',
        name: 'Custom Third-Party Integration',
        description: 'Integration with specific external systems',
        price: 2000,
        category: 'integration' as const,
        compatibleServices: ['mvp', 'prototype', 'landingpage', 'architecture', 'consulting']
    }
];

// Budget Ranges für das Contact Form
export const BUDGET_RANGES = [
    { value: 'under_5k', label: 'Under €5,000', min: 0, max: 5000 },
    { value: '5k_10k', label: '€5,000 - €10,000', min: 5000, max: 10000 },
    { value: '10k_20k', label: '€10,000 - €20,000', min: 10000, max: 20000 },
    { value: '20k_50k', label: '€20,000 - €50,000', min: 20000, max: 50000 },
    { value: 'over_50k', label: 'Over €50,000', min: 50000, max: Infinity },
    { value: 'not_sure', label: 'Not sure yet', min: 0, max: 0 }
];

// Timeline Options
export const TIMELINE_OPTIONS = [
    { value: 'asap', label: 'As soon as possible' },
    { value: '1_month', label: 'Within 1 month' },
    { value: '2_3_months', label: '2-3 months' },
    { value: '3_6_months', label: '3-6 months' },
    { value: '6_months_plus', label: 'More than 6 months' },
    { value: 'planning', label: 'Still planning' }
];

// Source Options (Wie haben sie von uns erfahren)
export const SOURCE_OPTIONS = [
    { value: 'google', label: 'Google Search' },
    { value: 'social_media', label: 'Social Media' },
    { value: 'referral', label: 'Referral from friend/colleague' },
    { value: 'linkedin', label: 'LinkedIn' },
    { value: 'github', label: 'GitHub' },
    { value: 'portfolio', label: 'Portfolio website' },
    { value: 'event', label: 'Event/Conference' },
    { value: 'other', label: 'Other' }
];

// Utility Functions
export const getServiceById = (serviceId: string) => {
    return SERVICES[serviceId as keyof typeof SERVICES];
};

export const getServicePrice = (serviceId: string, isHourly = false) => {
    const service = getServiceById(serviceId);
    if (!service) return 0;
    // Handle both old and new price structures for backward compatibility
    return 'basePrice' in service ? service.basePrice : service.price;
};

export const getAllServices = () => {
    return Object.values(SERVICES);
};

export const getServicesByCategory = (category: string) => {
    return Object.values(SERVICES).filter(service => service.category === category);
};

export const getAddOnById = (addOnId: string) => {
    return ADD_ONS.find(addon => addon.id === addOnId);
};

export const getCompatibleAddOns = (serviceId: string) => {
    return ADD_ONS.filter(addon =>
        addon.compatibleServices.includes(serviceId)
    );
};

export const calculateTotalPrice = (serviceId: string, selectedAddOnIds: string[]): number => {
    const service = getServiceById(serviceId);
    if (!service) return 0;

    // Handle both old and new price structures for backward compatibility
    const basePrice = 'basePrice' in service ? service.basePrice : service.price;
    const addOnPrice = selectedAddOnIds.reduce((total, addOnId) => {
        const addOn = getAddOnById(addOnId);
        return total + (addOn?.price || 0);
    }, 0);

    return basePrice + addOnPrice;
};

export const formatPrice = (price: number): string => {
    return new Intl.NumberFormat('de-DE', {
        style: 'currency',
        currency: 'EUR',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(price);
};

// Format service name for Notion (matches the select options)
export const formatServiceForNotion = (serviceId: string) => {
    const service = getServiceById(serviceId);
    return service ? service.name : serviceId;
};

// Get all service names for Notion select field
export const getNotionServiceOptions = () => {
    return Object.values(SERVICES).map(service => service.name);
};

// Service Categories for filtering
export const SERVICE_CATEGORIES = {
    development: 'Development',
    consulting: 'Consulting',
    design: 'Design'
} as const;

// Popular service combinations
export const POPULAR_COMBINATIONS = [
    {
        name: 'Startup Package',
        services: ['prototype', 'mvp'],
        addOns: ['seo_optimization', 'analytics_dashboard'],
        description: 'Perfect for new startups looking to validate and launch their idea'
    },
    {
        name: 'Business Package',
        services: ['homepage', 'crm_automation'],
        addOns: ['professional_photos', 'online_booking'],
        description: 'Complete business presence with customer management'
    },
    {
        name: 'Enterprise Package',
        services: ['fullstack', 'architecture'],
        addOns: ['ai_integration', 'advanced_security', 'priority_support'],
        description: 'Full-scale enterprise solution with advanced features'
    }
]; 