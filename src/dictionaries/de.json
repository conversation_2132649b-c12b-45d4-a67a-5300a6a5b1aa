{"nav": {"home": "Startseite", "about": "Über uns", "solutions": "Lösungen", "services": "Dienstleistungen", "techStack": "Tech Stack", "portfolio": "Portfolio", "contact": "Kontakt", "chatWithUs": "Chat mit uns", "bookConsultation": "Beratung buchen", "apps": "Apps", "pricing": "<PERSON><PERSON>", "prices": "<PERSON><PERSON>", "calculator": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "priceCalculator": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "clients": "<PERSON><PERSON>", "testimonials": "Referenzen", "blog": "Blog"}, "hero": {"title": "🚀 <PERSON><PERSON><PERSON> – Schnell. Skalierbar. Profitabel.", "subtitle": "Von der Idee zum App Store in Rekordzeit. Wir entwickeln MVPs, KI-fähige Apps & vollständige Produkte.", "tagline": "8 <PERSON><PERSON><PERSON> Erfahrung, 15+ erfolgreiche Projekte, 100% Kundenzufriedenheit", "description": "<PERSON><PERSON><PERSON><PERSON> andere monatelang planen, entwickle ich. <PERSON><PERSON><PERSON><PERSON>, direkt, ohne Umwege. Festpreis, höchste Qualität, pünktliche Lieferung.", "typing": {"sequence1": "App-Idee → Entwicklung → App Store ✓", "sequence2": "Festpreis. Höchste Qualität. Null Risiko.", "sequence3": "Von TOGG bis Ultimind vertrauen mir."}, "cta": {"primary": "Projekt starten", "secondary": "Festpreis in 24h erhalten", "calculator": "Sofort-Preischeck (60 Sek.)", "startProject": "Projekt starten", "freeConsultation": "Kostenlose Beratung", "calculateCost": "<PERSON><PERSON>"}, "trustedBy": {"title": "<PERSON><PERSON><PERSON><PERSON> von <PERSON>"}, "typeAnimation": ["Flutter Apps - Ein Code für iOS & Android 📱", 2000, "Native Performance - Moderne UI/UX 🚀", 2000, "Von der Idee bis zum App Store 🏆", 2000, "Ihr Partner für erfolgreiche Apps ✨", 2000, "", 500], "metrics": {"development": {"label": "Schnellere Entwicklung", "value": "40%", "description": "schnellere Entwicklung", "howTitle": "Wie:", "howExplanation": "Nutzung neuester IDE mit KI-Unterstützung, automatisierter Codegenerierung und intelligenter Code-Vervollständigung."}, "timeToMarket": {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "50%", "description": "schnellere Veröffentlichung", "howTitle": "Wie:", "howExplanation": "Fast Lane CI/CD-Pipeline, automatisierte Tests, KI-gestützte Code-Reviews und optimierte Bereitstellungsprozesse."}, "costSaving": {"label": "Kosteneinsparung", "value": "30%", "description": "Kostenersparnis", "howTitle": "Wie:", "howExplanation": "Ein<PERSON>z von Flutter für plattformübergreifende Entwicklung, optimierte Cloud-Ressourcen und effiziente Entwicklungspraktiken."}}}, "about": {"title": "Warum ich?", "subtitle": "8 <PERSON><PERSON><PERSON>, 15+ <PERSON><PERSON>, 0 gescheiterte Projekte", "vision": "<PERSON><PERSON>", "visionDesc": "2016 war ich frustriert: Apps kosteten 50.000€ und dauerten ein Jahr. Heute bringe ich für 8.500€ MVPs in 4 Wochen live. Kunden wie Union Investment und TOGG vertrauen mir, weil ich liefere was ich verspreche.", "mission": "<PERSON><PERSON>", "missionDesc": "Ihre App kommt pünktlich und im Budget. Punkt. Ich arbeite persönlich an jedem Projekt - kein Team von Juniors, keine Überraschungen. Flutter ist mein täglich Brot seit 5 Jahren.", "founderTitle": "<PERSON>", "founderDesc": "Ich lebe Code. Seit 8 Jahren entwickle ich Apps für Startups bis Konzerne. Meine Leidenschaft: Unmögliche Deadlines möglich machen. Zuletzt: <PERSON><PERSON>-MVP in 3 Wochen statt 3 Monaten.", "skills": "Fähigkeiten", "projects": "Projekte", "testimonials": "Referenzen", "experience": "<PERSON><PERSON><PERSON>", "clients": "Zufriedene Kunden", "transformBusiness": "Unternehmen durch Technologie transformieren", "createSolutions": "Zukunftssichere digitale Lösungen schaffen", "stayingAhead": "An der Spitze der Technologie bleiben", "exceptionalUX": "Außergewöhnliche Benutzererfahrungen bieten", "highPerformance": "Hochleistungsfähige Anwendungen entwickeln", "solvingChallenges": "Reale Geschäftsherausforderungen mit Technologie lösen", "flutterExpert": "Experte", "webDevAdvanced": "Fortgeschritten", "aiIntegration": "Integration", "projectsCompleted": "Projekte Abgeschlossen", "personalDedication": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dedication": "<PERSON><PERSON><PERSON>", "qualityFocused": "Qualitätsorientiert", "personalService": "Persönlicher Service", "focusedApproach": "Fokussierter Ansatz", "dedicatedService": "Engagierter Service", "clientReviews": "Kundenbewertungen", "focusedService": "Fokussierter Service", "longTerm": "Lang<PERSON><PERSON><PERSON>", "partnerships": "Partnerschaften", "privacyFocused": "Datenschutzorientiert", "secureServices": "<PERSON><PERSON><PERSON> Entwicklung", "personalAttention": "Persönliche Betreuung", "dedicatedDeveloper": "Engagierter Entwickler", "securityFocused": "Sicherheitsorientiert", "privacyRespected": "Datenschutz respektiert", "quality": "Qualität", "averageDelivery": "Durchschnittliche Lieferzeit", "metrics": {"yearsExperience": "8+", "projectsCompleted": "15+", "happyClients": "12+", "clientRating": "5,0/5", "deliveryTimeframe": "3-4 W<PERSON>en", "personalValue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "qualityValue": "Qualität", "longTermValue": "Lang<PERSON><PERSON><PERSON>"}}, "advantages": {"title": "UNSERE LÖSUNGEN", "subtitle": "Wie wir Ihnen helfen, erfolgreiche mobile Apps zu entwickeln", "speed": "<PERSON><PERSON><PERSON>", "speedDesc": "Wir liefern Lösungen schnell, ohne Kompromisse bei der Qualität", "stability": "Zuverlässige Anwendungen", "stabilityDesc": "Unsere Anwendungen sind auf Stabilität und Leistung ausgelegt", "cost": "Kosteneffizienz", "costDesc": "Optimierter Entwicklungsprozess spart Zeit und Geld", "timeToMarket": "Schnellere Markteinführung", "timeToMarketDesc": "Bringen Sie Ihr Produkt schnell auf den Markt und bleiben Sie der Konkurrenz voraus", "aiIntegration": "KI-Integration", "aiIntegrationDesc": "Verbessern Sie Ihr Unternehmen mit leistungsstarken KI-Funktionen", "development": "Full-Stack-Entwicklung", "developmentTime": "4-12 <PERSON><PERSON><PERSON>, variiert je nach Komplexität", "developmentDesc": "Komplette mobile Anwendungsentwicklung vom Konzept bis zur Bereitstellung, mit vollständiger Backend-Integration und erweiterten Funktionen.", "mvp": "MVP-<PERSON><PERSON><PERSON><PERSON>", "mvpTime": "2-4 <PERSON><PERSON><PERSON>, variiert je nach Komplexität", "mvpDesc": "Starten Sie schnell mit einem Minimum Viable Product, das Kernfunktionalitäten bietet, um Ihr Konzept zu validieren und frühe Nutzer oder Investoren anzuziehen.", "prototype": "Schnelles Prototyping", "prototypeTime": "1-2 <PERSON><PERSON><PERSON>, variiert je nach Komplexität", "prototypeDesc": "Testen Sie Konzepte schnell mit interaktiven Prototypen, bevor <PERSON> sich für die vollständige Entwicklung entscheiden und sparen Sie Zeit und Ressourcen.", "qa": "Qualitätssicherung", "qaTime": "Fortlau<PERSON>d, skaliert mit der Projektkomplexität", "qaDesc": "Umfassende Tests auf allen Geräten und Plattformen, um sicherzustellen, dass Ihre App mit automatisierten und manuellen Testprotokollen einwandfrei funktioniert.", "consulting": "Technische Beratung", "consultingTime": "<PERSON><PERSON>, basierend auf der Projektkomplexität", "consultingDesc": "Expertenberatung zu Technologie-Stack, Architekturentscheidungen und Implementierungsstrategien zur Optimierung Ihrer mobilen Anwendung.", "uiux": "UI/UX-Design", "uiuxTime": "2-3 <PERSON><PERSON><PERSON>, variiert je nach Komplexität", "uiuxDesc": "Nutzerzentriertes Design, das Ästhetik mit Funktionalität verbindet und intuitive und ansprechende mobile Erlebnisse schafft.", "maintenance": "Wartung & Support", "maintenanceTime": "Fortlau<PERSON>d, skaliert mit der Projektkomplexität", "maintenanceDesc": "Langfristiger Support mit regelmäßigen Updates, Leistungsoptimierung und Funktionserweiterungen, um Ihre App wettbewerbsfähig zu halten.", "analytics": "Analytics-Integration", "analyticsTime": "1-2 <PERSON><PERSON><PERSON>, variiert je nach Komplexität", "analyticsDesc": "Implementierung von Datenerfassung, um umsetzbare Einblicke in das Nutzerverhalten zu gewinnen und datengestützte Entscheidungen für Ihre App zu ermöglichen.", "training": "Team-<PERSON><PERSON><PERSON>", "trainingTime": "1-2 <PERSON><PERSON><PERSON>, variiert je nach Komplexität", "trainingDesc": "Umfassende Schulung für Ihr Team zur Wartung und Erweiterung Ihrer Anwendung nach der Übergabe.", "developmentEfficiency": "Entwicklungseffizienz", "timeToMarketReduction": "Reduzierung der Markteinführungszeit", "conceptValidation": "Konzeptvalidierung", "bugFreeRate": "Fehlerfreie Rate", "technicalImprovement": "Technische Verbesserung", "userSatisfaction": "Benutzerzufriedenheit", "appUptime": "App-Betriebszeit", "dataAccuracy": "Datengenauigkeit", "knowledgeRetention": "Wissensspeicherung", "developmentInfo": {"title": "Entwicklungszeiträume", "simpleApp": {"title": "Einfache App", "examples": "Beispiele: <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, einfache Informations-A<PERSON> ohne Backend.", "features": ["<PERSON>ige Screens (3-5)", "<PERSON><PERSON> oder minimale Backend-Integration", "Standard-UI-Komponenten", "<PERSON>ine komplexen Animationen oder Funktionen"], "timeline": {"total": "Entwicklungszeit: 4-8 W<PERSON>en", "frontend": "Frontend: 2-4 <PERSON><PERSON><PERSON>", "backend": "Backend (falls benötigt): 1-2 <PERSON><PERSON><PERSON>", "testing": "Testing und Deployment: 1-2 W<PERSON>en"}}, "mediumApp": {"title": "<PERSON><PERSON><PERSON>", "examples": "Beispiele: E-Commerce-Apps, Social-Media-Apps mit grundlegenden Funktionen, Apps mit Benutzerregistrierung und Datenbankintegration.", "features": ["6-15 Screens", "Backend-Integration (z. B. REST- oder GraphQL-APIs)", "Benutzerregistrierung und Authentifizierung", "Datenbank für Benutzer- und App-Daten", "Einige Animationen und interaktive Elemente", "Push-Benachrichtigungen"], "timeline": {"total": "Entwicklungszeit: 8-16 <PERSON><PERSON><PERSON>", "frontend": "Frontend: 4-6 <PERSON><PERSON><PERSON>", "backend": "Backend: 3-5 <PERSON><PERSON><PERSON>", "testing": "Testing und Deployment: 2-3 <PERSON><PERSON><PERSON>"}}, "complexApp": {"title": "Komplexe App", "examples": "Beispiele: <PERSON><PERSON> wie Uber, Instagram, oder Banking-Apps mit erweiterten Funktionen.", "features": ["15+ Screens", "Hochgradig interaktive Benutzeroberfläche", "Echtzeit-Funktionen (z. B. Live-Tracking, Chat)", "<PERSON> von <PERSON>-APIs (z. B. Zahlungs-Gateways, Karten-APIs)", "<PERSON><PERSON><PERSON><PERSON><PERSON> Backend mit Cloud-Integration", "Sicherheitsfunktionen (z. B. Verschlüsselung, Zwei-Faktor-Authentifizierung)", "Offline-Funktionalität"], "timeline": {"total": "Entwicklungszeit: 16-32 Wochen oder länger", "frontend": "Frontend: 6-10 <PERSON><PERSON><PERSON>", "backend": "Backend: 6-12 <PERSON><PERSON><PERSON>", "testing": "Testing und Deployment: 4-6 <PERSON><PERSON><PERSON>"}}, "factors": {"title": "<PERSON><PERSON><PERSON><PERSON>, die die Entwicklungszeit beeinflussen", "teamSize": "Teamgröße: Ein größeres Team (z. B. separate Entwickler für Frontend, Backend, und QA) kann die Entwicklung beschleunigen. Ein einzelner Entwickler benötigt mehr Zeit.", "technology": "Technologie: Native Entwicklung (z. <PERSON><PERSON> Swift für iOS, <PERSON><PERSON><PERSON> für Android) dauert oft länger als plattformübergreifende Ansätze wie Flutter oder React Native. Plattformübergreifende Frameworks können die Entwicklungszeit um 30-40 % reduzieren.", "requirements": "Anforderungen und Änderungen: Häufige Änderungen oder unklare Anforderungen können die Entwicklungszeit verlängern.", "testing": "Testing und Debugging: Komplexe Apps erfordern mehr Zeit für Tests, insbesondere bei mehreren Plattformen (iOS und Android).", "design": "Design: Einfache Designs benötigen weniger Zeit, während maßgeschneiderte, animierte Designs die Entwicklungszeit erhöhen."}, "summary": "Zusammenfassung: Einfache App: 4-8 Wochen. Mittlere App: 8-16 Wochen. Komplexe App: 16-32 Wochen oder länger.", "aiComparison": "Mit unserer KI-gestützten Entwicklung und Flutter können wir diese Zeiträume um 40-60% reduzieren, während wir hohe Qualität und Leistung beibehalten."}}, "serviceSection": {"title": "Unsere Dienstleistungen", "subtitle": "Maßgeschneiderte digitale Lösungen für Ihr Unternehmen", "description": "Wir bieten eine umfassende Palette digitaler Dienstleistungen an, um Unternehmen in der heutigen wettbewerbsintensiven Landschaft zum Erfolg zu verhelfen. Unsere Expertise erstreckt sich über mehrere Bereiche, um innovative und effektive Lösungen zu liefern.", "viewAll": "Ihr Projekt besprechen", "mobileApps": {"title": "Mobile App-Entwicklung", "description": "Plattformübergreifende mobile Anwendungen mit nativer Leistung mit Flutter, die nahtlose Erlebnisse auf iOS- und Android-Geräten bieten.", "benefits": ["40% schnellere Markteinführung im Vergleich zur nativen Entwicklung", "Einheitliche Codebasis für iOS- und Android-Plattformen", "Native Leistung und schöne Benutzeroberfläche"]}, "webDev": {"title": "Webentwicklung", "description": "Moderne, responsive Websites und Webanwendungen, die mit modernsten Technologien wie Next.js, React und Node.js entwickelt werden.", "benefits": ["SEO-optimierte Leistung mit serverseitigem Rendering", "Responsive Designs, die auf allen Geräten funktionieren", "Skalierbare Architektur für zukünftiges Wachstum"]}, "uiuxDesign": {"title": "UI/UX-Design", "description": "Intuitive, nutzerzentrierte Designs, die Kunden begeistern und das Engagement durch durchdachte Benutzererfahrungen verbessern.", "benefits": ["Nutzerzentrierter Design-Ansatz", "Konversionsoptimierte Schnittstellen", "Konsistente Markenerfahrung auf allen Plattformen"]}, "consulting": {"title": "Technische Beratung", "description": "Expertenberatung zu Technologiestrategie und -implementierung, um Ihnen zu helfen, fundierte Entscheidungen für Ihre digitalen Projekte zu treffen.", "benefits": ["Empfehlungen zum Technologie-Stack", "Architekturplanung und -überprüfung", "Leistungs- und Sicherheitsoptimierung"]}, "aiSolutions": {"title": "KI-Integration", "description": "Integrieren Sie KI-Funktionen, um Ihre Geschäftsanwendungen mit intelligenten Funktionen und Automatisierung zu verbessern.", "benefits": ["Personalisierte Benutzererfahrungen", "Automatisierte Arbeitsabläufe und Prozesse", "Datengesteuerte Erkenntnisse und Vorhersagen"]}, "prototype": {"title": "Rapid Prototyping", "description": "Unsicher ob Ihre Idee funktioniert? Für 4.200€ baue ich Ihnen einen klickbaren Prototyp. Damit können Si<PERSON> testen, Investoren zeigen oder Feedback sammeln - bevor Sie voll investieren.", "benefits": ["<PERSON><PERSON>barer Prototyp - fühlt sich an wie eine echte App", "Perfekt für Investor-Präsentationen und User-Tests", "<PERSON>nn später zum vollen MVP ausgebaut werden"]}, "mvp": {"title": "MVP-Entwicklung Express", "description": "Sie brauchen schnell eine funktionierende App für Investoren oder erste Kunden? Ich baue Ihnen ein vollwertiges MVP mit allem was nötig ist: Login, Datenbank, App Store Upload. Schneller als jede Agentur.", "benefits": ["Festpreis 8.500€ - keine Extras, keine Überraschungen", "App Store bereit - ich übernehme sogar den Upload", "Regelmäßige Updates - <PERSON><PERSON> se<PERSON> den Fortschritt live"]}, "fullstack": {"title": "Komplette App-Entwicklung", "description": "Sie wollen die volle App mit allen Features? Von der Datenbank bis zum Design mache ich alles selbst. Kein Team von Juniors - nur ich persönlich an Ihrem Projekt.", "benefits": ["Ein Ansprechpartner für alles - Frontend, Backend, Design", "Flutter + Firebase = bewährte Technologie, die funktioniert", "Persönlicher WhatsApp-Support während der Entwicklung"]}, "homepage": {"title": "Homepage & Landing Pages", "description": "Professionelle Websites und Landing Pages, die Ihre Arbeit präsentieren und Besucher in Kunden umwandeln.", "timeframe": "2-3 Wochen", "benefits": ["Konversionsoptimiertes Design", "SEO-freundliche Architektur", "Mobil-responsive Layouts"]}, "landingpage": {"title": "Barrierefreie Landing Pages", "description": "WCAG 2.0-konforme Landing-Pages mit neuesten Webtechnologien für maximale Zugänglichkeit und Leistung.", "timeframe": "2-4 W<PERSON>en", "benefits": ["WCAG 2.0 AA-Konformität", "Inklusives Design für alle Nutzer", "Screenreader-Kompatibilität", "Hohe Performance-Metriken"]}, "qa": {"title": "Qualitätssicherung", "description": "Umfassende Tests auf verschiedenen Geräten und Plattformen, um sicherzustellen, dass Ihre App einwandfrei mit automatisierten und manuellen Tests funktioniert.", "timeframe": "Fortlaufend", "benefits": ["Fehlerfreie Benutzererfahrung", "Leistungsoptimierung", "Plattformübergreifende Kompatibilität"]}}, "services": {"title": "Unsere Technologie-Stack", "subtitle": "Umfassende digitale Lösungen", "description": "Wir nutzen modernste Technologien in verschiedenen Bereichen, um robuste und skalierbare Lösungen für Ihre geschäftlichen Anforderungen zu liefern.", "frontend": "Frontend-Entwicklung", "frontendDesc": "Entwicklung responsiver und interaktiver Benutzeroberflächen mit modernen Web-Technologien", "backend": "Backend-Entwicklung", "backendDesc": "Erstellung robuster serverseitiger Lösungen und APIs für skalierbare Anwendungen", "mobile": "Mobile Entwicklung", "mobileDesc": "Entwicklung plattformübergreifender mobiler Anwendungen mit nativer Leistung", "ai": "KI & Maschinelles Lernen", "aiDesc": "Integration intelligenter Funktionen und Automatisierung in Anwendungen", "mobileApps": "Mobile App-Entwicklung", "mobileAppsDesc": "Plattformübergreifende Anwendungen mit Flutter für iOS und Android", "webDev": "Webentwicklung", "webDevDesc": "Moderne, responsive Websites und Webanwendungen", "uiuxDesign": "UI/UX-Design", "uiuxDesignDesc": "Intuitive, benutzerzentrierte Designs, die Kunden begeistern", "consulting": "Technische Beratung", "consultingDesc": "Expertenberatung zu Technologiestrategie und Implementierung", "aiSolutions": "KI-Integration", "aiSolutionsDesc": "Integrieren Sie KI-Funktionen zur Verbesserung Ihres Unternehmens", "viewAll": "Alle Dienstleistungen anzeigen"}, "prices": {"title": "Unsere Preise", "subtitle": "Transparente Preise für jede Phase", "description": "Wählen Sie aus unseren vordefinierten Paketen oder passen Sie sie an Ihre Projektanforderungen an. Kombinieren Sie Dienstleistungen für 15% Rabatt.", "caseStudyTitle": "Real-Case: PayFlow MVP - Blitzschnell statt monatelang", "caseStudyDescription": "Startup aus Wyoming brauchte dringend ein funktionsfähiges Fintech-MVP für ihre Series-A Runde. Ihre vorherige Agentur wollte Monate + 50.000€. Ich habe es express für 8.500€ gemacht. Sie bekamen ihr Investment - und ich einen Bonusauftrag.", "promotionTitle": "RISIKO-FREI: Geld-zurück-Garantie", "promotionDescription": "Ihre App entspricht nicht der Vereinbarung? Sie erhalten 100% Ihres Geldes zurück. Ich bin so sicher in meiner Qualität, dass ich dieses Risiko übernehme. In 8 Jahren musste ich das noch nie machen.", "calculatorTitle": "Sofort-Preischeck in 60 Sekunden", "calculatorDescription": "Festpreis sofort, keine Überraschungen. Angebot 7 Tage gültig, kostenlose Beratung inklusive.", "calculatorButton": "Mein Festp<PERSON> (60 Sek.)", "discussButton": "<PERSON><PERSON><PERSON> (30 Min.)", "contactButton": "WhatsApp mir direkt", "pricingDisclaimer": "* Preise können je nach Projektanforderungen und zusätzlichen Leistungen variieren. Kontaktieren Sie uns für ein individuelles Angebot, das auf Ihre spezifischen Bedürfnisse zugeschnitten ist.", "disclaimer": "* Preise können je nach Projektanforderungen und zusätzlichen Leistungen variieren. Kontaktieren Sie uns für ein individuelles Angebot, das auf Ihre spezifischen Bedürfnisse zugeschnitten ist.", "contactUs": "Kontaktieren Sie uns", "priceVariesInfo": "Der Preis kann je nach Projektkomplexität, zusätzlichen Anforderungen und Zeitvorgaben variieren. Kontaktieren Sie uns für ein detailliertes Angebot.", "calculator": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitle": "Erhalten Sie ein individuelles Angebot für Ihre Bedürfnisse", "back": "Zurück", "selectServiceType": "Dienstleistungstyp auswählen", "stepIndicator": "<PERSON><PERSON><PERSON>", "continueButton": "<PERSON><PERSON>", "getQuoteButton": "Ang<PERSON><PERSON> erhalten", "startOver": "<PERSON>eu starten", "calculatorTypes": {"mvp": {"name": "MVP-<PERSON><PERSON><PERSON><PERSON>", "description": "Berechnen Sie die Kosten für die Entwicklung Ihres Minimum Viable Product", "tagline": "Bringen Sie Ihre App schnell mit Kernfunktionen auf den Markt"}, "prototype": {"name": "Schneller Prototyp", "description": "Schätzen Sie Ihre Prototyp-Entwicklungskosten", "tagline": "Testen Sie Ihre Idee mit einem funktionalen Prototyp"}, "homepage": {"name": "Homepage & Landing Page", "description": "Erhalten Sie Preise für Ihre Website oder Landing Page", "tagline": "Erstellen Sie eine professionelle Online-Präsenz"}, "consulting": {"name": "Technische Beratung", "description": "Schätzen Sie Beratungsstunden und -pakete", "tagline": "Expertenberatung für Ihre technischen Herausforderungen"}}, "mvpCalculator": {"title": "MVP-Entwick<PERSON>s<PERSON>chner", "subtitle": "Konfigurieren Sie Ihre MVP-Anforderungen für ein individuelles Angebot", "industry": "Wählen Sie Ihre Branche", "timeline": "Entwicklungszeitplan", "features": "App-Funktionen auswählen", "featuresInfo": "<PERSON><PERSON>hlen Sie die Funktionen, die Sie für Ihr <strong>voll funktionsfähiges, App Store-bereites</strong> MVP ben<PERSON><PERSON><PERSON>. Dies sind Ihre Kernfunktionen.", "addFeatures": "<PERSON><PERSON><PERSON>, um Funktionen hinzuzufügen...", "customFeatures": "Zusätzliche Anforderungen", "customFeaturesPlaceholder": "Beschreiben Sie alle benutzerdefinierten Funktionen oder Anforderungen...", "needsReview": "Ich möchte eine Code-Überprüfung meines bestehenden Projekts", "industries": {"ecommerce": "E-Commerce", "health": "Gesundheitswesen", "finance": "Fin<PERSON>zen", "social": "Soziale Medien", "productivity": "Produktivität", "other": "<PERSON><PERSON>"}, "timelines": {"normal": {"label": "Standard (4-5 Wochen)", "description": "Reguläres Entwicklungstempo"}, "fast": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> (3-4 W<PERSON>en)", "description": "30% schnellere Lieferung"}, "urgent": {"label": "Priorität (2-3 Wochen)", "description": "Dediziertes Team, schnellste Lieferung"}}, "featureCategories": {"auth": "Authentifizierung", "user": "Benutzerverwaltung", "common": "Allgemeine Funktionen", "payment": "Zahlung", "device": "Gerätefunktionen", "data": "Daten & Speicher", "social": "Soziale Funktionen"}}, "prototypeCalculator": {"title": "Schneller Prototyp-<PERSON><PERSON>ner", "subtitle": "Konfigurieren Sie Ihre Prototyp-Anforderungen für einen visuellen und navigierbaren Prototyp", "uiOnly": "Nur UI", "timeline": "Entwicklungszeitplan", "complexity": "Prototyp-Komplexität", "userRoles": "Anzahl der Benutzerrollen", "userRolesBasic": "Einfach (1)", "userRolesComplex": "Komplex (5+)", "userRolesExamples": "Beispiele: <PERSON><PERSON><PERSON>, Administrator, Editor, <PERSON><PERSON><PERSON><PERSON>, Moderator", "features": "Prototyp-Funktionen auswählen", "featuresInfo": "<PERSON>ählen Sie die Funktionen, die Sie für Ihren visuellen Prototyp benötigen. Diese definieren den Umfang Ihres Prototyps.", "addFeatures": "<PERSON><PERSON><PERSON>, um Funktionen hinzuzufügen...", "integrations": "UI-Integrationen", "integrationsInfo": "<PERSON><PERSON>hlen Sie die UI-Integrationen, die in Ihren Prototyp aufgenommen werden sollen.", "addIntegrations": "<PERSON><PERSON><PERSON>, um Integrationen hinzuzufügen...", "customFeatures": "Zusätzliche Anforderungen", "customFeaturesPlaceholder": "Beschreiben Sie alle benutzerdefinierten Funktionen oder Anforderungen...", "needsReview": "Ich möchte eine Design-Überprüfung meiner bestehenden Mockups", "flutterPrototype": "Flutter-Powered Prototyp", "flutterInfo": "Unsere Flutter-Prototypen bieten überlegene Leistung und können später leicht in ein vollständiges MVP umgewandelt werden.", "complexityLevels": {"simple": {"label": "<PERSON><PERSON><PERSON>", "description": "Grundlegende Benutzerflüsse, minimale Bildschirme"}, "medium": {"label": "<PERSON><PERSON><PERSON>", "description": "Mehrere Benutzerflüsse, Standardbildschirme"}, "complex": {"label": "Komplex", "description": "Fortgeschrittene UI, viele Bildschirme"}}, "timelines": {"normal": {"label": "Standard (1-2 Wochen)", "description": "Reguläres Entwicklungstempo"}, "fast": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> (5-7 Tage)", "description": "30% schnellere Lieferung"}, "urgent": {"label": "Priorität (3-4 Tage)", "description": "Dediziertes Team, schnellste Lieferung"}}}, "homepageCalculator": {"title": "Homepage & Landing Page Rechner", "subtitle": "Konfigurieren Sie Ihre Website-Anforderungen für ein individuelles Angebot", "pageCount": "Anzahl der Seiten", "pageCountLanding": "<PERSON> Page (1)", "pageCountFull": "Vollständige Website (15+)", "pageCountExamples": "Beispielseiten: Startseite, Über uns, Dienstleistungen, Portfolio, Kontakt, Blog, usw.", "timeline": "Entwicklungszeitplan", "features": "Website-Funktionen", "featuresInfo": "Wählen Sie die Funktionen, die Sie für Ihre <strong>responsive Website</strong> benötigen. Erforderliche Funktionen können nicht abgewählt werden.", "addFeatures": "<PERSON><PERSON><PERSON>, um Funktionen hinzuzufügen...", "seo": "Suchmaschinenoptimierung", "seoDescription": "Grundlegende SEO-Einrichtung (Meta-Tags, Sitemap, usw.)", "multilingual": "Mehrsprachige Unterstützung", "multilingualDescription": "Unterstützung für mehrere Sprachen", "customFeatures": "Zusätzliche Anforderungen", "customFeaturesPlaceholder": "Beschreiben Sie alle benutzerdefinierten Funktionen oder Anforderungen...", "needsReview": "Ich möchte eine Überprüfung meiner bestehenden Website", "timelines": {"normal": {"label": "Standard (2-3 Wochen)", "description": "Reguläres Entwicklungstempo"}, "fast": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> (1-2 Wochen)", "description": "30% schnellere Lieferung"}, "urgent": {"label": "Priorität (3-5 Tage)", "description": "Dediziertes Team, schnellste Lieferung"}}}, "consultingCalculator": {"title": "Technischer Beratungsrechner", "subtitle": "Konfigurieren Sie Ihre Beratungsanforderungen für ein individuelles Angebot", "projectPhase": "Projektphase", "teamSize": "Teamgröße", "teamSizeSolo": "Solo (1)", "teamSizeLarge": "Großes Team (10+)", "developer": "<PERSON><PERSON><PERSON><PERSON>", "developers": "<PERSON><PERSON><PERSON><PERSON>", "expertise": "Benötigte Expertise", "addExpertise": "<PERSON><PERSON><PERSON>, um Expertisebereiche hinzuzufügen...", "duration": "Gesch<PERSON><PERSON><PERSON>", "durationShort": "Kurzfristig (1 Woche)", "durationLong": "<PERSON><PERSON><PERSON><PERSON> (12+ <PERSON><PERSON><PERSON>)", "week": "<PERSON><PERSON><PERSON>", "weeks": "<PERSON><PERSON><PERSON>", "expertConsultation": "Experten-Technische Beratung", "consultationDescription": "Unsere technischen Beratungsdienste nutzen jahrzehntelange Branchenerfahrung in verschiedenen Technologien und Branchen. Wir bieten sowohl strategische Beratung als auch praktische Implementierungsunterstützung.", "ctoLevel": "CTO-Level Expertise", "flexibleScheduling": "Flexible Terminplanung", "projectPhases": {"planning": "Planung & Strategie", "development": "Aktive Entwicklung", "maintenance": "Wartung & Support", "optimization": "Leistungsoptimierung"}}, "leadForm": {"title": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> Ihr {calculatorType}-Ang<PERSON><PERSON>", "subtitle": "<PERSON><PERSON>llen Sie das Formular aus, um ein detailliertes Angebot per E-Mail zu erhalten", "fullName": "Vollständiger Name", "namePlaceholder": "<PERSON>", "nameRequired": "Name ist erforderlich", "email": "E-Mail-Adresse", "emailPlaceholder": "<EMAIL>", "emailRequired": "E-Mail ist erforderlich", "emailInvalid": "Bitte geben Si<PERSON> eine gültige E-Mail-Adresse ein", "company": "Firmenname", "companyOptional": "(Optional)", "companyPlaceholder": "Muster GmbH", "agreeTerms": "<PERSON>ch bin damit ein<PERSON>den, das Preisangebot per E-Mail zu erhalten", "secureInfo": "Ihre Daten sind sicher und werden nicht weitergegeben", "termsRequired": "Sie müssen den Bedingungen zustimmen", "getQuote": "Ang<PERSON><PERSON> erhalten"}, "pricingResult": {"title": "<PERSON><PERSON> individ<PERSON><PERSON>", "subtitle": "Basierend auf Ihren Anforderungen", "estimatedCost": "Geschätzte Kosten", "estimatedTime": "Geschätzter Zeitrahmen", "weeks": "<PERSON><PERSON><PERSON>", "days": "Tage", "hours": "Stunden", "startingFrom": "Ab", "hourlyRate": "Stundensatz", "includedFeatures": "Enthaltene Funktionen", "nextSteps": "Nächste Schritte", "nextStepsDescription": "Wir haben ein detailliertes Angebot an Ihre E-Mail gesendet. Unser Team wird sich in Kürze mit Ihnen in Verbindung setzen, um Ihr Projekt im Detail zu besprechen.", "startOver": "<PERSON>eu starten", "contactUs": "Kontaktieren Sie uns"}}, "packages": {"mvp": {"title": "MVP-<PERSON><PERSON><PERSON><PERSON>", "timeframe": "3-4 W<PERSON>en", "description": "Bringen Sie Ihre Idee schnell mit einem Minimum Viable Product auf den Markt", "features": ["Implementierung der Kernfunktionalität", "Grundlegendes UI-Design", "Benutzerauthentifizierung", "Datenspeicherlösung", "Deployment auf einer Plattform", "Weltweit konform"]}, "prototype": {"title": "Schneller Prototyp", "timeframe": "1-2 Wochen", "description": "Testen Sie Ihr Konzept mit einem funktionalen Prototyp", "features": ["Interaktive UI-Mockups", "Grundlegende Funktionalität", "Implementierung des Benutzerflusses", "Präsentation für Stakeholder", "Flutter-gesteuerte MVP-Entwicklung"]}, "landingpage": {"title": "Landing-Page-Entwicklung", "timeframe": "2-4 W<PERSON>en", "description": "WCAG 2.0-konforme Landing-Pages mit neuesten Webtechnologien", "features": ["WCAG 2.0 AA-Konformität", "Inklusives Design für alle Nutzer", "Screenreader-Kompatibilität", "Hohe Performance-Metriken", "SEO-optimierte Struktur", "Responsives Design"]}, "architecture": {"title": "Projektarchitektur", "timeframe": "1-2 Wochen", "description": "Solide Grundlage für den Erfolg Ihres Projekts", "features": ["Technische Spezifikationen", "System-Architekturdesign", "Datenbankschema", "API-Dokumentation", "Entwicklungs-Roadmap"]}, "consulting": {"title": "Technische Beratung", "timeframe": "Fortlaufend", "description": "Expertenberatung für Ihre technischen Entscheidungen", "features": ["Empfehlungen zur Technologie-Stack", "Code-Reviews", "Leistungsoptimierung", "Sicherheitsbewertung", "Skalierbarkeitsplanung", "VAE-Zahlungsgateway-Integration"]}}}, "packages": {"title": "Unsere Pakete", "subtitle": "Maßgeschneiderte Lösungen für Ihre Bedürfnisse", "mvp": {"title": "MVP-<PERSON><PERSON><PERSON><PERSON>", "timeframe": "3-4 W<PERSON>en", "description": "Bringen Sie Ihre Idee schnell mit einem Minimum Viable Product auf den Markt", "features": ["Implementierung der Kernfunktionalität", "Grundlegendes UI-Design", "Benutzerauthentifizierung", "Datenspeicherlösung", "Deployment auf einer Plattform"]}, "prototype": {"title": "Prototyp-Paket", "timeframe": "1-2 Wochen", "description": "Testen Sie Ihr Konzept mit einem funktionalen Prototyp", "features": ["Interaktive UI-Mockups", "Grundlegende Funktionalität", "Implementierung des Benutzerflusses", "Präsentation für Stakeholder"]}, "architecture": {"title": "Projektarchitektur", "timeframe": "1-2 Wochen", "description": "Solide Grundlage für den Erfolg Ihres Projekts", "features": ["Technische Spezifikationen", "System-Architekturdesign", "Datenbankschema", "API-Dokumentation", "Entwicklungs-Roadmap"]}, "consulting": {"title": "Technische Beratung", "timeframe": "Fortlaufend", "description": "Expertenberatung für Ihre technischen Entscheidungen", "features": ["Empfehlungen zum Technologie-Stack", "Code-Reviews", "Leistungsoptimierung", "Sicherheitsbewertung", "Skalierbarkeitsplanung"]}}, "solutionsPortfolio": {"title": "Unsere Lösungen", "subtitle": "Branchenführende digitale Lösungen", "clickInstruction": "Karte anklicken für Details & Vollbildansicht", "imageCounter": "Bild {current} von {total}", "keyFeatures": "Hauptfunktionen", "problemsSolved": "Gelöste Probleme", "viewDetails": "Details anzeigen", "screenshot": "Screenshot", "screenshots": "Screenshots", "categories": {"all": "Alle Kategorien", "aiAssistant": "KI-Assistent", "foodDelivery": "Essenslieferung", "hospitality": "Gastgewerbe", "medical": "Medizin", "lifestyle": "Lifestyle-Apps", "automotive": "Automobil"}, "items": {"spotzAiAssistant": {"title": "Spotz KI-Assistent", "description": "KI-gestützter Assistent zum Finden von Orten in der Nähe, Empfehlungen geben, Navigation und Notfalldienste bereitstellen.", "imageAlt": "Spotz KI-Assistent mobile App Interface Screenshot {index}"}, "foodDelivery": {"title": "Essenslieferung", "description": "Komplette Lösung für Essensentdeckung und -lieferung mit intuitivem Bestellprozess.", "imageAlt": "Essenslieferungs-App Screenshot {index}"}, "hostIQ": {"title": "HostIQ - Hospitality SuperApp", "description": "All-in-One-Lösung für Hotels zur Verwaltung von Betriebsabläufen, Gästeerfahrungen und Dienstleistungen.", "imageAlt": "HostIQ Hospitality-Management-Plattform Screenshot {index}"}, "businessManagement": {"title": "Lokales Geschäftsmanagement", "description": "Marketing-Tools, Veranstaltungsplanung, Story-Sharing und Reservierungsverwaltung für lokale Unternehmen.", "imageAlt": "Geschäftsmanagement-Anwendung Screenshot {index}"}, "lumeusApp": {"title": "Lumeus A<PERSON>", "description": "Soziale Netzwerk- und Verbindungsplattform mit erweiterten Funktionen.", "imageAlt": "Lumeus Social-Networking-App Screenshot {index}"}, "nearby": {"title": "In der Nähe", "description": "Standortbasierte Entdeckungs-App, um alles in Ihrer Umgebung zu finden.", "imageAlt": "In der Nähe App Interface Screenshot {index}"}, "toggCarControl": {"title": "Togg Fahrzeugsteuerung", "description": "Intelligentes Fahrzeugsteuerungs- und Managementsystem für Togg-Fahrzeuge.", "imageAlt": "Togg Fahrzeugsteuerungs-App Screenshot {index}"}, "socialMediaPlatform": {"title": "Social-Media-Plattform", "description": "Moderne Social-Media-Plattform mit ansprechender Benutzererfahrung.", "imageAlt": "Social-Media-Plattform Interface Screenshot {index}"}, "default": {"imageAlt": "Portfolio-Element Screenshot {index}"}}, "solutions": {"aiAssistant": {"title": "KI-Assistenten-Lösungen", "description": "Intelligente virtuelle Assistenten, die Benutzererfahrungen verbessern und Aufgaben automatisieren.", "features": ["Verarbeitung natürlicher Sprache", "Kontextverständnis", "S<PERSON><PERSON>kennung", "Multi-Plattform-Unterstützung", "Integrationsfähigkeiten"], "problemsSolved": ["Informationszugriff", "Entscheidungsunterstützung", "Automatisierung des Kundenservice", "Aufgabenverwaltung"]}, "foodDelivery": {"title": "Essenslieferungs-Plattformen", "description": "End-to-End-Systeme für Restaurants und Lieferdienste.", "features": ["Bestellverwaltung", "Echtzeit-Tracking", "Zahlungsabwicklung", "Restaurant-Dashboard", "Lieferoptimierung"], "problemsSolved": ["Restaurant-Entdeckung", "Liefer<PERSON><PERSON>", "Auftragsabwicklung", "Kundenbindung"]}, "hospitality": {"title": "Hospitality-Management-Lösungen", "description": "Digitale Systeme zur Verbesserung der Gästeerfahrung und Optimierung der Betriebsabläufe.", "features": ["Buchungsverwaltung", "Gästeservices", "Personalkoordination", "Bestandskontrolle", "Analyse-Dashboard"], "problemsSolved": ["Optimierung der Gästeerfahrung", "Betriebliche Effizienz", "Ressourcenmanagement", "Servicebereitstellung"]}, "business": {"title": "Geschäftsmanagement-Systeme", "description": "Umfassende Plattformen zur Verwaltung von Betriebsabläufen und Wachstum.", "features": ["CRM-Integration", "Bestandsverwaltung", "Finanzverfolgung", "Mitarbeiterverwaltung", "Berichtswerkzeuge"], "problemsSolved": ["Prozessoptimierung", "Datenverwaltung", "Ressourcenzuweisung", "Business Intelligence"]}, "social": {"title": "Social-Media-Plattformen", "description": "Ansprechende Social-Networking-Lösungen für Gemeinschaften.", "features": ["Benutzerprofile", "<PERSON><PERSON>e teilen", "Nachrichtensystem", "Benachrichtigungsengine", "Analyse-Tools"], "problemsSolved": ["Aufbau digitaler Gemeinschaften", "Benutzerbindung", "Inhaltsentdeckung", "Soziale Interaktion"]}, "automotive": {"title": "Automobiltechnologie-Lösungen", "description": "Digitale Schnittstellen und Steuerungssysteme für Fahrzeuge.", "features": ["Fahrzeugverwaltung", "Navigationssysteme", "Diagnosewerkzeuge", "Entertainment-Integration", "IoT-Konnektivität"], "problemsSolved": ["Fahrzeugsteuerung", "Navigationsherausforderungen", "Wartungsmanagement", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}}}, "portfolio": {"title": "Unsere Expertise", "subtitle": "Branchen & Problemlösungen", "all": "Alle Branchen", "screenshot": "Screenshot", "screenshots": "Screenshots", "problemsWeSolve": "<PERSON><PERSON>, die wir lösen", "noSectorsFound": "Keine Branchen für den ausgewählten Filter gefunden.", "categories": {"aiAssistant": "KI-Assistent", "foodDelivery": "Lebensmittellieferung", "hospitality": "Gastgewerbe", "business": "Geschäft", "social": "Soziale Medien", "automotive": "Automobilindustrie"}, "sectors": {"assistant": "Assistenz-Apps", "food": "Essensbestellung & Lieferung", "hospitality": "Gastgewerbe", "lifestyle": "Lifestyle-Apps", "social": "Soziale Medien", "automotive": "Automobilindustrie", "medical": "Medizin & Gesundheitswesen", "business": "Geschäftslösungen"}, "sectorDescriptions": {"assistant": "KI-gestützte Assistenten, die die Produktivität steigern und personalisierte Empfehlungen geben", "food": "Nahtlose E<PERSON>sbestellungs- und Lieferplattformen mit Echtzeit-Tracking", "hospitality": "Digitale Lösungen für Hotels und Gastgewerbe zur Verbesserung des Gästeerlebnisses", "lifestyle": "<PERSON><PERSON><PERSON><PERSON><PERSON>, die den Alltag, das Wohlbefinden und die persönliche Entwicklung verbessern", "social": "Plattformen, die Menschen und Gemeinschaften durch gemeinsame Interessen verbinden", "automotive": "Intelligente Lösungen für Fahrzeugmanagement, Navigation und Fahrerassistenz", "medical": "Digitale Gesundheitslösungen, die die Patientenversorgung und medizinischen Abläufe verbessern", "business": "Unternehmensanwendungen, die Abläufe rationalisieren und die Produktivität steigern"}, "problems": {"assistant": {"1": "Informationsüberflutung", "2": "Aufgabenmanagement", "3": "Entscheidungsunterstützung"}, "food": {"1": "Bestellverwaltung", "2": "Liefer<PERSON><PERSON>", "3": "Restaurantentdeckung"}, "hospitality": {"1": "Gästemanagement", "2": "Serviceoptimierung", "3": "Buchungssysteme"}, "lifestyle": {"1": "Gesundheitstracking", "2": "Gewohnheitsbildung", "3": "Persönliche Organisation"}, "social": {"1": "Benutzerengagement", "2": "Inhaltsentdeckung", "3": "Gemeinschaftsaufbau"}, "automotive": {"1": "Fahrzeugüberwachung", "2": "Navigationoptimierung", "3": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "medical": {"1": "Patientenmanagement", "2": "Gesundheitsüberwachung", "3": "Medizinische Aufzeichnungssysteme"}, "business": {"1": "Arbeitsablaufoptimierung", "2": "Datenmanagement", "3": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "viewDetails": "Details anzeigen", "viewAllProjects": "Alle Projekte anzeigen"}, "clients": {"title": "<PERSON>ser<PERSON>", "subtitle": "<PERSON><PERSON><PERSON><PERSON>, mit denen wir zusammengearbeitet haben", "visitWebsite": "Website besuchen"}, "testimonials": {"title": "<PERSON>um Kunden bei mir bleiben", "subtitle": "8 <PERSON><PERSON><PERSON>, 15+ <PERSON><PERSON><PERSON><PERSON>, 0 unzufriedene Kunden", "readMore": "Vollständiges Feedback", "readLess": "<PERSON><PERSON> lesen"}, "contact": {"title": "Kostenlose Beratung buchen", "subtitle": "30 <PERSON><PERSON><PERSON>, unver<PERSON><PERSON><PERSON>, sofort terminierbar", "name": "Name", "email": "E-Mail", "phone": "Telefon (WhatsApp)", "message": "<PERSON><PERSON><PERSON> A<PERSON>-Idee", "send": "Senden", "yourName": "Ihr Name", "yourEmail": "Ihre E-Mail", "subject": "<PERSON><PERSON>-Idee", "howCanIHelp": "<PERSON><PERSON><PERSON><PERSON><PERSON> Sie mir von Ihrer App-Idee:", "yourMessageHere": "Beschreiben Sie kurz: Was soll die App können? Für wen ist sie? Bis wann brauchen Sie sie?", "getInTouch": "Beratung vereinbaren", "sendMessage": "Kostenlose Beratung buchen", "schedule": "Sofort-<PERSON><PERSON><PERSON> buchen", "freeConsultation": "30 Min. kostenlose Beratung - Festpreis in 24h", "location": "Remote (Deutschland/EU)", "submitButton": "<PERSON><PERSON><PERSON> an<PERSON>gen", "sending": "Terminanfrage wird gesendet...", "messageSent": "Terminanfrage gesendet! Antwort in max. 2h.", "errorTryAgain": "<PERSON><PERSON>, bitte versuchen Sie es erneut", "orSchedule": "Oder direkt Termin im Kalender buchen (antworte in 2h)"}, "footer": {"copyright": "© 2025 Innovatio. Alle Rechte vorbehalten.", "description": "Wir entwickeln hochmoderne mobile und Web-Anwendungen, die Unternehmen durch innovative Technologie transformieren.", "quickLinks": "<PERSON><PERSON><PERSON>", "footerContact": "Kontakt", "legal": "Rechtliches", "newsletter": "Newsletter", "newsletterDesc": "Abonnieren Sie unseren Newsletter, um Updates und Einblicke zu erhalten.", "emailPlaceholder": "E-Mail eingeben", "subscribe": "Abonnieren", "builtWith": "Erstellt mit", "and": "und", "downloadCV": "<PERSON><PERSON>", "englishCV": "<PERSON><PERSON><PERSON>", "germanCV": "De<PERSON>ch"}, "featuresSection": {"features": [{"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> und <PERSON><PERSON>.", "icon": "IconTerminal2"}, {"title": "Einfache Bedienung", "description": "<PERSON><PERSON> ist so einfach wie die Benutzung eines Apple-Geräts und so teuer wie der Kauf eines.", "icon": "IconEaseInOut"}, {"title": "Einzigartige Preisgestaltung", "description": "Unsere Preise sind die besten auf dem Markt. <PERSON><PERSON>, keine <PERSON>, keine K<PERSON>it<PERSON> er<PERSON>.", "icon": "Icon<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"title": "100% Verfügbarkeitsgarantie", "description": "Uns kann einfach niemand vom Netz nehmen.", "icon": "IconCloud"}, {"title": "Multi-Tenant-Architektur", "description": "<PERSON>e können einfach Passwörter teilen, anstatt neue Lizenzen zu kaufen.", "icon": "IconRouteAltLeft"}, {"title": "24/7 Kundensupport", "description": "Wir sind zu 100% der Zeit verfügbar. Zumindest unsere KI-Agenten.", "icon": "IconHelp"}, {"title": "Geld-zurück-Garantie", "description": "<PERSON>n Ihnen EveryAI nicht gef<PERSON>, überzeugen wir <PERSON><PERSON>, uns zu mögen.", "icon": "IconAdjustmentsBolt"}, {"title": "Und alles andere", "description": "Mir sind gerade die Textideen ausgegangen. Akzeptieren Sie meine aufrichtige Entschuldigung.", "icon": "IconHeart"}]}, "cookies": {"title": "Cookie-Einwilligung", "description": "Wir verwenden <PERSON>, um Ihr Surferlebnis zu verbessern, personalisierte Anzeigen oder Inhalte bereitzustellen und unseren Datenverkehr zu analysieren. Durch Klicken auf \"Alle akzeptieren\" stimmen Sie der Verwendung von <PERSON> zu.", "acceptAll": "Alle akzeptieren", "decline": "<PERSON><PERSON><PERSON><PERSON>", "customize": "<PERSON><PERSON><PERSON>", "necessary": "Notwendige Cookies", "necessaryDesc": "Diese Cookies sind für das ordnungsgemäße Funktionieren der Website unerlässlich und können nicht deaktiviert werden.", "analytics": "Analyse-Cookies", "analyticsDesc": "Diese Cookies helfen uns zu verstehen, wie Besucher mit unserer Website interagieren, und helfen uns, unsere Dienste zu verbessern.", "marketing": "Marketing-Cookies", "marketingDesc": "Diese Cookies werden verwendet, um Besucher über Websites hinweg zu verfolgen, um relevante Werbung anzuzeigen.", "functional": "Funktionale Cookies", "functionalDesc": "Diese Cookies ermöglichen erweiterte Funktionen und Personalisierung auf unserer Website.", "save": "Einstellungen speichern", "settings": "Cookie-Einstellungen", "close": "Schließen", "cookiePolicy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "privacyPolicy": "Datenschutzrichtlinie"}, "heroParallax": {"title": "Das ultimative Entwicklungsstudio", "subtitle": "Wir erstellen schöne Produkte mit den neuesten Technologien und Frameworks. Wir sind ein Team aus leidenschaftlichen Entwicklern und Designern, die es lieben, erstaunliche Produkte zu bauen.", "products": {"mobileApp": "Mobile App-Entwicklung", "webDev": "Web-Entwicklung", "uiux": "UI/UX-Design", "ecommerce": "E-Commerce-Lösungen", "ai": "KI-Integration", "cloud": "Cloud-Lösungen", "devops": "DevOps", "dataAnalytics": "Datenanalyse", "blockchain": "Blockchain-Entwicklung", "arvr": "AR/VR-Lösungen", "customSoftware": "Individuelle Software", "mobileGame": "Mobile Game-Entwicklung", "iot": "IoT-Lösungen", "api": "API-Entwicklung", "cybersecurity": "Cybersicherheit"}}, "seo": {"meta": {"home": {"title": "Mobile App-Entwicklung & Digitale Lösungen | Innovatio-Pro", "description": "Professionelle Mobile App-Entwicklung mit Flutter, KI-Integration und digitale Lösungen. MVP-Entwicklung, Prototyping und Full-Stack-Entwicklung von Innovatio-Pro.", "keywords": "Mobile App-Entwicklung, Flutter-Entwicklung, MVP-Entwicklung, KI-Integration, digitale Lösungen, Prototyping, Full-Stack-Entwicklung, Innovatio-Pro"}, "templates": {"title": "Mobile App-Lösungen & KI-Integration - Innovatio-Pro", "description": "Entdecken Sie unsere mobilen App-Lösungen, KI-Integration und digitale Plattformen. Spezialisiert auf Flutter, MVP-Entwicklung und innovative mobile Technologien.", "keywords": "Mobile App-Lösungen, KI-Integration, Flutter Apps, MVP-Entwicklung, digitale Plattformen, mobile Technologien, App-Entwicklung"}, "services": {"title": "Mobile App-Entwicklungsservices - Flutter, KI & Digitale Lösungen", "description": "Professionelle Mobile App-Entwicklungsservices mit Flutter, KI-Integration und modernen Technologien. Spezialisiert auf MVP, Prototyping und Full-Stack mobile Lösungen.", "keywords": "Mobile App-Services, Flutter-Entwicklung, MVP-Services, KI-Integration, mobile Lösungen, App-Entwicklungsservices, digitale Transformation"}, "about": {"title": "Über Innovatio-Pro - Mobile App-Entwicklung & KI-Spezialist", "description": "Erfahren Sie mehr über Innovatio-Pro, Ihren Spezialisten für Mobile App-Entwicklung mit Flutter, KI-Integration und innovative digitale Lösungen.", "keywords": "Innovatio-Pro, Mobile App-Entwickler, Flutter-Spezialist, KI-Integration, digitale Lösungen, App-Entwicklungsunternehmen"}, "contact": {"title": "Kontakt - Mobile App-Entwicklung & Digitale Lösungen | Innovatio-Pro", "description": "Kontaktieren Sie Innovatio-Pro für professionelle Mobile App-Entwicklung, KI-Integration und digitale Lösungen. Kostenlose Beratung für Ihr nächstes Projekt verfügbar.", "keywords": "Mobile App-Entwicklung Kontakt, Flutter-Entwicklung Beratung, KI-Integration Services, digitale Lösungen Anfrage, Innovatio-Pro Kontakt"}, "pricing": {"title": "Mobile App-Entwicklung Preise - MVP, Prototyping & KI-Lösungen", "description": "Transparente Preise für Mobile App-Entwicklung, MVP-Development, Prototyping und KI-Integration. Von schnellen Prototypen bis zu Full-Stack mobilen Lösungen.", "keywords": "Mobile App-Entwicklung Preise, MVP-Entwicklung Kosten, <PERSON><PERSON>ter A<PERSON> Preise, KI-Integration Tarife, Prototyping Preise, App-Entwicklung Angebote"}, "faq": {"title": "FAQ - Mobile App-Entwicklung & Digitale Lösungen | Innovatio-Pro", "description": "Häufige Fragen zu unseren Mobile App-Entwicklungsservices, Flutter-Entwicklung, KI-Integration und digitalen Lösungen. Support und Antworten von Innovatio-Pro.", "keywords": "Mobile App-Entwicklung FAQ, Flutter-Entwicklung Fragen, KI-Integration Hilfe, digitale Lösungen Support, App-Entwicklung Antworten"}}, "openGraph": {"siteName": "Innovatio-Pro - Mobile App-Entwicklung & Digitale Lösungen", "defaultImage": "/images/og-innovatio-pro-de.jpg", "defaultImageAlt": "Innovatio-Pro - Mobile App-Entwicklung mit Flutter und KI-Integration"}, "jsonLd": {"organization": {"name": "Innovatio-Pro", "alternateName": "Innovatio-Pro Mobile Development", "description": "Spezialist für Mobile App-Entwicklung mit Flutter, KI-Integration und innovative digitale Lösungen für moderne Unternehmen.", "url": "https://innovatio-pro.com", "telephone": "+49-175-9918357", "email": "<EMAIL>", "address": {"streetAddress": "Remote Office", "addressLocality": "Wyoming", "addressRegion": "Wyoming", "postalCode": "82001", "addressCountry": "US"}, "sameAs": ["https://github.com/innovatio-pro", "https://linkedin.com/company/innovatio-pro"]}}}, "blog": {"title": "Tech-Insights", "description": "Entdecken Sie die neuesten Trends, Einblicke und Innovationen in der mobilen Entwicklung. Von Flutter Best Practices bis hin zu KI-Integrationsstrategien - bleiben Sie an der Spitze der Tech-Revolution.", "categoriesTitle": "<PERSON><PERSON><PERSON>", "categoriesDescription": "Entdecken Sie Artikel zu verschiedenen Tech-Bereichen", "subscriptionTitle": "Bleiben Sie informiert", "subscriptionDescription": "Erhalten Sie die neuesten Flutter-Insights, AI-Trends und Tech-Innovationen direkt in Ihr Postfach.", "readMore": "Wei<PERSON>lesen", "noArticles": "<PERSON><PERSON> gefunden", "author": "Autor", "readingTime": "Lesezeit", "views": "Auf<PERSON><PERSON>", "tags": "Tags", "floatingButton": {"tooltip": "Tech Blog", "currentArticle": "Aktueller Artikel", "viewAll": "Alle Artikel an<PERSON>hen", "newPosts": "Neue Beiträge"}, "categories": {"all": "Alle Artikel", "company": "Über das Unternehmen", "flutter": "Flutter Development", "mobile": "Mobile Trends", "ai": "AI Integration", "performance": "Performance", "caseStudies": "Case Studies", "trends": "Industry Trends"}}}