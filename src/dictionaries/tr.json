{"nav": {"home": "<PERSON>", "about": "Hakkımızda", "solutions": "Çözümler", "services": "<PERSON><PERSON><PERSON><PERSON>", "techStack": "Teknoloji", "portfolio": "Portföy", "contact": "İletişim", "chatWithUs": "Bizimle Sohbet Et", "bookConsultation": "Danışmanlı<PERSON>", "apps": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pricing": "Fiya<PERSON><PERSON>rma", "prices": "<PERSON><PERSON><PERSON><PERSON>", "calculator": "<PERSON>yat <PERSON>", "clients": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "testimonials": "Referanslar", "blog": "Blog"}, "hero": {"title": "🚀 Uygulamanız – Hızlı. Ölçeklenebilir. Karlı.", "subtitle": "Fikirden App Store'a rekor sürede. MVP'<PERSON>r, <PERSON>-<PERSON><PERSON><PERSON><PERSON>r ve tam ölçekli ürünler geliştiriyoruz.", "tagline": "Geleceği Birlikte Şekillendiriyoruz", "description": "Fikirden App Store'a: <PERSON><PERSON>, <PERSON><PERSON><PERSON>.", "typing": {"sequence1": "Fikirden Uygulamaya: Vizyon & Kod.", "sequence2": "Profes<PERSON>el <PERSON>.", "sequence3": "Yüksek Performanslı Çözümler."}, "cta": {"primary": "Projenizi başlatın", "secondary": "Danışmanlı<PERSON>", "calculator": "Proje Fiyatını Şimdi Alın", "startProject": "Projenizi başlatın", "freeConsultation": "Ücretsiz danışmanlık", "calculateCost": "Maliyetleri hesaplayın"}, "metrics": {"development": {"label": "Daha Hızlı Geliştirme", "value": "40%", "description": "daha hızlı geliştirme", "howTitle": "Nasıl:", "howExplanation": "Yapay zeka destekli en yeni IDE'leri, otomatik kod üretimini ve akıllı kod tamamlamayı kullanarak."}, "timeToMarket": {"label": "Daha Hızlı Pazara Giriş", "value": "50%", "description": "daha hızlı yayınlama", "howTitle": "Nasıl:", "howExplanation": "Fast lane CI/CD pipeline, otomatik test, yapay zeka destekli kod inceleme ve optimize edilmiş dağıtım süreci."}, "costSaving": {"label": "Maliyet Tasarrufu", "value": "30%", "description": "mali<PERSON>t ta<PERSON><PERSON><PERSON>", "howTitle": "Nasıl:", "howExplanation": "Çoklu platform geliştirme için Flutter kullanımı, optimize edilmiş bulut kaynakları ve verimli geliştirme uygulamaları."}}}, "about": {"title": "Hakkımızda", "subtitle": "Vizyonumuz ve Misyonumuz", "vision": "Vizyon", "visionDesc": "İşletmelerin modern dünyada gelişmesini sağlayan yenilikçi teknoloji çözümleri sunarak dijital geleceği şekillendirmek.", "mission": "<PERSON><PERSON><PERSON>", "missionDesc": "İşletmelerin karmaşık sorunları çözmelerine ve hedeflerine ulaşmalarına yardımcı olmak için Flutter gibi en son teknolojileri kullanarak yüksek kaliteli dijital çözümler oluşturuyoruz.", "founderTitle": "<PERSON><PERSON><PERSON>", "founderDesc": "Tutkulu bir mobil ve web geliştirici olarak, modern ve verimli çözümler oluşturmak için Flutter gibi en son teknolojileri kullanma konusunda uzmanım.", "skills": "<PERSON><PERSON><PERSON>", "projects": "<PERSON><PERSON><PERSON>", "testimonials": "Müşteri Yorumları", "experience": "<PERSON><PERSON><PERSON>", "clients": "<PERSON><PERSON><PERSON>", "transformBusiness": "Teknoloji ile işletmeleri dönüştürmek", "createSolutions": "Geleceğe dönük dijital çözümler yaratmak", "stayingAhead": "Teknolojinin ön saflarında kalmak", "exceptionalUX": "Olağanüstü kullanıcı deneyimleri sunmak", "highPerformance": "Yüksek performanslı uygulamalar geliştirmek", "solvingChallenges": "Teknoloji ile gerçek iş zorluklarını çözmek", "flutterExpert": "<PERSON><PERSON>", "webDevAdvanced": "İleri Seviye", "aiIntegration": "Entegrasyon", "projectsCompleted": "<PERSON><PERSON><PERSON><PERSON>", "personalDedication": "<PERSON><PERSON><PERSON><PERSON>", "dedication": "Adanmışlık", "qualityFocused": "<PERSON><PERSON>", "personalService": "<PERSON><PERSON><PERSON><PERSON>", "focusedApproach": "Odaklı Yaklaşım", "dedicatedService": "<PERSON>zel <PERSON>", "clientReviews": "Müşteri Yorumları", "focusedService": "Odaklı Hizmet", "longTerm": "<PERSON><PERSON><PERSON>", "partnerships": "Ortaklıklar", "privacyFocused": "Gizlilik Odaklı", "secureServices": "Gü<PERSON><PERSON>", "personalAttention": "<PERSON><PERSON>isel <PERSON>", "dedicatedDeveloper": "<PERSON><PERSON>", "securityFocused": "Güvenlik Odaklı", "privacyRespected": "Gizlilik Saygılı", "quality": "<PERSON><PERSON>", "averageDelivery": "Ortalama Teslimat", "metrics": {"yearsExperience": "8+", "projectsCompleted": "15+", "happyClients": "12+", "clientRating": "5,0/5", "deliveryTimeframe": "3-4 hafta", "personalValue": "<PERSON><PERSON><PERSON><PERSON>", "qualityValue": "<PERSON><PERSON>", "longTermValue": "<PERSON><PERSON><PERSON>"}}, "advantages": {"title": "ÇÖZÜMLERİMİZ", "subtitle": "Başarılı Mobil Uygulamalar Geliştirmenize Nasıl Yardımcı Oluyoruz", "speed": "Hızlı Geliştirme", "speedDesc": "Kaliteden ödün vermeden çözümleri hızla <PERSON>uz", "stability": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stabilityDesc": "Uygulamalarımız stabilite ve performans için ta<PERSON>lanmıştır", "cost": "Maliyet Verimliliği", "costDesc": "Optimize edilmiş geliştirme süreci zamanınızı ve paranızı tasarruf eder", "timeToMarket": "Daha Hızlı Pazara Sunum", "timeToMarketDesc": "Ürününüzü hızla piyasaya sürün ve rekabette öne geçin", "aiIntegration": "Yapay Zeka Entegrasyonu", "aiIntegrationDesc": "İşletmenizi güçlü yapay zeka yetenekleriyle geliştirin", "development": "Full-<PERSON><PERSON>", "developmentTime": "4-12 hafta, pro<PERSON><PERSON>ığ<PERSON>na gö<PERSON>", "developmentDesc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>, tam backend entegrasyonu ve gelişmiş özelliklerle eksiksiz mobil uygulama geliştirme.", "mvp": "MVP <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mvpTime": "2-4 hafta, pro<PERSON><PERSON>ığ<PERSON><PERSON> gö<PERSON>", "mvpDesc": "Konseptinizi doğrulamak ve erken kullanıcıları veya yatırımcıları çekmek için temel işlevselliğe sahip bir Minimum Uygulanabilir Ürün ile hızla başlayın.", "prototype": "Hızlı Prototipleme", "prototypeTime": "1-2 hafta, pro<PERSON><PERSON>ığına gö<PERSON>", "prototypeDesc": "<PERSON>tirmeye başlamadan önce kavramları hızla test edin, zaman ve kaynak tasarrufu sağlayın.", "qa": "<PERSON><PERSON>", "qaTime": "<PERSON><PERSON><PERSON><PERSON>, proje karmaşıklığına göre ölçeklendirilir", "qaDesc": "Uygulamanızın otomatik ve manuel test protokolleriyle tüm cihazlarda ve platformlarda kusursuz çalışmasını sağlayan kapsamlı testler.", "consulting": "Teknik Danışmanlık", "consultingTime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, pro<PERSON><PERSON>ığ<PERSON><PERSON> g<PERSON><PERSON>", "consultingDesc": "Mobil uygulamanızı optimize etmek için teknoloji yığını, mi<PERSON><PERSON> ka<PERSON>lar ve uygulama stratejileri hakkında uzman tavsiyeleri.", "uiux": "UI/UX Tasarımı", "uiuxTime": "2-3 hafta, pro<PERSON><PERSON>ığına gö<PERSON>", "uiuxDesc": "Estetiği işlevsellikle dengeleyen, sezgisel ve çekici mobil deneyimler yaratan kullanıcı merkezli tasarım.", "maintenance": "Bakım ve Destek", "maintenanceTime": "<PERSON><PERSON><PERSON><PERSON>, proje karmaşıklığına göre ölçeklendirilir", "maintenanceDesc": "Uygulamanızın rekabet gücünü korumak için dü<PERSON> g<PERSON>, performans optimizasyonu ve özellik geliştirmeleriyle uzun vadeli destek.", "analytics": "Analitik Entegrasyonu", "analyticsTime": "1-2 hafta, pro<PERSON><PERSON>ığına gö<PERSON>", "analyticsDesc": "Kullanıcı davranışı hakkında uygulanabilir bilgiler elde etmek için veri izleme uygulaması, uygulamanız için veri odaklı kararlar almanızı sağlar.", "training": "Ekip Eğitimi", "trainingTime": "1-2 hafta, pro<PERSON><PERSON>ığına gö<PERSON>", "trainingDesc": "Ekibiniz için teslim<PERSON>an sonra uygulamanızı yönetme ve geliştirme konusunda kapsamlı eğitim.", "developmentEfficiency": "Geliştirme Verimliliği", "timeToMarketReduction": "Pazara Çıkış Süresinde Azalma", "conceptValidation": "Konsept Doğrulama", "bugFreeRate": "Hatasız Oranı", "technicalImprovement": "Teknik İyileştirme", "userSatisfaction": "Kullanıcı Memnuniyeti", "appUptime": "Uygulama Çalışma Süresi", "dataAccuracy": "<PERSON><PERSON>", "knowledgeRetention": "<PERSON><PERSON><PERSON>", "developmentInfo": {"title": "Geliş<PERSON><PERSON><PERSON>", "simpleApp": {"title": "<PERSON><PERSON><PERSON>", "examples": "Örnekler: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, backend o<PERSON><PERSON> basit bilgi uygulamaları.", "features": ["Birkaç ekran (3-5)", "Backend entegrasyonu yok veya minimal", "Standart UI bileşenleri", "Karmaşık animasyon veya fonksiyon yok"], "timeline": {"total": "Geliştirme süresi: 4-8 hafta", "frontend": "Frontend: 2-4 hafta", "backend": "Backend (gerekirse): 1-2 hafta", "testing": "Test ve dağıtım: 1-2 hafta"}}, "mediumApp": {"title": "Or<PERSON> Sevi<PERSON> Uygulama", "examples": "Örnekler: <PERSON><PERSON><PERSON><PERSON><PERSON> u<PERSON>gu<PERSON>aları, temel özelliklere sahip sosyal medya uygulamaları, kullanıcı kaydı ve veritabanı entegrasyonu olan uygulamalar.", "features": ["6-15 ekran", "Backend entegrasyonu (örn. REST veya GraphQL API'leri)", "Kullanıcı kaydı ve kimlik doğrulama", "Kullanıcı ve uygulama verileri için veritabanı", "Bazı animasyonlar ve interaktif öğeler", "<PERSON><PERSON>"], "timeline": {"total": "Geliştirme süresi: 8-16 hafta", "frontend": "Frontend: 4-6 hafta", "backend": "Backend: 3-5 hafta", "testing": "Test ve dağıtım: 2-3 hafta"}}, "complexApp": {"title": "Karmaşık Uygulama", "examples": "Örnekler: <PERSON><PERSON>, Instagram gibi uygulamalar veya gelişmiş özelliklere sahip bankacılık uygulamaları.", "features": ["15+ ekran", "Yüksek düzeyde interaktif kullanıcı arayüzü", "Gerçek zamanlı özellikler (örn. canlı takip, sohbet)", "Üçüncü taraf API entegrasyonları (örn. ödeme geçitleri, kart API'leri)", "Bulut entegrasyonlu ölçeklenebilir backend", "Güvenlik özellikleri (örn<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, iki faktörlü kimlik doğrulama)", "Çevrimdışı işlevsellik"], "timeline": {"total": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sü<PERSON>i: 16-32 hafta veya daha uzun", "frontend": "Frontend: 6-10 hafta", "backend": "Backend: 6-12 hafta", "testing": "Test ve dağıtım: 4-6 hafta"}}, "factors": {"title": "Geliştirme Süresini Etkileyen Faktörler", "teamSize": "Ekip büyüklüğü: <PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON> bir ekip (örn. frontend, backend ve QA için ayrı geliştiriciler) geliştirmeyi hızlandırabilir. Tek bir geliştirici daha fazla zaman gerektirir.", "technology": "Teknoloji: Native geliştirme (örn. iOS için Swift, Android i<PERSON><PERSON>) genellikle Flutter veya React Native gibi çapraz platform yaklaşımlarından daha uzun sürer. Çapraz platform framework'leri geliştirme süresini %30-40 azaltabilir.", "requirements": "Gereksinimler ve değişiklikler: <PERSON><PERSON><PERSON>ğ<PERSON>şiklikler veya belirsiz gereksinimler geliştirme süresini uzatabilir.", "testing": "Test ve hata ayıklama: <PERSON><PERSON><PERSON><PERSON><PERSON>, özellikle birden fazla platformda (iOS ve Android) daha fazla test süresi gerektirir.", "design": "Tasarım: <PERSON><PERSON><PERSON> daha az zaman gerektiri<PERSON>en, <PERSON><PERSON>, animasyonlu tasarımlar geliştirme süresini artırır."}, "summary": "Özet: <PERSON><PERSON><PERSON>: 4-8 hafta. Orta Seviye Uygulama: 8-16 hafta. Karmaşık Uygulama: 16-32 hafta veya daha uzun.", "aiComparison": "Yapay zeka destekli geliştirme ve Flutter ile bu süreleri %40-60 oranında azaltabilir, yüksek kalite ve performansı koruyarak."}}, "serviceSection": {"title": "Hizmetlerimiz", "subtitle": "İşletmeniz için <PERSON>zel Dijital Çözümler", "description": "Günümüzün rekabetçi ortamında işletmelerin başarılı olmasına yardımcı olmak için kapsamlı bir dijital hizmet yelpazesi sunuyoruz. U<PERSON><PERSON>ığ<PERSON><PERSON><PERSON>z, yenilikçi ve etkili çözümler sunmak için çeşitli alanları kapsamaktadır.", "viewAll": "Projenizi <PERSON>", "mobileApps": {"title": "<PERSON><PERSON>", "description": "Flutter kullanarak iOS ve Android cihazlarda sorunsuz den<PERSON>, yerel performanslı çapraz platform mobil uygulamalar.", "benefits": ["<PERSON><PERSON>tirmeye kıyasla %40 daha hızlı pazara sunum", "iOS ve Android platformları için tek kod tabanı", "<PERSON><PERSON> ben<PERSON>i performans ve güzel kullanıcı arayüzü"]}, "webDev": {"title": "Web Geliştirme", "description": "Next.js, React ve Node.js gibi en son teknolojilerle oluşturulmuş modern, duyarlı web siteleri ve web uygulamaları.", "benefits": ["Sunucu tarafı işleme ile SEO optimize edilmiş performans", "Tüm cihazlarda çalışan duyarlı tasarımlar", "Gelecekteki büyüme için ölçeklenebilir mimari"]}, "uiuxDesign": {"title": "UI/UX Tasarımı", "description": "Düşüncelice tasarlanmış kullanıcı deneyimleri aracılığıyla müşterileri memnun eden ve etkileşimi artıran sezgisel, kullanıcı odaklı tasarım.", "benefits": ["Kullanıcı merkezli tasarım yaklaşımı", "Dönüşüm optimize edilmiş a<PERSON>üzler", "Tüm platformlarda tutarlı marka deneyimi"]}, "consulting": {"title": "Teknik Danışmanlık", "description": "Dijital projeleriniz için bilgiye dayalı kararlar vermenize yardımcı olmak üzere teknoloji stratejisi ve uygulaması konusunda uzman tavsiyesi.", "benefits": ["Teknoloji yığını önerileri", "<PERSON><PERSON><PERSON>a ve inceleme", "Performans ve güvenlik optimizasyonu"]}, "aiSolutions": {"title": "Yapay Zeka Entegrasyonu", "description": "Akıllı özellikler ve otomasyon ile iş uygulamalarınızı geliştirmek için yapay zeka yeteneklerini dahil edin.", "benefits": ["Kişiselleştirilmiş kullanıcı deneyimleri", "Otomatikleştirilmiş iş akışları ve süreçler", "Veri odaklı içgörüler ve tahminler"]}, "prototype": {"title": "Hızlı Prototipleme", "description": "<PERSON>rmeye başlamadan önce interaktif prototiplerle konseptleri hızla test edin, zaman ve kaynak tasarrufu sağlayın.", "benefits": ["Minimum yatırı<PERSON>la fikirleri doğ<PERSON>ın", "Sürecin erken aşamalarında kullanıcı geri bildirimi top<PERSON>ın", "Tam geliştirmeden önce konseptleri iyileştirin"]}, "mvp": {"title": "MVP <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Konseptinizi doğrulamak ve ilk kullanıcıları çekmek için temel işlevsellik içeren bir Minimum Uygulanabilir Ürün ile hızla başlayın.", "benefits": ["Temel özelliklerle daha hızlı pazar girişi", "İş fikirlerini doğrulamak için maliyet etkin yaklaşım", "Gerçek kullanıcı geri bildirimlerine dayalı yinelemeli geliştirme"]}, "fullstack": {"title": "Full-<PERSON><PERSON>", "description": "Entegre frontend ve backend çözümleriyle konseptten dağıtıma kadar eksiksiz uygulama geliştirme.", "benefits": ["Uçtan uca geliştirme uzmanlığı", "Tüm sistem bileşenleri arasında sorunsuz entegrasyon", "Kapsamlı test ve kalite güvencesi"]}, "homepage": {"title": "Ana Sayfa ve İniş Sayfaları", "description": "İşinizi sergileyen ve ziyaretçileri müşterilere dönüştüren profesyonel web siteleri ve iniş sayfaları.", "timeframe": "2-3 hafta", "benefits": ["Dönüşüm odaklı tasarım", "SEO dostu mimari", "<PERSON><PERSON>ü<PERSON>"]}, "landingpage": {"title": "Erişilebilir İniş <PERSON>ı", "description": "Maks<PERSON>um erişilebilirlik ve performans için en yeni web teknolojileriyle WCAG 2.0 uyumlu iniş sayfaları.", "timeframe": "2-4 hafta", "benefits": ["WCAG 2.0 AA uyumluluğu", "<PERSON><PERSON><PERSON> kullanıcılar için kapsayıcı tasarım", "Ekran ok<PERSON>u uyumluluğu", "Yüksek performans metrikleri"]}, "qa": {"title": "<PERSON><PERSON>", "description": "Uygulamanızın hatasız çalışmasını sağlamak için otomatik ve manuel testlerle cihazlar ve platformlar genelinde kaps<PERSON>lı testler.", "timeframe": "<PERSON><PERSON><PERSON><PERSON>", "benefits": ["Hatasız kullanıcı deneyimi", "Performans optimizasyonu", "Çapraz platform uyumluluğu"]}}, "services": {"title": "Teknoloji Altyapımız", "subtitle": "Kapsamlı Dijital Çözümler", "description": "İş ihtiyaçlarınız için sağlam ve ölçeklenebilir çözümler sunmak üzere birden fazla alanda en son teknolojileri kullanıyoruz.", "frontend": "Frontend Geliştirme", "frontendDesc": "Modern web teknolojileri ile duyarlı ve etkileşimli kullanıcı arayüzleri oluşturma", "backend": "Backend Geliştirme", "backendDesc": "Ölçeklenebilir uygulamalar için sağlam sunucu tarafı çözümleri ve API'ler oluşturma", "mobile": "<PERSON><PERSON>", "mobileDesc": "<PERSON>rel performansa sahip çapraz platform mobil uygulamalar geliştirme", "ai": "<PERSON><PERSON>y Zeka ve Makine Öğrenimi", "aiDesc": "Uygulamalara akıllı özellikler ve otomasyon entegrasyonu", "mobileApps": "<PERSON><PERSON>", "mobileAppsDesc": "iOS ve Android için Flutter ile çapraz platform uygulamaları", "webDev": "Web Geliştirme", "webDevDesc": "Modern, duyarlı web siteleri ve web uygulamaları", "uiuxDesign": "UI/UX Tasarım", "uiuxDesignDesc": "Müşterileri memnun eden sez<PERSON>, kullanıcı odaklı tasarım", "consulting": "Teknik Danışmanlık", "consultingDesc": "Teknoloji stratejisi ve uygulaması konusunda uzman tavsiyesi", "aiSolutions": "Yapay Zeka Entegrasyonu", "aiSolutionsDesc": "İşletmenizi geliştirmek için yapay zeka yeteneklerini dahil edin", "viewAll": "<PERSON><PERSON>m Hizmetleri Görüntüle"}, "prices": {"title": "Fiyatlarımız", "subtitle": "Her Aşama iç<PERSON>", "description": "Önceden tanımlanmış paketlerimizden seçim yapın veya proje gereksinimlerinize göre özelleştirin. Hizmetleri birleştirerek %15 indirim kazanın.", "caseStudyTitle": "Vaka Çalışması: %40 Daha Hızlı Pazara Çıkış", "caseStudyDescription": "Wyoming merkezli fintech müşterimiz, MVP'sini sektör ortalamasından %40 daha hızlı piyasaya sürerek ek finansman sağlama ve büyümeyi hızlandırma imkanı buldu.", "promotionTitle": "Hizmetleri Birleştirin ve %15 Tasarruf Edin", "promotionDescription": "Herhangi iki veya daha fazla hizmeti birleştirin ve toplam proje maliyetinizden %15 in<PERSON><PERSON> ka<PERSON>n.", "calculatorTitle": "<PERSON><PERSON> Teklif mi Lazım?", "calculatorDescription": "Özel proje gereksinimlerinize göre ayrıntılı bir tahmin almak için interaktif fiyatlandırma hesaplayıcımızı kullanın.", "calculatorButton": "Fiyat Hesaplayıcıyı Aç", "discussButton": "Projenizi <PERSON>", "contactButton": "Bizimle İletişime Geçin", "pricingDisclaimer": "* <PERSON><PERSON><PERSON><PERSON>, proje gereksinimleri ve ek hizmetlere göre değişiklik gösterebilir. Özel ihtiyaçlarınıza uygun özel bir teklif için bizimle iletişime geçin.", "priceVariesInfo": "<PERSON><PERSON><PERSON>, pro<PERSON><PERSON>, ek gereksinimlere ve zaman kısıtlamalarına bağlı olarak değişebilir. Ayrıntılı bir teklif için bizimle iletişime geçin.", "packages": {"mvp": {"title": "MVP <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "timeframe": "3-4 hafta", "description": "Minimum Uygulanabilir Ürün ile fikrinizi hızla piyasaya sürün", "features": ["Temel işlevsellik uygulaması", "Temel UI tasarımı", "Kullanıcı kimlik doğrulama", "<PERSON><PERSON> de<PERSON>", "Tek platform dağıtımı", "Dünya çapında uyumluluk"]}, "prototype": {"title": "Hızlı Prototip", "timeframe": "1-2 hafta", "description": "Konseptinizi işlevsel bir prototiple test edin", "features": ["Etkileşimli UI mockup'ları", "Temel işlevsellik", "Kullanıcı akışı uygulaması", "Pay<PERSON><PERSON>", "Flutter destekli MVP geli<PERSON>rme"]}, "landingpage": {"title": "İniş Sayfası Geliştirme", "timeframe": "2-4 hafta", "description": "Maks<PERSON>um erişilebilirlik ve performans için en yeni web teknolojileriyle WCAG 2.0 uyumlu iniş sayfaları", "features": ["WCAG 2.0 AA uyumluluğu", "<PERSON><PERSON><PERSON> kullanıcılar için kapsayıcı tasarım", "Ekran ok<PERSON>u uyumluluğu", "Yüksek performans metrikleri", "SEO optimize edilmiş yapı", "Duyarlı tasarım"]}, "architecture": {"title": "<PERSON><PERSON>", "timeframe": "1-2 hafta", "description": "Projenizin başarısı için sağlam bir temel", "features": ["Teknik özellikler", "Sistem mimarisi tasarımı", "Veritabanı şeması", "API belgelendirmesi", "Geliştirme yol haritası"]}, "consulting": {"title": "Teknik Danışmanlık", "timeframe": "<PERSON><PERSON><PERSON><PERSON>", "description": "Teknik kararlarınız için uzman rehberliği", "features": ["Teknoloji yığını önerileri", "<PERSON><PERSON>", "Performans optimizasyonu", "Güvenlik değerlendirmesi", "Ölçeklenebilirlik planlaması", "BAE ödeme ağ geçidi entegrasyonu"]}}}, "packages": {"title": "Paketlerimiz", "subtitle": "İhtiyaçlarınıza Göre Özelleştirilmiş Çözümler", "mvp": {"title": "MVP <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "timeframe": "3-4 hafta", "description": "Minimum Uygulanabilir Ürün ile fikrinizi hızla hayata geçirin", "features": ["Temel işlevsellik uygulaması", "Temel UI tasarımı", "Kullanıcı kimlik doğrulama", "<PERSON><PERSON> de<PERSON>", "Tek platform dağıtımı"]}, "prototype": {"title": "Prototip Paketi", "timeframe": "1-2 hafta", "description": "Fonksiyonel bir prototip ile konseptinizi test edin", "features": ["Etkileşimli UI mockupları", "Temel işlevsellik", "Kullanıcı akışı uygulaması", "Pay<PERSON><PERSON>"]}, "architecture": {"title": "<PERSON><PERSON>", "timeframe": "1-2 hafta", "description": "Projenizin başarısı için sağlam bir temel", "features": ["Teknik özellikler", "Sistem mimarisi tasarımı", "Veritabanı şeması", "API dokümantasyonu", "Geliştirme yol haritası"]}, "consulting": {"title": "Teknik Danışmanlık", "timeframe": "<PERSON><PERSON><PERSON><PERSON>", "description": "Teknik kararlarınız için uzman rehberliği", "features": ["Teknoloji yığını önerileri", "<PERSON><PERSON>", "Performans optimizasyonu", "Güvenlik değerlendirmesi", "Ölçeklenebilirlik planlaması"]}}, "solutionsPortfolio": {"title": "Çözümlerimiz", "subtitle": "Sektör Lideri Dijital Çözümlerimizi Keşfedin", "clickInstruction": "Detaylar ve tam ekran görünümü için kartı tıklayın", "imageCounter": "G<PERSON><PERSON>l {current} / {total}", "keyFeatures": "<PERSON><PERSON>", "problemsSolved": "Çöz<PERSON><PERSON>", "viewDetails": "Detayları Görüntüle", "screenshot": "Ekran Görüntüsü", "screenshots": "Ekran Görüntüleri", "categories": {"all": "<PERSON><PERSON><PERSON>", "aiAssistant": "Yapay Zeka Asistanı", "foodDelivery": "Yemek Teslimatı", "hospitality": "<PERSON><PERSON><PERSON><PERSON>", "medical": "<PERSON><PERSON><PERSON><PERSON>", "lifestyle": "Yaşam Tarzı Uygulamaları", "automotive": "Otomotiv"}, "items": {"spotzAiAssistant": {"title": "Spotz Yapay Zeka Asistanı", "description": "Yakındaki yerleri bul<PERSON>, <PERSON><PERSON><PERSON>mak, navigasyon ve acil durum hizmetleri sağlamak için yapay zeka destekli asistan.", "imageAlt": "Spotz Yapay Zeka Asistanı mobil uygulama arayüz ekran görüntüsü {index}"}, "foodDelivery": {"title": "Yemek Teslimatı", "description": "<PERSON>z<PERSON>el sipariş süreciyle eksiksiz yemek keşfi ve teslimat çözümü.", "imageAlt": "Yemek teslimatı uygulaması ekran görüntüsü {index}"}, "hostIQ": {"title": "HostIQ - Konaklama Süper Uygulaması", "description": "<PERSON><PERSON><PERSON> i<PERSON><PERSON>, misa<PERSON>r deneyimini ve hizmetleri yönetmek için hepsi bir arada çözüm.", "imageAlt": "HostIQ konaklama yönetim platformu ekran görüntüsü {index}"}, "businessManagement": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON>meler için pazar<PERSON> araçları, et<PERSON><PERSON> pay<PERSON>, hikaye paylaşımı ve rezervasyon yönetimi.", "imageAlt": "İşletme yönetimi uygulaması ekran görüntüsü {index}"}, "lumeusApp": {"title": "<PERSON><PERSON><PERSON>", "description": "Gelişmiş özelliklere sahip sosyal ağ ve bağlantı platformu.", "imageAlt": "Lumeus sosyal ağ uygulaması ekran görüntüsü {index}"}, "nearby": {"title": "Yakınlarda", "description": "Bölgenizdeki her şeyi bulmak için konum tabanlı keşif uygulaması.", "imageAlt": "Yakınlarda uygulaması arayüz ekran görüntüsü {index}"}, "toggCarControl": {"title": "Togg Araç Kontrolü", "description": "Togg araçları için akıllı araç kontrol ve yönetim sistemi.", "imageAlt": "Togg araç kontrol uygulaması ekran görüntüsü {index}"}, "socialMediaPlatform": {"title": "<PERSON><PERSON><PERSON> Medya Platformu", "description": "Etkileyici kullanıcı deneyimine sahip modern sosyal medya platformu.", "imageAlt": "Sosyal medya platformu arayüz ekran görüntüsü {index}"}, "default": {"imageAlt": "Portföy öğesi ekran görüntüsü {index}"}}, "solutions": {"aiAssistant": {"title": "Yapay Zeka Asistanı Çözümleri", "description": "Kullanıcı deneyimlerini geliştiren ve görevleri otomatikleştiren akıllı sanal asistanlar.", "features": ["Doğal Dil İşleme", "Bağlamsal Anlama", "<PERSON><PERSON>", "Çoklu Platform Desteği", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "problemsSolved": ["Bilgiye Erişim", "<PERSON><PERSON>", "Müşteri Hizmetleri Otomasyonu", "<PERSON><PERSON><PERSON><PERSON>"]}, "foodDelivery": {"title": "Yemek Teslimat Platformları", "description": "Restoranlar ve teslimat hizmetleri için uçtan uca sistemler.", "features": ["Sipariş Yönetimi", "Gerçek Zamanlı Takip", "Ödeme <PERSON>", "Restoran Kontrol Paneli", "Teslimat Optimizasyonu"], "problemsSolved": ["<PERSON><PERSON>", "Teslimat Lojistiği", "Sipariş Tamamlama", "Müşteri Sadakati"]}, "hospitality": {"title": "Konaklama Yönetim Çözümleri", "description": "<PERSON><PERSON><PERSON><PERSON>ini geliştiren ve operasyonları düzenleyen dijital sistemler.", "features": ["Rezervasyon Yönetimi", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Analitik Kontrol Paneli"], "problemsSolved": ["<PERSON><PERSON><PERSON><PERSON>", "Operasyonel <PERSON>lik", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"]}, "business": {"title": "İşletme Yönetim Sistemleri", "description": "Operasyonları ve büyümeyi yönetmek için kapsamlı platformlar.", "features": ["CRM Entegrasyonu", "<PERSON><PERSON><PERSON>", "Finansal Takip", "Çalışan <PERSON>", "Raporlama <PERSON>ları"], "problemsSolved": ["<PERSON><PERSON><PERSON>ç Optimizasyonu", "<PERSON><PERSON>", "Kaynak <PERSON>", "İş Zekası"]}, "social": {"title": "Sosyal Medya <PERSON>ı", "description": "Topluluklar için ilgi çekici sosyal ağ çözümleri.", "features": ["Kullanıcı Profilleri", "İçerik Paylaşımı", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "problemsSolved": ["Dijital Topluluk Oluşturma", "Kullanıcı Etkileşimi", "İçerik Keşfi", "<PERSON><PERSON><PERSON>"]}, "automotive": {"title": "Otomotiv Teknoloji Çözümleri", "description": "Araçlar için dijital arayüzler ve kontrol sistemleri.", "features": ["<PERSON><PERSON>", "Navigas<PERSON>", "Tanılama Araçları", "<PERSON><PERSON><PERSON><PERSON>", "IoT Bağlantısı"], "problemsSolved": ["<PERSON><PERSON>", "Navigasyon Zorlukları", "Bakım <PERSON>i", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}}}, "portfolio": {"title": "Uzmanlıklarımız", "subtitle": "Sektörler ve Çözüm Sunduğumuz Problemler", "all": "<PERSON><PERSON><PERSON>", "screenshot": "Ekran Görüntüsü", "screenshots": "Ekran Görüntüleri", "problemsWeSolve": "Çözdüğümüz Problemler", "noSectorsFound": "Seçilen filtre için sektör bulunamadı.", "categories": {"aiAssistant": "Yapay Zeka Asistanı", "foodDelivery": "Yemek Teslimatı", "hospitality": "<PERSON><PERSON><PERSON><PERSON>", "business": "İş", "social": "<PERSON><PERSON><PERSON>", "automotive": "Otomotiv"}, "sectors": {"assistant": "Asistan Uygulamaları", "food": "Yemek Siparişi ve Teslimatı", "hospitality": "<PERSON><PERSON><PERSON><PERSON>", "lifestyle": "Yaşam Tarzı Uygulamaları", "social": "<PERSON><PERSON><PERSON>", "automotive": "Otomotiv", "medical": "Tıp ve Sağlık Hizmetleri", "business": "İş Çözümleri"}, "sectorDescriptions": {"assistant": "Verimliliği artıran ve kişiselleştirilmiş öneriler sunan yapay zeka destekli asistanlar", "food": "Gerçek zamanlı takip özelliğine sahip kesintisiz yemek sipariş ve teslimat platformları", "hospitality": "<PERSON><PERSON><PERSON><PERSON> deneyimini geliştirmek için otel ve konaklama işletmelerine yönelik dijital çözümler", "lifestyle": "Günlük yaşamı, sağlığı ve kişisel gelişimi iyileştiren uygulamalar", "social": "İnsanları ve toplulukları ortak ilgi alanları üzerinden birbirine bağlayan platformlar", "automotive": "<PERSON><PERSON>, navigasyon ve sürücü asistanı için akıllı çözümler", "medical": "<PERSON>ta bakımını ve tıbbi operasyonları iyileştiren dijital sağlık çözümleri", "business": "İşlemleri kolaylaştıran ve üretkenliği artıran kurumsal uygulamalar"}, "problems": {"assistant": {"1": "<PERSON><PERSON><PERSON>", "2": "<PERSON><PERSON><PERSON><PERSON>", "3": "<PERSON><PERSON>"}, "food": {"1": "Sipariş yönetimi", "2": "Teslimat lojistiği", "3": "Restoran keşfi"}, "hospitality": {"1": "<PERSON><PERSON><PERSON><PERSON>", "2": "Hizmet optimizasyonu", "3": "Rezervasyon sistemleri"}, "lifestyle": {"1": "Sağlık takibi", "2": "Alışkanlık oluşturma", "3": "<PERSON><PERSON><PERSON><PERSON> organiza<PERSON>"}, "social": {"1": "Kullanıcı katılımı", "2": "İçerik keşfi", "3": "Topluluk oluşturma"}, "automotive": {"1": "<PERSON><PERSON>", "2": "Navigasyon optimizasyonu", "3": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "medical": {"1": "<PERSON><PERSON>", "2": "Sağlık izleme", "3": "<PERSON><PERSON><PERSON><PERSON> kayıt <PERSON>"}, "business": {"1": "İş akışı optimizasyonu", "2": "<PERSON><PERSON>", "3": "Takım işbirliği"}}, "viewDetails": "Detayları Görüntüle", "viewAllProjects": "<PERSON><PERSON><PERSON> Projeleri Gö<PERSON>"}, "clients": {"title": "Müşterilerimiz", "subtitle": "Birlikte Çalıştığımız Şirketler", "visitWebsite": "Web Sitesini Ziyaret <PERSON>t"}, "testimonials": {"title": "Müşteri Görüşleri", "subtitle": "Müşterilerimiz Ne Diyor", "readMore": "<PERSON><PERSON> fazla oku", "readLess": "<PERSON><PERSON> a<PERSON> g<PERSON>"}, "contact": {"title": "İletişim", "subtitle": "Bizimle İletişime Geçin", "name": "İsim", "email": "E-posta", "phone": "Telefon", "message": "<PERSON><PERSON>", "send": "<PERSON><PERSON>", "yourName": "Adınız", "yourEmail": "E-posta adresiniz", "subject": "<PERSON><PERSON>", "howCanIHelp": "Nasıl yardımcı olabilirim?", "yourMessageHere": "Mesajınızı buraya yazın", "getInTouch": "İletişime Geçin", "sendMessage": "<PERSON><PERSON>", "schedule": "Görüşme <PERSON>", "freeConsultation": "Ücretsiz 15 dakikalık danışmanlık randevusu alın", "location": "<PERSON><PERSON>", "submitButton": "<PERSON><PERSON><PERSON>", "sending": "Gönderiliyor...", "messageSent": "Mesaj Gönderildi!", "errorTryAgain": "<PERSON><PERSON>, lütfen tekrar deneyin", "orSchedule": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> takvim bağlantısını kullanarak toplantı planlayın"}, "footer": {"copyright": "© 2025 Innovatio. Tüm hakları saklıdır.", "description": "Yenilikçi teknoloji aracılığıyla işletmeleri dönüştüren son teknoloji mobil ve web uygulamaları geliştiriyoruz.", "quickLinks": "Hızlı Bağlantılar", "footerContact": "İletişim", "legal": "<PERSON><PERSON>", "newsletter": "<PERSON><PERSON><PERSON>", "newsletterDesc": "Güncellemeler ve içgörüler almak için bültenimize abone olun.", "emailPlaceholder": "E-posta adresinizi girin", "subscribe": "<PERSON><PERSON>", "builtWith": "<PERSON><PERSON>ıştır", "and": "ve", "downloadCV": "Özgeçmişim", "englishCV": "İngilizce", "germanCV": "Almanca"}, "cookies": {"title": "Çerez İzni", "description": "Gezinme deneyiminizi <PERSON>, kişiselleştirilmiş reklamlar veya içerik sunmak ve trafiğimizi analiz etmek için çerezleri kullanıyoruz. \"Tümünü Kabul Et\" düğmesine tıklayarak çerezleri kullanmamıza izin vermiş olursunuz.", "acceptAll": "Tümünü Kabul Et", "decline": "<PERSON><PERSON>", "customize": "Ö<PERSON>leş<PERSON>r", "necessary": "Gerek<PERSON>", "necessaryDesc": "<PERSON><PERSON> çerezler, web sitesinin düzgün çalışması için gereklidir ve devre dışı bırakılamaz.", "analytics": "<PERSON><PERSON><PERSON>zle<PERSON>", "analyticsDesc": "<PERSON><PERSON>zle<PERSON>, ziyaretçilerin web sitemizle nasıl etkileşimde bulunduğunu anlamamıza ve hizmetlerimizi geliştirmemize yardımcı olur.", "marketing": "<PERSON><PERSON><PERSON><PERSON>", "marketingDesc": "<PERSON><PERSON>, il<PERSON><PERSON> reklamları göstermek için ziyaretçileri web siteleri arasında takip etmek için kullanılır.", "functional": "İşlevsel Çerezler", "functionalDesc": "<PERSON><PERSON>, web sitemizde gelişmiş işlevsellik ve kişiselleştirme sağlar.", "save": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": "Çerez Ayarları", "close": "Ka<PERSON><PERSON>", "cookiePolicy": "Çerez Politikası", "privacyPolicy": "Gizlilik Politikası"}, "heroParallax": {"title": "En İyi Geliştirme Stüdyosu", "subtitle": "En son teknolojiler ve çerçevelerle güzel ürünler oluşturuyoruz. Harika ürünler oluşturmayı seven tutkulu geliştiriciler ve tasarımcılardan oluşan bir ekibiz.", "products": {"mobileApp": "<PERSON><PERSON>", "webDev": "Web Geliştirme", "uiux": "UI/UX Tasarımı", "ecommerce": "<PERSON><PERSON>t<PERSON><PERSON>", "ai": "Yapay Zeka Entegrasyonu", "cloud": "Bulut Çözümleri", "devops": "DevOps", "dataAnalytics": "<PERSON><PERSON>", "blockchain": "Blockchain <PERSON>", "arvr": "AR/VR Çözümleri", "customSoftware": "<PERSON>zel <PERSON>ı<PERSON>", "mobileGame": "<PERSON><PERSON>", "iot": "IoT Çözümleri", "api": "API Geliştirme", "cybersecurity": "Siber Güvenlik"}}, "featuresSection": {"features": [{"title": "Geliştiriciler için ta<PERSON>ı", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ve yapanlar için tasarlandı.", "icon": "IconTerminal2"}, {"title": "Kullanım kolaylığı", "description": "Bir <PERSON> kullanmak kadar kolay ve bir tane almak kadar pahalı.", "icon": "IconEaseInOut"}, {"title": "<PERSON><PERSON>", "description": "Fiyatlarımız piyasadaki en iyisidir. Üst sınır yok, kilitlenme yok, kredi kartı gerekmez.", "icon": "Icon<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"title": "%100 Çalışma süresi garantisi", "description": "Kimse bizi alaşağı edemez.", "icon": "IconCloud"}, {"title": "Çok kiracılı mimari", "description": "Yeni koltuklar almak yerine şifreleri paylaşabilirsiniz.", "icon": "IconRouteAltLeft"}, {"title": "7/24 Müşteri Desteği", "description": "Zamanın %100'ünde hizmetinizdeyiz. En azından yapay zeka temsilcilerimiz.", "icon": "IconHelp"}, {"title": "Para iade garantisi", "description": "<PERSON><PERSON><PERSON> EveryAI'y<PERSON>, sizi bizi beğenmeniz için ikna edeceğiz.", "icon": "IconAdjustmentsBolt"}, {"title": "<PERSON><PERSON> di<PERSON> her <PERSON>ey", "description": "Metin fikirlerim tükendi. İçten özürlerimi kabul edin.", "icon": "IconHeart"}]}, "seo": {"meta": {"home": {"title": "Mobil Uygulama Geliştirme ve Dijital Çözümler | Innovatio-Pro", "description": "Flutter ile profesyonel mobil uygulama geliştirme, yapay zeka entegrasyonu ve dijital çözümler. Innovatio-Pro'dan MVP geli<PERSON>e, prototipleme ve full-stack geliştirme.", "keywords": "mobil <PERSON><PERSON> gel<PERSON>, <PERSON><PERSON><PERSON>, MVP gel<PERSON>, ya<PERSON><PERSON> <PERSON> ente<PERSON>, di<PERSON><PERSON>, proto<PERSON><PERSON><PERSON>, full-stack gel<PERSON><PERSON><PERSON><PERSON><PERSON>, Innovatio-Pro"}, "templates": {"title": "Mobil Uygulama Çözümleri ve Yapay Zeka Entegrasyonu - Innovatio-Pro", "description": "<PERSON>bil uygulama çözümlerimizi, yapay zeka entegrasyonu ve dijital platformları keşfedin. <PERSON><PERSON><PERSON>, MVP geliştirm<PERSON> ve yenilikçi mobil teknolojilerde uzmanlaşmış.", "keywords": "mobil uygu<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, MVP gel<PERSON>, di<PERSON><PERSON>, mobil <PERSON>, u<PERSON><PERSON><PERSON><PERSON> gel<PERSON>e"}, "services": {"title": "<PERSON><PERSON>a Geliştirme Hizmetleri - <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> ve Dijital Çözümler", "description": "<PERSON><PERSON><PERSON>, yapay zeka entegrasyonu ve modern teknolojiler kullanarak profesyonel mobil uygulama geliştirme hizmetleri. MVP, prototipleme ve full-stack mobil çözümlerde uzmanlaşmış.", "keywords": "mobil <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, ya<PERSON><PERSON> ente<PERSON>, mobil <PERSON>, u<PERSON><PERSON><PERSON>a gel<PERSON> hi<PERSON>, di<PERSON><PERSON>"}, "about": {"title": "Innovatio-Pro Hakkında - Mobil Uygulama Geliştirme ve Yapay Zeka Uzmanı", "description": "Flutter ile mobil uygulama geliştirme, yapay zeka entegrasyonu ve yenilikçi dijital çözümler uzmanınız Innovatio-Pro hakkında bilgi edinin.", "keywords": "Innovatio-Pro, mobil uygulama gel<PERSON>isi, <PERSON><PERSON><PERSON>, ya<PERSON><PERSON> zeka entegra<PERSON>u, di<PERSON><PERSON>, uygulama geliştirme şirketi"}, "contact": {"title": "İletişim - Mobil Uygulama Geliştirme ve Dijital Çözümler | Innovatio-Pro", "description": "Profesyonel mobil uygulama geliştirme, yapay zeka entegrasyonu ve dijital çözümler için Innovatio-Pro ile iletişime geçin. Bir sonraki projeniz için ücretsiz danışmanlık mevcut.", "keywords": "mobil uygulama geliştirme il<PERSON>im, <PERSON><PERSON><PERSON> gelişti<PERSON><PERSON>, yapay zeka entegrasyonu hizmetleri, dijital <PERSON>özümler talebi, Innovatio-Pro iletişim"}, "pricing": {"title": "Mobil <PERSON>a Geliştirme <PERSON> - MVP, Prototipleme ve Yapay Zeka Çözümleri", "description": "<PERSON><PERSON>, MVP <PERSON>, prototipleme ve yapay zeka entegrasyonu için şeffaf fiyatlandırma. Hızlı prototiplerden full-stack mobil çözümlere kadar.", "keywords": "mobil uygulama geli<PERSON><PERSON><PERSON><PERSON>, MVP gel<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> uygu<PERSON> fiya<PERSON>arı, yapay zeka entegrasyonu oranları, prototipleme fiyatları, uygulama geliştirme teklifleri"}, "faq": {"title": "SSS - Mobil Uygulama Geliştirme ve Dijital Çözümler | Innovatio-Pro", "description": "<PERSON><PERSON> uygulama gel<PERSON>e <PERSON>, <PERSON><PERSON><PERSON> gel<PERSON>, yapay zeka entegrasyonu ve dijital çözümler hakkında sık sorulan sorular. Innovatio-Pro'dan destek ve cevaplar.", "keywords": "mobil uygulama geliştirme SSS, Flutter geliştirme soruları, yapay zeka entegrasyonu yardımı, dijital çözümler desteği, uygulama geliştirme cevapları"}}, "openGraph": {"siteName": "Innovatio-Pro - Mobil Uygulama Geliştirme ve Dijital Çözümler", "defaultImage": "/images/og-innovatio-pro-tr.jpg", "defaultImageAlt": "Innovatio-Pro - Flutter ile Mobil Uygulama Geliştirme ve Yapay Zeka Entegrasyonu"}, "jsonLd": {"organization": {"name": "Innovatio-Pro", "alternateName": "Innovatio-Pro Mobil Geliştirme", "description": "Flutter ile mobil uygulama geliştirme, yapay zeka entegrasyonu ve modern işletmeler için yenilikçi dijital çözümler uzmanı.", "url": "https://innovatio-pro.com", "telephone": "+49-175-9918357", "email": "<EMAIL>", "address": {"streetAddress": "Remote Office", "addressLocality": "Wyoming", "addressRegion": "Wyoming", "postalCode": "82001", "addressCountry": "US"}, "sameAs": ["https://github.com/innovatio-pro", "https://linkedin.com/company/innovatio-pro"]}}}, "blog": {"title": "Teknoloji İçgörüleri", "description": "Mobil geliştirmedeki en son trendleri, içgörüleri ve yenilikleri keşfedin. Flutter en iyi uygulamalarından yapay zeka entegrasyon stratejilerine - teknoloji devriminde öncü kalın.", "categoriesTitle": "<PERSON><PERSON><PERSON>", "categoriesDescription": "Farklı teknoloji alanlarındaki makaleleri keşfedin", "subscriptionTitle": "Bilgili <PERSON>", "subscriptionDescription": "En son F<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, yapay zeka trendleri ve teknoloji yeniliklerini doğrudan gelen kutunuza alın.", "readMore": "Devamını Oku", "noArticles": "Makale bulunamadı", "author": "<PERSON><PERSON>", "readingTime": "<PERSON><PERSON>ü<PERSON>", "views": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tags": "<PERSON><PERSON><PERSON><PERSON>", "floatingButton": {"tooltip": "Teknoloji Blogu", "currentArticle": "<PERSON><PERSON><PERSON><PERSON>", "viewAll": "<PERSON><PERSON><PERSON>", "newPosts": "<PERSON><PERSON>"}, "categories": {"all": "<PERSON><PERSON><PERSON>", "company": "Şirket Hakkında", "flutter": "Flutter Geliştirme", "mobile": "<PERSON><PERSON>", "ai": "Yapay Zeka Entegrasyonu", "performance": "Performans", "caseStudies": "Vaka Çalışmaları", "trends": "<PERSON><PERSON><PERSON><PERSON>"}}}