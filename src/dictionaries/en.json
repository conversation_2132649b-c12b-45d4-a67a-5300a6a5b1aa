{"nav": {"home": "Home", "about": "About", "solutions": "Solutions", "services": "Services", "techStack": "Tech Stack", "portfolio": "Portfolio", "contact": "Contact", "chatWithUs": "Chat With Us", "bookConsultation": "Book a Consultation", "apps": "Apps", "pricing": "Pricing", "prices": "Prices", "calculator": "Pricing Calculator", "priceCalculator": "Price Calculator", "clients": "Clients", "testimonials": "Testimonials", "blog": "Blog"}, "hero": {"title": "🚀 Your App – Fast. Scalable. Profitable.", "subtitle": "From idea to App Store in record time. We build MVPs, AI-ready apps & full-scale products.", "tagline": "8 Years Experience, 15+ Successful Projects, 100% Customer Satisfaction", "description": "While others plan for months, I develop. Personal, direct, no detours. Fixed price, highest quality, on-time delivery.", "typing": {"sequence1": "App Idea → Development → App Store ✓", "sequence2": "Fixed Price. Highest Quality. Zero Risk.", "sequence3": "TOGG to <PERSON><PERSON><PERSON><PERSON> trust me."}, "cta": {"primary": "Start your project", "secondary": "Fixed Price in 24h", "calculator": "Instant Price Check (60 Sec.)", "startProject": "Start your project", "freeConsultation": "Free consultation", "calculateCost": "Calculate costs"}, "trustedBy": {"title": "Trusted by industry leaders"}, "typeAnimation": ["Flutter Apps - One Code for iOS & Android 📱", 2000, "Native Performance - Modern UI/UX 🚀", 2000, "From Idea to App Store 🏆", 2000, "Your Partner for Successful Apps ✨", 2000, "", 500], "metrics": {"development": {"label": "Faster Development", "value": "40%", "description": "faster development", "howTitle": "How:", "howExplanation": "Using newest IDE with AI support, automated code generation, and intelligent code completion."}, "timeToMarket": {"label": "Faster Time-to-Market", "value": "50%", "description": "faster publishing", "howTitle": "How:", "howExplanation": "Fast lane CI/CD pipeline, automated testing, AI-powered code review, and streamlined deployment process."}, "costSaving": {"label": "Cost Saving", "value": "30%", "description": "cost savings", "howTitle": "How:", "howExplanation": "Using Flutter for cross-platform development, optimized cloud resources, and efficient development practices."}}}, "about": {"title": "Why Me?", "subtitle": "8 Years, 15+ Apps, 0 Failed Projects", "description": "Real developer, real results. No agency fluff, no junior team, just me personally building your app.", "vision": "My Story", "visionDesc": "2016: Apps cost €50,000 and took a year. Today I deliver MVPs for €8,500 in 4 weeks. Companies like Union Investment and TOGG trust me because I deliver what I promise.", "mission": "My Promise", "missionDesc": "Your app arrives on time and on budget. Period. I work personally on every project - no team of juniors, no surprises. Flutter has been my daily bread for 5 years.", "founderTitle": "<PERSON>", "founderDesc": "I live code. 8 years building apps from startups to corporations. My passion: making impossible deadlines possible. Latest: fintech MVP in 3 weeks instead of 3 months.", "skills": "Skills", "projects": "Projects", "testimonials": "Testimonials", "experience": "Years Experience", "clients": "Happy Clients", "transformBusiness": "Transforming businesses through technology", "createSolutions": "Creating future-proof digital solutions", "stayingAhead": "Staying at the forefront of technology", "exceptionalUX": "Delivering exceptional user experiences", "highPerformance": "Building high-performance applications", "solvingChallenges": "Solving real business challenges with technology", "flutterExpert": "Expert", "webDevAdvanced": "Advanced", "aiIntegration": "Integration", "projectsCompleted": "Projects Completed", "personalDedication": "Personal", "dedication": "Dedication", "qualityFocused": "Quality Focused", "personalService": "Personal Service", "focusedApproach": "Focused Approach", "dedicatedService": "Dedicated Service", "clientReviews": "Client Reviews", "focusedService": "Focused Service", "longTerm": "Long-term", "partnerships": "Partnerships", "privacyFocused": "Privacy Focused", "secureServices": "Secure Development", "personalAttention": "Personal Attention", "dedicatedDeveloper": "Dedicated Developer", "securityFocused": "Security Focused", "privacyRespected": "Privacy Respected", "quality": "Quality", "averageDelivery": "Average Delivery", "metrics": {"yearsExperience": "8+", "projectsCompleted": "15+", "happyClients": "12+", "clientRating": "5.0/5", "deliveryTimeframe": "3-4 weeks", "personalValue": "Personal", "qualityValue": "Quality", "longTermValue": "Long-term"}}, "advantages": {"title": "OUR SOLUTIONS", "subtitle": "How We Help You Build Successful Mobile Apps", "description": "We leverage cutting-edge technologies across multiple domains to deliver robust and scalable solutions for your business needs.", "speed": "Rapid Development", "speedDesc": "We deliver solutions quickly without compromising on quality", "stability": "Reliable Applications", "stabilityDesc": "Our applications are built for stability and performance", "cost": "Cost Efficiency", "costDesc": "Optimized development process saves you time and money", "timeToMarket": "Faster Time-to-Market", "timeToMarketDesc": "Launch your product quickly and stay ahead of competition", "aiIntegration": "AI Integration", "aiIntegrationDesc": "Enhance your business with powerful AI capabilities", "development": "Full-Stack Development", "developmentTime": "4-12 weeks, varies with complexity", "developmentDesc": "Complete mobile application development from concept to deployment, with full backend integration and advanced features.", "mvp": "MVP Development", "mvpTime": "2-4 weeks, varies with complexity", "mvpDesc": "Launch quickly with a Minimum Viable Product featuring core functionality to validate your concept and attract early users or investors.", "prototype": "Rapid Prototyping", "prototypeTime": "1-2 weeks, varies with complexity", "prototypeDesc": "Test concepts quickly with interactive prototypes before committing to full development, saving time and resources.", "qa": "Quality Assurance", "qaTime": "Ongoing, scales with project complexity", "qaDesc": "Comprehensive testing across devices and platforms to ensure your app performs flawlessly with automated and manual testing protocols.", "consulting": "Technical Consulting", "consultingTime": "As needed, based on project complexity", "consultingDesc": "Expert advice on technology stack, architecture decisions, and implementation strategies to optimize your mobile application.", "uiux": "UI/UX Design", "uiuxTime": "2-3 weeks, varies with complexity", "uiuxDesc": "User-centered design that balances aesthetics with functionality, creating intuitive and engaging mobile experiences.", "maintenance": "Maintenance & Support", "maintenanceTime": "Ongoing, scales with project complexity", "maintenanceDesc": "Long-term support with regular updates, performance optimization, and feature enhancements to keep your app competitive.", "analytics": "Analytics Integration", "analyticsTime": "1-2 weeks, varies with complexity", "analyticsDesc": "Data tracking implementation to gain actionable insights into user behavior, enabling data-driven decisions for your app.", "training": "Team Training", "trainingTime": "1-2 weeks, varies with complexity", "trainingDesc": "Comprehensive training for your team on maintaining and extending your application after handover.", "developmentEfficiency": "Development Efficiency", "timeToMarketReduction": "Time-to-Market Reduction", "conceptValidation": "Concept Validation", "bugFreeRate": "Bug-Free Rate", "technicalImprovement": "Technical Improvement", "userSatisfaction": "User Satisfaction", "appUptime": "App Uptime", "dataAccuracy": "Data Accuracy", "knowledgeRetention": "Knowledge Retention", "developmentInfo": {"title": "Development Timeframes", "simpleApp": {"title": "Simple App", "examples": "Examples: To-do lists, calculators, simple information apps without backend.", "features": ["Few screens (3-5)", "No or minimal backend integration", "Standard UI components", "No complex animations or functions"], "timeline": {"total": "Development time: 4-8 weeks", "frontend": "Frontend: 2-4 weeks", "backend": "Backend (if needed): 1-2 weeks", "testing": "Testing and deployment: 1-2 weeks"}}, "mediumApp": {"title": "Medium App", "examples": "Examples: E-commerce apps, social media apps with basic features, apps with user registration and database integration.", "features": ["6-15 screens", "Backend integration (e.g., REST or GraphQL APIs)", "User registration and authentication", "Database for user and app data", "Some animations and interactive elements", "Push notifications"], "timeline": {"total": "Development time: 8-16 weeks", "frontend": "Frontend: 4-6 weeks", "backend": "Backend: 3-5 weeks", "testing": "Testing and deployment: 2-3 weeks"}}, "complexApp": {"title": "Complex App", "examples": "Examples: Apps like Uber, Instagram, or banking apps with advanced features.", "features": ["15+ screens", "Highly interactive user interface", "Real-time features (e.g., live tracking, chat)", "Third-party API integration (e.g., payment gateways, card APIs)", "Scalable backend with cloud integration", "Security features (e.g., encryption, two-factor authentication)", "Offline functionality"], "timeline": {"total": "Development time: 16-32 weeks or longer", "frontend": "Frontend: 6-10 weeks", "backend": "Backend: 6-12 weeks", "testing": "Testing and deployment: 4-6 weeks"}}, "factors": {"title": "Factors Affecting Development Time", "teamSize": "Team size: A larger team (e.g., separate developers for frontend, backend, and QA) can speed up development. A single developer needs more time.", "technology": "Technology: Native development (e.g., Swift for iOS, <PERSON><PERSON>in for Android) often takes longer than cross-platform approaches like Flutter or React Native. Cross-platform frameworks can reduce development time by 30-40%.", "requirements": "Requirements and changes: Frequent changes or unclear requirements can extend development time.", "testing": "Testing and debugging: Complex apps require more time for testing, especially on multiple platforms (iOS and Android).", "design": "Design: Simple designs require less time, while custom, animated designs increase development time."}, "summary": "Summary: Simple App: 4-8 weeks. Medium App: 8-16 weeks. Complex App: 16-32 weeks or longer.", "aiComparison": "With our AI-powered development and Flutter, we can reduce these timelines by 40-60% while maintaining high quality and performance."}}, "services": {"title": "Our Technology Stack", "subtitle": "Comprehensive Digital Solutions", "description": "We leverage cutting-edge technologies across multiple domains to deliver robust and scalable solutions for your business needs.", "frontend": "Frontend Development", "frontendDesc": "Building responsive and interactive user interfaces with modern web technologies", "backend": "Backend Development", "backendDesc": "Creating robust server-side solutions and APIs for scalable applications", "mobile": "Mobile Development", "mobileDesc": "Developing cross-platform mobile applications with native performance", "ai": "AI & Machine Learning", "aiDesc": "Integrating intelligent features and automation into applications", "mobileApps": "Mobile App Development", "mobileAppsDesc": "Cross-platform applications with Flutter for iOS and Android", "webDev": "Web Development", "webDevDesc": "Modern, responsive websites and web applications", "uiuxDesign": "UI/UX Design", "uiuxDesignDesc": "Intuitive, user-focused design that delights customers", "consulting": "Technical Consulting", "consultingDesc": "Expert advice on technology strategy and implementation", "aiSolutions": "AI Integration", "aiSolutionsDesc": "Incorporate AI capabilities to enhance your business", "viewAll": "View All Services"}, "serviceSection": {"title": "Our Services", "subtitle": "Tailored Digital Solutions for Your Business", "description": "We offer a comprehensive range of digital services to help businesses thrive in today's competitive landscape. Our expertise spans across multiple domains to deliver innovative and effective solutions.", "viewAll": "Discuss Your Project", "comparisonTitle": "Why Choose Us?", "comparisonSubtitle": "See how we compare to traditional development approaches", "timeComparison": {"title": "Development Time", "traditional": "16 weeks", "withUs": "10 weeks", "savings": "40%"}, "costComparison": {"title": "Project Cost", "traditional": "€50,000", "withUs": "€25,000", "savings": "50%"}, "qualityComparison": {"title": "Performance", "traditional": "Standard", "withUs": "Optimized", "savings": "35% faster"}, "mvp": {"title": "MVP Development", "description": "Launch quickly with a Minimum Viable Product featuring core functionality to validate your concept and attract early users.", "timeframe": "3-4 weeks", "benefits": ["Faster market entry with essential features", "Cost-effective approach to validate business ideas", "Iterative development based on real user feedback"]}, "prototype": {"title": "Rapid Prototyping", "description": "Test concepts quickly with interactive prototypes before committing to full development, saving time and resources.", "timeframe": "1-2 weeks", "benefits": ["Validate ideas with minimal investment", "Gather user feedback early in the process", "Refine concepts before full development"]}, "fullstack": {"title": "Full-Stack Mobile App Development", "description": "Complete mobile application development from concept to deployment with integrated frontend and backend solutions.", "timeframe": "4-12 weeks", "benefits": ["End-to-end development expertise", "Seamless integration between all system components", "Comprehensive testing and quality assurance"]}, "homepage": {"title": "Homepage & Landing Pages", "description": "Professional websites and landing pages that showcase your work and convert visitors into customers.", "timeframe": "2-3 weeks", "benefits": ["Conversion-optimized design", "SEO-friendly architecture", "Mobile-responsive layouts"]}, "landingpage": {"title": "Accessible Landing Pages", "description": "WCAG 2.0 compliant landing pages with the latest web technologies for maximum accessibility and performance.", "timeframe": "2-4 weeks", "benefits": ["WCAG 2.0 AA compliance", "Inclusive design for all users", "Screen reader compatibility", "High-performance metrics"]}, "qa": {"title": "Quality Assurance", "description": "Comprehensive testing across devices and platforms to ensure your app performs flawlessly with automated and manual testing.", "timeframe": "Ongoing", "benefits": ["Bug-free user experience", "Performance optimization", "Cross-platform compatibility"]}, "consulting": {"title": "Technical Consulting", "description": "Expert advice on technology strategy and implementation to help you make informed decisions for your digital projects.", "timeframe": "As needed", "benefits": ["Technology stack recommendations", "Architecture planning and review", "Performance and security optimization"]}, "architecture": {"title": "Project Architecture", "description": "Solid foundation for your project's success with well-designed system architecture and technical specifications.", "timeframe": "1-2 weeks", "benefits": ["Scalable system design", "Future-proof technology choices", "Clear development roadmap"]}, "aiSolutions": {"title": "AI Integration", "description": "Incorporate AI capabilities to enhance your business applications with intelligent features and automation.", "timeframe": "2-4 weeks", "benefits": ["Personalized user experiences", "Automated workflows and processes", "Data-driven insights and predictions"]}}, "prices": {"title": "Our Pricing", "subtitle": "Transparent Pricing for Every Stage", "description": "Choose from our predefined packages or customize them to fit your project requirements. Bundle services for a 15% discount.", "caseStudyTitle": "Case Study: 40% Faster Time-to-Market", "caseStudyDescription": "Our Wyoming-based fintech client launched their MVP 40% faster than industry average, allowing them to secure additional funding and accelerate growth.", "promotionTitle": "Bundle Services & Save 15%", "promotionDescription": "Combine any two or more services and receive a 15% discount on your total project cost.", "calculatorTitle": "Need a Custom Quote?", "calculatorDescription": "Use our interactive pricing calculator to get a detailed estimate tailored to your specific project requirements.", "calculatorButton": "Open Pricing Calculator", "discussButton": "Discuss Your Project", "contactButton": "Contact Us", "pricingDisclaimer": "* Prices may vary based on project requirements and additional services. Contact us for a custom quote tailored to your specific needs.", "disclaimer": "* Prices may vary based on project requirements and additional services. Contact us for a custom quote tailored to your specific needs.", "contactUs": "Contact Us", "priceVariesInfo": "Price may vary based on project complexity, additional requirements, and timeline constraints. Contact us for a detailed quote.", "calculator": {"title": "Pricing Calculator", "subtitle": "Get a custom quote tailored to your needs", "back": "Back", "selectServiceType": "Select service type", "stepIndicator": "Step", "continueButton": "Continue", "getQuoteButton": "Get Quote", "startOver": "Start Over", "calculatorTypes": {"mvp": {"name": "MVP Development", "description": "Calculate the cost of building your Minimum Viable Product", "tagline": "Get your app to market quickly with core features"}, "prototype": {"name": "Rapid Prototype", "description": "Estimate your prototype development costs", "tagline": "Test your idea with a functional prototype"}, "homepage": {"name": "Homepage & Landing", "description": "Get pricing for your website or landing page", "tagline": "Create a professional online presence"}, "consulting": {"name": "Technical Consulting", "description": "Estimate consulting hours and packages", "tagline": "Expert guidance for your technical challenges"}}, "mvpCalculator": {"title": "MVP Development Calculator", "subtitle": "Configure your MVP requirements to get a custom quote", "industry": "Select Your Industry", "timeline": "Development Timeline", "features": "Select App Features", "featuresInfo": "Select the features you need for your <strong>fully functional, App Store ready</strong> MVP. These are your core features.", "addFeatures": "Tap to add features...", "customFeatures": "Additional Requirements", "customFeaturesPlaceholder": "Describe any custom features or requirements...", "needsReview": "I'd like a code review of my existing project", "industries": {"ecommerce": "E-commerce", "health": "Healthcare", "finance": "Finance", "social": "Social Media", "productivity": "Productivity", "other": "Other"}, "timelines": {"normal": {"label": "Standard (4-5 weeks)", "description": "Regular development pace"}, "fast": {"label": "Accelerated (3-4 weeks)", "description": "30% faster delivery"}, "urgent": {"label": "Priority (2-3 weeks)", "description": "Dedicated team, fastest delivery"}}, "featureCategories": {"auth": "Authentication", "user": "User Management", "common": "Common Features", "payment": "Payment", "device": "Device Features", "data": "Data & Storage", "social": "Social Features"}}, "prototypeCalculator": {"title": "Rapid Prototype Calculator", "subtitle": "Configure your prototype requirements for a visual and navigational prototype", "uiOnly": "UI Only", "timeline": "Development Timeline", "complexity": "Prototype Complexity", "userRoles": "Number of User Roles", "userRolesBasic": "Basic (1)", "userRolesComplex": "Complex (5+)", "userRolesExamples": "Examples: User, <PERSON><PERSON>, Editor, Viewer, Moderator", "features": "Select Prototype Features", "featuresInfo": "Select the features you need for your visual prototype. These define the scope of your prototype.", "addFeatures": "Tap to add features...", "integrations": "UI Integrations", "integrationsInfo": "Select the UI integrations to include in your prototype.", "addIntegrations": "Tap to add integrations...", "customFeatures": "Additional Requirements", "customFeaturesPlaceholder": "Describe any custom features or requirements...", "needsReview": "I'd like a design review of my existing mockups", "flutterPrototype": "Flutter-Powered Prototype", "flutterInfo": "Our Flutter-powered prototypes offer superior performance and can be easily converted to a full MVP later.", "complexityLevels": {"simple": {"label": "Simple", "description": "Basic user flows, minimal screens"}, "medium": {"label": "Medium", "description": "Multiple user flows, standard screens"}, "complex": {"label": "Complex", "description": "Advanced UI, many screens"}}, "timelines": {"normal": {"label": "Standard (1-2 weeks)", "description": "Regular development pace"}, "fast": {"label": "Accelerated (5-7 days)", "description": "30% faster delivery"}, "urgent": {"label": "Priority (3-4 days)", "description": "Dedicated team, fastest delivery"}}}, "homepageCalculator": {"title": "Homepage & Landing Page Calculator", "subtitle": "Configure your website requirements to get a custom quote", "pageCount": "Number of Pages", "pageCountLanding": "<PERSON> Page (1)", "pageCountFull": "Full Website (15+)", "pageCountExamples": "Example pages: Home, About, Services, Portfolio, Contact, Blog, etc.", "timeline": "Development Timeline", "features": "Website Features", "featuresInfo": "Select the features you need for your <strong>responsive website</strong>. Required features cannot be deselected.", "addFeatures": "Tap to add features...", "seo": "Search Engine Optimization", "seoDescription": "Basic SEO setup (meta tags, sitemap, etc.)", "multilingual": "Multilingual Support", "multilingualDescription": "Support for multiple languages", "customFeatures": "Additional Requirements", "customFeaturesPlaceholder": "Describe any custom features or requirements...", "needsReview": "I'd like a review of my existing website", "timelines": {"normal": {"label": "Standard (2-3 weeks)", "description": "Regular development pace"}, "fast": {"label": "Accelerated (1-2 weeks)", "description": "30% faster delivery"}, "urgent": {"label": "Priority (3-5 days)", "description": "Dedicated team, fastest delivery"}}}, "consultingCalculator": {"title": "Technical Consulting Calculator", "subtitle": "Configure your consulting needs to get a custom quote", "projectPhase": "Project Phase", "teamSize": "Team Size", "teamSizeSolo": "Solo (1)", "teamSizeLarge": "Large Team (10+)", "developer": "Developer", "developers": "Developers", "expertise": "Expertise Needed", "addExpertise": "Tap to add expertise areas...", "duration": "Estimated Duration", "durationShort": "Short-term (1 week)", "durationLong": "Long-term (12+ weeks)", "week": "Week", "weeks": "Weeks", "expertConsultation": "Expert Technical Consultation", "consultationDescription": "Our technical consulting services leverage decades of industry experience across a wide range of technologies and industries. We provide both strategic guidance and hands-on implementation support.", "ctoLevel": "CTO-level expertise", "flexibleScheduling": "Flexible scheduling", "projectPhases": {"planning": "Planning & Strategy", "development": "Active Development", "maintenance": "Maintenance & Support", "optimization": "Performance Optimization"}}, "leadForm": {"title": "Get Your {calculatorType} Quote", "subtitle": "Complete the form below to receive a detailed quote via email", "fullName": "Full Name", "namePlaceholder": "<PERSON>", "nameRequired": "Name is required", "email": "Email Address", "emailPlaceholder": "<EMAIL>", "emailRequired": "Email is required", "emailInvalid": "Please enter a valid email address", "company": "Company Name", "companyOptional": "(Optional)", "companyPlaceholder": "Acme Inc.", "agreeTerms": "I agree to receive the price quote via email", "secureInfo": "Your information is secure and will not be shared", "termsRequired": "You must agree to the terms", "getQuote": "Get Quote"}, "pricingResult": {"title": "Your Custom Quote", "subtitle": "Based on your requirements", "estimatedCost": "Estimated Cost", "estimatedTime": "Estimated Timeline", "weeks": "weeks", "days": "days", "hours": "hours", "startingFrom": "Starting from", "hourlyRate": "Hourly rate", "includedFeatures": "Included Features", "nextSteps": "Next Steps", "nextStepsDescription": "We've sent a detailed quote to your email. Our team will contact you shortly to discuss your project in more detail.", "startOver": "Start Over", "contactUs": "Contact Us"}}, "packages": {"mvp": {"title": "MVP Development", "timeframe": "3-4 weeks", "description": "Launch your idea quickly with a Minimum Viable Product", "features": ["Core functionality implementation", "Basic UI design", "User authentication", "Data storage solution", "One platform deployment", "Worldwide Compliance Ready"]}, "prototype": {"title": "Rapid Prototype", "timeframe": "1-2 weeks", "description": "Test your concept with a functional prototype", "features": ["Interactive UI mockups", "Basic functionality", "User flow implementation", "Stakeholder presentation", "Flutter-driven MVP development"]}, "landingpage": {"title": "Landing Page Development", "timeframe": "2-4 weeks", "description": "WCAG 2.0 compliant landing pages with the latest web technologies", "features": ["WCAG 2.0 AA compliance", "Inclusive design for all users", "Screen reader compatibility", "High-performance metrics", "SEO optimized structure", "Responsive design"]}, "architecture": {"title": "Project Architecture", "timeframe": "1-2 weeks", "description": "Solid foundation for your project's success", "features": ["Technical specifications", "System architecture design", "Database schema", "API documentation", "Development roadmap"]}, "consulting": {"title": "Technical Consulting", "timeframe": "Ongoing", "description": "Expert guidance for your technical decisions", "features": ["Technology stack recommendations", "Code reviews", "Performance optimization", "Security assessment", "Scalability planning", "UAE payment gateway integration"]}}}, "packages": {"title": "Our Packages", "subtitle": "Tailored Solutions for Your Needs", "description": "Choose from our predefined packages or customize them to fit your project requirements.", "mvp": {"title": "MVP Development", "timeframe": "3-4 weeks", "description": "Launch your idea quickly with a Minimum Viable Product", "features": ["Core functionality implementation", "Basic UI design", "User authentication", "Data storage solution", "One platform deployment"]}, "prototype": {"title": "Prototype Package", "timeframe": "1-2 week", "description": "Test your concept with a functional prototype", "features": ["Interactive UI mockups", "Basic functionality", "User flow implementation", "Stakeholder presentation"]}, "architecture": {"title": "Project Architecture", "timeframe": "1-2 weeks", "description": "Solid foundation for your project's success", "features": ["Technical specifications", "System architecture design", "Database schema", "API documentation", "Development roadmap"]}, "consulting": {"title": "Technical Consulting", "timeframe": "Ongoing", "description": "Expert guidance for your technical decisions", "features": ["Technology stack recommendations", "Code reviews", "Performance optimization", "Security assessment", "Scalability planning"]}}, "solutionsPortfolio": {"title": "Our Solutions", "subtitle": "Showcasing Industry-Leading Digital Solutions", "description": "Explore our innovative digital solutions designed to solve real-world business problems and drive growth.", "clickInstruction": "Click card for details & fullscreen view", "imageCounter": "Image {current} of {total}", "keyFeatures": "Key Features", "problemsSolved": "Problems Solved", "viewDetails": "View Details", "screenshot": "Screenshot", "screenshots": "Screenshots", "categories": {"all": "All Categories", "aiAssistant": "AI Assistant", "foodDelivery": "Food Delivery", "hospitality": "Hospitality", "medical": "Medical", "lifestyle": "Lifestyle Apps", "automotive": "Automotive"}, "items": {"spotzAiAssistant": {"title": "Spotz AI Assistant", "description": "AI-powered assistant with context-aware capabilities.", "imageAlt": "Spotz AI assistant screenshot {index}"}, "foodDelivery": {"title": "Food Delivery", "description": "Complete platform for ordering and delivering food.", "imageAlt": "Food delivery app screenshot {index}"}, "hostIQ": {"title": "HostIQ - Hospitality SuperApp", "description": "All-in-one solution for hotels to manage operations, guest experience, and services.", "imageAlt": "HostIQ hospitality management platform screenshot {index}"}, "medicalApp": {"title": "Medical Management", "description": "Healthcare management platform for patients and providers.", "imageAlt": "Medical management application screenshot {index}"}, "lifestyleApp": {"title": "Lifestyle Planner", "description": "Personal wellness and lifestyle management application.", "imageAlt": "Lifestyle planning app screenshot {index}"}, "nearby": {"title": "Nearby", "description": "Location-based discovery app to find everything in your area.", "imageAlt": "Nearby app interface screenshot {index}"}, "toggCarControl": {"title": "Togg Car Control", "description": "Smart car control and management system for Togg vehicles.", "imageAlt": "Togg car control application screenshot {index}"}, "lifestylePlatform": {"title": "Lifestyle Platform", "description": "Modern lifestyle platform with engaging user experience.", "imageAlt": "Lifestyle platform interface screenshot {index}"}, "default": {"imageAlt": "Portfolio item screenshot {index}"}}, "solutions": {"aiAssistant": {"title": "AI Assistant Solutions", "description": "Intelligent virtual assistants enhancing user experiences and automating tasks.", "features": ["Natural Language Processing", "Contextual Understanding", "Multi-platform Support", "Voice Recognition"], "problemsSolved": ["Information Access", "Decision Support", "Customer Service Automation", "Accessibility Challenges"]}, "foodDelivery": {"title": "Food Delivery Platforms", "description": "End-to-end systems for restaurants and delivery services.", "features": ["Order Management", "Real-time Tracking", "Payment Processing", "Rating Systems"], "problemsSolved": ["Restaurant Discovery", "Delivery Logistics", "Order Fulfillment", "Digital Menu Management"]}, "hospitality": {"title": "Hospitality Management Solutions", "description": "Digital systems enhancing guest experiences and streamlining operations.", "features": ["Booking Management", "Guest Services", "Facility Management", "Staff Coordination"], "problemsSolved": ["Guest Experience Optimization", "Operational Efficiency", "Resource Management", "Service Delivery"]}, "medical": {"title": "Medical Applications", "description": "Comprehensive healthcare platforms for managing patient care and medical operations.", "features": ["Patient Records", "Appointment Scheduling", "Medical Analytics", "Health Monitoring"], "problemsSolved": ["Healthcare Access", "Medical Data Management", "Treatment Tracking", "Patient Engagement"]}, "lifestyle": {"title": "Lifestyle Applications", "description": "Apps that enhance daily life, wellness, and personal organization.", "features": ["Wellness Planning", "Activity Tracking", "Habit Formation", "Personal Organization"], "problemsSolved": ["Organization", "Wellness Management", "Personal Development", "Life Balancing"]}, "automotive": {"title": "Automotive Technology Solutions", "description": "Digital interfaces and control systems for modern automotive applications.", "features": ["Vehicle Management", "Navigation Systems", "Diagnostic Tools", "Driver Assistance"], "problemsSolved": ["Vehicle Control", "Navigation Challenges", "Maintenance Tracking", "Driver Experience"]}}}, "portfolio": {"title": "Our Expertise", "subtitle": "Sectors & Problem Solving", "description": "Explore our expertise across various sectors and the problems we solve for businesses.", "all": "All Sectors", "screenshot": "Screenshot", "screenshots": "Screenshots", "problemsWeSolve": "Problems We Solve", "noSectorsFound": "No sectors found for the selected filter.", "categories": {"aiAssistant": "AI Assistant", "foodDelivery": "Food Delivery", "hospitality": "Hospitality", "medical": "Medical", "lifestyle": "Lifestyle Apps", "automotive": "Automotive"}, "sectors": {"assistant": "Assistant Apps", "food": "Food Order & Delivery", "hospitality": "Hospitality", "lifestyle": "Lifestyle Apps", "social": "Social Media", "automotive": "Automotive", "medical": "Medical & Healthcare", "business": "Business Solutions"}, "sectorDescriptions": {"assistant": "AI-powered assistants that enhance productivity and provide personalized recommendations", "food": "Seamless food ordering and delivery platforms with real-time tracking", "hospitality": "Digital solutions for hotels and hospitality businesses to enhance guest experience", "lifestyle": "Applications that enhance daily life, wellness, and personal development", "social": "Platforms that connect people and communities through shared interests", "automotive": "Smart solutions for vehicle management, navigation, and driver assistance", "medical": "Digital health solutions that improve patient care and medical operations", "business": "Enterprise applications that streamline operations and boost productivity"}, "problems": {"assistant": {"1": "Information overload", "2": "Task management", "3": "Decision making support"}, "food": {"1": "Order management", "2": "Delivery logistics", "3": "Restaurant discovery"}, "hospitality": {"1": "Guest management", "2": "Service optimization", "3": "Booking systems"}, "lifestyle": {"1": "Health tracking", "2": "Habit formation", "3": "Personal organization"}, "social": {"1": "User engagement", "2": "Content discovery", "3": "Community building"}, "automotive": {"1": "Vehicle monitoring", "2": "Navigation optimization", "3": "Driver experience"}, "medical": {"1": "Patient management", "2": "Health monitoring", "3": "Medical record systems"}, "business": {"1": "Workflow optimization", "2": "Data management", "3": "Team collaboration"}}, "viewDetails": "View Details", "viewAllProjects": "View All Projects", "features": "Features", "featureItem1": "Intuitive user interface", "featureItem2": "Multi-platform support", "featureItem3": "Advanced data analytics", "featureItem4": "Real-time notifications", "technologies": "Technologies Used", "requestDemo": "Request a Demo"}, "clients": {"title": "Our Clients", "subtitle": "Companies We've Worked With", "description": "We've had the pleasure of working with a diverse range of clients across various industries.", "visitWebsite": "Visit Website"}, "testimonials": {"title": "Client Testimonials", "subtitle": "What Our Clients Say", "description": "Hear what our clients have to say about their experience working with us and the results we've delivered for their businesses.", "readMore": "Read more", "readLess": "Read less"}, "contact": {"title": "Contact Us", "subtitle": "Get in Touch", "description": "Reach out to us for any inquiries, project discussions, or to schedule a consultation. We're here to help bring your digital vision to life.", "name": "Name", "email": "Email", "phone": "Phone", "message": "Message", "send": "Send Message", "yourName": "Your Name", "yourEmail": "Your Email", "subject": "Subject", "howCanIHelp": "How can I help?", "yourMessageHere": "Your message here", "getInTouch": "Get In Touch", "sendMessage": "Send a Message", "schedule": "Schedule a Call", "freeConsultation": "Book a free 15-min consultation", "location": "Location", "submitButton": "Send Message", "sending": "Sending...", "messageSent": "Message Sent!", "errorTryAgain": "Error, please try again", "orSchedule": "Or schedule a meeting directly using the calendar link"}, "faq": {"title": "Frequently Asked Questions", "subtitle": "Everything you need to know about our mobile app development and MVP services", "description": "Find answers to common questions about our mobile app development and MVP services. Can't find what you're looking for? Contact us directly.", "showMore": "Show More Questions", "showLess": "Show Less", "items": [{"id": "app-development", "question": "What services do you offer for mobile app development?", "answer": "We provide comprehensive mobile app development services including native iOS and Android development, cross-platform solutions using Flutter and React Native, UI/UX design, backend integration, and ongoing maintenance and support. Our expertise spans various industries and we specialize in creating scalable, high-performance mobile applications that deliver exceptional user experiences."}, {"id": "mvp-development", "question": "How do you approach MVP (Minimum Viable Product) development?", "answer": "Our MVP development process focuses on creating a functional product with core features that solve your target audience's key problems. We start with thorough market research and user analysis, define essential features, develop a streamlined product, and gather user feedback for iterative improvements. This approach helps validate your business idea quickly and cost-effectively before investing in a full-scale solution."}, {"id": "development-timeline", "question": "How long does it take to develop a mobile app?", "answer": "The timeline for mobile app development varies based on complexity, features, and platforms. A basic MVP can be developed in 2-3 months, while more complex applications may take 4-6 months or longer. We work with you to establish realistic timelines and milestones, ensuring transparent communication throughout the development process."}, {"id": "technology-stack", "question": "What technology stack do you use for app development?", "answer": "We utilize modern, robust technology stacks tailored to each project's specific requirements. For native development, we use Swift/SwiftUI for iOS and Kotlin/Java for Android. For cross-platform solutions, we leverage Flutter and React Native. Our backend technologies include Node.js, Python, Firebase, and AWS services. We select the optimal stack based on your project needs, performance requirements, and long-term scalability goals."}, {"id": "app-cost", "question": "How much does it cost to develop a mobile app?", "answer": "Mobile app development costs vary widely depending on complexity, features, platforms, and design requirements. Basic MVPs typically start from €15,000-30,000, while more complex applications with advanced features can range from €30,000-100,000+. We provide detailed estimates based on your specific requirements and offer flexible engagement models to accommodate different budget constraints."}]}, "footer": {"copyright": "© 2025 Innovatio-Pro. All rights reserved.", "description": "Transforming businesses with innovative software solutions.", "quickLinks": "Quick Links", "footerContact": "Contact", "legal": "Legal", "newsletter": "Newsletter", "newsletterDesc": "Subscribe to our newsletter for the latest updates.", "emailPlaceholder": "Your email", "subscribe": "Subscribe", "builtWith": "Built with", "and": "and", "downloadCV": "My CV", "englishCV": "English", "germanCV": "German"}, "cookies": {"title": "<PERSON><PERSON>", "description": "We use cookies to enhance your browsing experience, serve personalized ads or content, and analyze our traffic. By clicking \"Accept All\", you consent to our use of cookies.", "acceptAll": "Accept All", "decline": "Decline", "customize": "Customize", "necessary": "Necessary Cookies", "necessaryDesc": "These cookies are essential for the website to function properly and cannot be disabled.", "analytics": "Analytics Cookies", "analyticsDesc": "These cookies help us understand how visitors interact with our website and help us improve our services.", "marketing": "Marketing Cookies", "marketingDesc": "These cookies are used to track visitors across websites to display relevant advertisements.", "functional": "Functional Cookies", "functionalDesc": "These cookies enable enhanced functionality and personalization on our website.", "save": "Save Preferences", "settings": "<PERSON><PERSON>", "close": "Close", "cookiePolicy": "<PERSON><PERSON>", "privacyPolicy": "Privacy Policy"}, "heroParallax": {"title": "The Ultimate Development Studio", "subtitle": "We build beautiful products with the latest technologies and frameworks. We are a team of passionate developers and designers that love to build amazing products.", "products": {"mobileApp": "Mobile App Development", "webDev": "Web Development", "uiux": "UI/UX Design", "ecommerce": "E-commerce Solutions", "ai": "AI Integration", "cloud": "Cloud Solutions", "devops": "DevOps", "dataAnalytics": "Data Analytics", "blockchain": "Blockchain Development", "arvr": "AR/VR Solutions", "customSoftware": "Custom Software", "mobileGame": "Mobile Game Development", "iot": "IoT Solutions", "api": "API Development", "cybersecurity": "Cybersecurity"}}, "featuresSection": {"features": [{"title": "Built for developers", "description": "Built for engineers, developers, dreamers, thinkers and doers.", "icon": "IconTerminal2"}, {"title": "Ease of use", "description": "It's as easy as using an Apple, and as expensive as buying one.", "icon": "IconEaseInOut"}, {"title": "Pricing like no other", "description": "Our prices are best in the market. No cap, no lock, no credit card required.", "icon": "Icon<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"title": "100% Uptime guarantee", "description": "We just cannot be taken down by anyone.", "icon": "IconCloud"}, {"title": "Multi-tenant Architecture", "description": "You can simply share passwords instead of buying new seats", "icon": "IconRouteAltLeft"}, {"title": "24/7 Customer Support", "description": "We are available a 100% of the time. Atleast our AI Agents are.", "icon": "IconHelp"}, {"title": "Money back guarantee", "description": "If you donot like EveryAI, we will convince you to like us.", "icon": "IconAdjustmentsBolt"}, {"title": "And everything else", "description": "I just ran out of copy ideas. Accept my sincere apologies", "icon": "IconHeart"}]}, "seo": {"meta": {"home": {"title": "Mobile App Development & Digital Solutions | Innovatio-Pro", "description": "Professional mobile app development with Flutter, AI integration, and digital solutions. MVP development, prototyping, and full-stack development by Innovatio-Pro.", "keywords": "mobile app development, Flutter development, MVP development, AI integration, digital solutions, prototyping, full-stack development, Innovatio-Pro"}, "templates": {"title": "Mobile App Solutions & AI Integration - Innovatio-Pro", "description": "Discover our mobile app solutions, AI integration, and digital platforms. Specializing in Flutter, MVP development, and innovative mobile technologies.", "keywords": "mobile app solutions, AI integration, Flutter apps, MVP development, digital platforms, mobile technologies, app development"}, "services": {"title": "Mobile App Development Services - Flutter, AI & Digital Solutions", "description": "Professional mobile app development services using Flutter, AI integration, and modern technologies. Specialized in MVP, prototyping, and full-stack mobile solutions.", "keywords": "mobile app services, Flutter development, MVP services, AI integration, mobile solutions, app development services, digital transformation"}, "about": {"title": "About Innovatio-Pro - Mobile App Development & AI Specialist", "description": "Learn about Innovatio-Pro, your specialist for mobile app development with Flutter, AI integration, and innovative digital solutions.", "keywords": "Innovatio-Pro, mobile app developer, Flutter specialist, AI integration, digital solutions, app development company"}, "contact": {"title": "Contact Us - Mobile App Development & Digital Solutions | Innovatio-Pro", "description": "Contact Innovatio-Pro for professional mobile app development, AI integration, and digital solutions. Free consultation available for your next project.", "keywords": "mobile app development contact, Flutter development consultation, AI integration services, digital solutions inquiry, Innovatio-Pro contact"}, "pricing": {"title": "Mobile App Development Pricing - MVP, Prototyping & AI Solutions", "description": "Transparent pricing for mobile app development, MVP development, prototyping, and AI integration. From rapid prototypes to full-stack mobile solutions.", "keywords": "mobile app development pricing, MVP development cost, Flutter app prices, AI integration rates, prototyping prices, app development quotes"}, "faq": {"title": "FAQ - Mobile App Development & Digital Solutions | Innovatio-Pro", "description": "Common questions about our mobile app development services, Flutter development, AI integration, and digital solutions. Support and answers from Innovatio-Pro.", "keywords": "mobile app development FAQ, Flutter development questions, AI integration help, digital solutions support, app development answers"}}, "openGraph": {"siteName": "Innovatio-Pro - Mobile App Development & Digital Solutions", "defaultImage": "/images/og-innovatio-pro-en.jpg", "defaultImageAlt": "Innovatio-Pro - Mobile App Development with Flutter and AI Integration"}, "jsonLd": {"organization": {"name": "Innovatio-Pro", "alternateName": "Innovatio-Pro Mobile Development", "description": "Specialist in mobile app development with Flutter, AI integration, and innovative digital solutions for modern businesses.", "url": "https://innovatio-pro.com", "telephone": "+49-175-9918357", "email": "<EMAIL>", "address": {"streetAddress": "Remote Office", "addressLocality": "Wyoming", "addressRegion": "Wyoming", "postalCode": "82001", "addressCountry": "US"}, "sameAs": ["https://github.com/innovatio-pro", "https://linkedin.com/company/innovatio-pro"]}}}, "blog": {"title": "Tech Insights", "description": "Explore the latest trends, insights, and innovations in mobile development. From Flutter best practices to AI integration strategies - stay ahead in the tech revolution.", "categoriesTitle": "Categories", "categoriesDescription": "Explore articles across different tech domains", "subscriptionTitle": "Stay Informed", "subscriptionDescription": "Get the latest Flutter insights, AI trends, and tech innovations delivered to your inbox.", "readMore": "Read More", "noArticles": "No articles found", "author": "Author", "readingTime": "Reading Time", "views": "Views", "tags": "Tags", "floatingButton": {"tooltip": "Tech Blog", "currentArticle": "Current Article", "viewAll": "View All Articles", "newPosts": "New Posts"}, "categories": {"all": "All Articles", "company": "About Company", "flutter": "Flutter Development", "mobile": "Mobile Trends", "ai": "AI Integration", "performance": "Performance", "caseStudies": "Case Studies", "trends": "Industry Trends"}}}