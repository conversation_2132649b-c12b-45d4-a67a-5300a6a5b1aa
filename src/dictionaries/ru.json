{"nav": {"home": "Главная", "about": "О нас", "solutions": "Решения", "services": "Услуги", "techStack": "Технологии", "portfolio": "Портфолио", "contact": "Контакты", "chatWithUs": "Чат с нами", "bookConsultation": "Записаться на консультацию", "apps": "Приложения", "pricing": "Цены", "prices": "Цены", "calculator": "Калькулятор цен", "clients": "Клиенты", "testimonials": "Отзывы", "blog": "Блог"}, "hero": {"title": "🚀 Ваше приложение – Быстро. Масштабируемо. Прибыльно.", "subtitle": "От идеи до App Store в рекордные сроки. Мы создаем MVP, AI-готовые приложения и полноценные продукты.", "tagline": "Создаем будущее вместе", "description": "От идеи до App Store: Ваше видение, наш код.", "typing": {"sequence1": "От идеи до App Store: Видение и код.", "sequence2": "Экспертная разработка приложений.", "sequence3": "Высокопроизводительные решения."}, "cta": {"primary": "Начать проект", "secondary": "Записаться на консультацию", "calculator": "Получить цену проекта сейчас", "startProject": "Начать проект", "freeConsultation": "Бесплатная консультация", "calculateCost": "Рассчитать стоимость"}, "metrics": {"development": {"label": "Быстрая разработка", "value": "40%", "description": "быстрее разработка", "howTitle": "Как:", "howExplanation": "Использование новейших IDE с поддержкой ИИ, автоматизированной генерацией кода и интеллектуальным автодополнением."}, "timeToMarket": {"label": "Быстрый выход на рынок", "value": "50%", "description": "быстрее выход на рынок", "howTitle": "Как:", "howExplanation": "Fast lane CI/CD конвейер, автоматизированное тестирование, проверка кода с помощью ИИ и оптимизированный процесс развертывания."}, "costSaving": {"label": "Экономия затрат", "value": "30%", "description": "снижение затрат", "howTitle": "Как:", "howExplanation": "Использование Flutter для кросс-платформенной разработки, оптимизированные облачные ресурсы и эффективные методы разработки."}}}, "about": {"title": "О нас", "subtitle": "Наше видение и миссия", "vision": "Видение", "visionDesc": "Формировать цифровое будущее, предоставляя инновационные технологические решения, которые позволяют бизнесу процветать в современном мире.", "mission": "Миссия", "missionDesc": "Мы создаем высококачественные цифровые решения с использованием передовых технологий, таких как Flutter, чтобы помочь бизнесу решать сложные проблемы и достигать своих целей.", "founderTitle": "Основатель", "founderDesc": "Как увлеченный разработчик мобильных приложений и веб-разработчик, я специализируюсь на использовании новейших технологий, таких как Flutter, для создания современных, эффективных решений.", "skills": "Навыки", "projects": "Проекты", "testimonials": "Отзывы", "experience": "Лет опыта", "clients": "Довольных клиентов", "transformBusiness": "Преобразование бизнеса с помощью технологий", "createSolutions": "Создание перспективных цифровых решений", "stayingAhead": "Лидерство в области технологий", "exceptionalUX": "Предоставление исключительного пользовательского опыта", "highPerformance": "Разработка высокопроизводительных приложений", "solvingChallenges": "Решение реальных бизнес-задач с помощью технологий", "flutterExpert": "Эксперт", "webDevAdvanced": "Продвинутый", "aiIntegration": "Интеграция", "projectsCompleted": "Завершенные проекты", "personalDedication": "Личный", "dedication": "Преданность", "qualityFocused": "Ориентированный на качество", "personalService": "Персональный сервис", "focusedApproach": "Целенаправленный подход", "dedicatedService": "Преданный сервис", "clientReviews": "Отзывы клиентов", "focusedService": "Целенаправленный сервис", "longTerm": "Долгосрочный", "partnerships": "Партнерства", "privacyFocused": "Ориентированный на конфиденциальность", "secureServices": "Безопасная разработка", "personalAttention": "Личное внимание", "dedicatedDeveloper": "Преданный разработчик", "securityFocused": "Ориентированный на безопасность", "privacyRespected": "Конфиденциальность соблюдена", "quality": "Качество", "averageDelivery": "Среднее время доставки", "metrics": {"yearsExperience": "8+", "projectsCompleted": "15+", "happyClients": "12+", "clientRating": "5,0/5", "deliveryTimeframe": "3-4 недели", "personalValue": "Личный", "qualityValue": "Качество", "longTermValue": "Долгосрочный"}}, "advantages": {"title": "НАШИ РЕШЕНИЯ", "subtitle": "Как мы помогаем вам создавать успешные мобильные приложения", "speed": "Быстрая разработка", "speedDesc": "Мы быстро предоставляем решения без ущерба для качества", "stability": "Надежные приложения", "stabilityDesc": "Наши приложения созданы для стабильной и высокой производительности", "cost": "Экономическая эффективность", "costDesc": "Оптимизированный процесс разработки экономит ваше время и деньги", "timeToMarket": "Быстрый выход на рынок", "timeToMarketDesc": "Запускайте свой продукт быстро и опережайте конкурентов", "development": "Полноценная разработка", "developmentTime": "4-12 недель, зависит от сложности", "developmentDesc": "Полная разработка мобильного приложения от концепции до развертывания, с полной интеграцией бэкенда и расширенными функциями.", "mvp": "Разработка MVP", "mvpTime": "2-4 недели, зависит от сложности", "mvpDesc": "Быстрый запуск с минимально жизнеспособным продуктом, включающим основной функционал для проверки вашей концепции и привлечения первых пользователей или инвесторов.", "prototype": "Быстрое прототипирование", "prototypeTime": "1-2 недели, зависит от сложности", "prototypeDesc": "Быстрое тестирование концепций с интерактивными прототипами перед полноценной разработкой, экономя время и ресурсы.", "qa": "Контроль качества", "qaTime": "Постоянно, масштабируется в зависимости от сложности проекта", "qaDesc": "Комплексное тестирование на различных устройствах и платформах для обеспечения безупречной работы вашего приложения с автоматизированными и ручными протоколами тестирования.", "consulting": "Техническое консультирование", "consultingTime": "По необходимости, зависит от сложности проекта", "consultingDesc": "Экспертные рекомендации по технологическому стеку, архитектурным решениям и стратегиям реализации для оптимизации вашего мобильного приложения.", "uiux": "UI/UX-дизайн", "uiuxTime": "2-3 нед<PERSON><PERSON><PERSON>, зависит от сложности", "uiuxDesc": "Ориентированный на пользователя дизайн, который сочетает эстетику с функциональностью, создавая интуитивно понятный и привлекательный мобильный опыт.", "maintenance": "Поддержка и сопровождение", "maintenanceTime": "Постоянно, масштабируется в зависимости от сложности проекта", "maintenanceDesc": "Долгосрочная поддержка с регулярными обновлениями, оптимизацией производительности и улучшением функций для поддержания конкурентоспособности вашего приложения.", "analytics": "Интеграция аналитики", "analyticsTime": "1-2 недели, зависит от сложности", "analyticsDesc": "Внедрение отслеживания данных для получения практических сведений о поведении пользователей, что позволяет принимать обоснованные решения для вашего приложения.", "training": "Обучение команды", "trainingTime": "1-2 недели, зависит от сложности", "trainingDesc": "Комплексное обучение вашей команды по поддержке и расширению вашего приложения после передачи.", "developmentEfficiency": "Эффективность разработки", "timeToMarketReduction": "Сокращение времени выхода на рынок", "conceptValidation": "Проверка концепции", "bugFreeRate": "Показатель отсутствия ошибок", "technicalImprovement": "Техническое улучшение", "userSatisfaction": "Удовлетворенность пользователей", "appUptime": "Бесперебойная работа приложения", "dataAccuracy": "Точность данных", "knowledgeRetention": "Сохранение знаний", "developmentInfo": {"title": "Сроки разработки", "simpleApp": {"title": "Простое приложение", "examples": "Примеры: списки дел, калькуляторы, простые информационные приложения без бэкенда.", "features": ["Несколько экранов (3-5)", "Отсутствие или минимальная интеграция с бэкендом", "Стандартные компоненты интерфейса", "Отсутствие сложных анимаций или функций"], "timeline": {"total": "Время разработки: 4-8 недель", "frontend": "Фронтенд: 2-4 недели", "backend": "Бэкенд (если требуется): 1-2 недели", "testing": "Тестирование и развертывание: 1-2 недели"}}, "mediumApp": {"title": "Среднее приложение", "examples": "Примеры: приложения электронной коммерции, социальные сети с базовыми функциями, приложения с регистрацией пользователей и интеграцией с базой данных.", "features": ["6-15 <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Интеграция с бэкендом (например, REST или GraphQL API)", "Регистрация и аутентификация пользователей", "База данных для пользователей и данных приложения", "Некоторые анимации и интерактивные элементы", "Push-уведомления"], "timeline": {"total": "Время разработки: 8-16 недель", "frontend": "Фронтенд: 4-6 недель", "backend": "Бэкенд: 3-5 недель", "testing": "Тестирование и развертывание: 2-3 недели"}}, "complexApp": {"title": "Сложное приложение", "examples": "Примеры: приложения типа Uber, Instagram или банковские приложения с расширенными функциями.", "features": ["15+ <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Высокоинтерактивный пользовательский интерфейс", "Функции реального времени (например, отслеживание в реальном времени, чат)", "Интеграция сторонних API (например, платежные шлюзы, API карт)", "Масштабируемый бэкенд с облачной интеграцией", "Функции безопасности (например, шифрование, двухфакторная аутентификация)", "Офлайн-функциональность"], "timeline": {"total": "Время разработки: 16-32 недели или дольше", "frontend": "Фронтенд: 6-10 недель", "backend": "Бэкенд: 6-12 недель", "testing": "Тестирование и развертывание: 4-6 недель"}}, "factors": {"title": "Факторы, влияющие на время разработки", "teamSize": "Размер команды: большая команда (например, отдельные разработчики для фронтенда, бэкенда и QA) может ускорить разработку. Один разработчик требует больше времени.", "technology": "Технологии: нативная разработка (например, Swift для iOS, Kotlin для Android) часто занимает больше времени, чем кроссплатформенные подходы, такие как Flutter или React Native. Кроссплатформенные фреймворки могут сократить время разработки на 30-40%.", "requirements": "Требования и изменения: частые изменения или неясные требования могут продлить время разработки.", "testing": "Тестирование и отладка: сложные приложения требуют больше времени для тестирования, особенно на нескольких платформах (iOS и Android).", "design": "Дизайн: простые дизайны требуют меньше времени, в то время как пользовательские, анимированные дизайны увеличивают время разработки."}, "summary": "Итого: Простое приложение: 4-8 недель. Среднее приложение: 8-16 недель. Сложное приложение: 16-32 недели или дольше.", "aiComparison": "С нашей разработкой на основе ИИ и Flutter мы можем сократить эти сроки на 40-60%, сохраняя при этом высокое качество и производительность."}}, "serviceSection": {"title": "Наши услуги", "subtitle": "Индивидуальные цифровые решения для вашего бизнеса", "description": "Мы предлагаем широкий спектр цифровых услуг, чтобы помочь бизнесу процветать в современной конкурентной среде. Наша экспертиза охватывает различные области для предоставления инновационных и эффективных решений.", "viewAll": "Обсудить ваш проект", "mobileApps": {"title": "Разработка мобильных приложений", "description": "Кроссплатформенные мобильные приложения с нативной производительностью на Flutter, обеспечивающие безупречный опыт на устройствах iOS и Android.", "benefits": ["На 40% быстрее выход на рынок по сравнению с нативной разработкой", "Единая кодовая база для платформ iOS и Android", "Нативная производительность и красивый пользовательский интерфейс"]}, "webDev": {"title": "Веб-разработка", "description": "Современные, адаптивные веб-сайты и веб-приложения, созданные с использованием передовых технологий, таких как Next.js, React и Node.js.", "benefits": ["SEO-оптимизированная производительность с серверным рендерингом", "Адаптивный дизайн, работающий на всех устройствах", "Масштабируемая архитектура для будущего роста"]}, "uiuxDesign": {"title": "UI/UX-дизайн", "description": "Интуитивный, ориентированный на пользователя дизайн, который восхищает клиентов и повышает вовлеченность благодаря продуманному пользовательскому опыту.", "benefits": ["Подход к дизайну, ориентированный на пользователя", "Интерфейсы, оптимизированные для конверсии", "Единый опыт бренда на всех платформах"]}, "consulting": {"title": "Техническое консультирование", "description": "Экспертные рекомендации по технологической стратегии и реализации, помогающие принимать обоснованные решения для ваших цифровых проектов.", "benefits": ["Рекомендации по технологическому стеку", "Планирование и анализ архитектуры", "Оптимизация производительности и безопасности"]}, "aiSolutions": {"title": "Интеграция ИИ", "description": "Внедрение возможностей искусственного интеллекта для улучшения ваших бизнес-приложений с помощью интеллектуальных функций и автоматизации.", "benefits": ["Персонализированный пользовательский опыт", "Автоматизированные рабочие процессы", "Аналитика данных и прогнозирование"]}, "prototype": {"title": "Быстрое прототипирование", "description": "Быстрое тестирование концепций с интерактивными прототипами перед полноценной разработкой, экономя время и ресурсы.", "benefits": ["Проверка идей с минимальными инвестициями", "Сбор отзывов пользователей на ранних этапах", "Доработка концепций перед полной разработкой"]}, "mvp": {"title": "Разработка MVP", "description": "Быстрый запуск минимально жизнеспособного продукта с основным функционалом для проверки вашей концепции и привлечения первых пользователей.", "benefits": ["Более быстрый выход на рынок с основными функциями", "Экономичный подход к проверке бизнес-идей", "Итеративная разработка на основе реальных отзывов пользователей"]}, "fullstack": {"title": "Полноценная разработка", "description": "Полная разработка приложений от концепции до развертывания с интегрированными решениями для фронтенда и бэкенда.", "benefits": ["Комплексная экспертиза разработки", "Безупречная интеграция между всеми компонентами системы", "Всестороннее тестирование и контроль качества"]}, "homepage": {"title": "Сайты и Лендинги", "description": "Профессиональные веб-сайты и лендинги, которые демонстрируют вашу работу и превращают посетителей в клиентов.", "timeframe": "2-3 недели", "benefits": ["Ди<PERSON><PERSON><PERSON><PERSON>, оптимизированный для конверсии", "SEO-дружественная архитектура", "Адаптивные макеты для мобильных устройств"]}, "landingpage": {"title": "Доступные Лендинги", "description": "Лендинги, соответствующие стандарту WCAG 2.0, с использованием новейших веб-технологий для максимальной доступности и производительности.", "timeframe": "2-4 недели", "benefits": ["Соответствие WCAG 2.0 AA", "Инклюзивный дизайн для всех пользователей", "Совместимость с программами чтения с экрана", "Высокие показатели производительности"]}, "qa": {"title": "Контроль Качества", "description": "Комплексное тестирование на различных устройствах и платформах с использованием автоматизированного и ручного тестирования.", "timeframe": "Постоянно", "benefits": ["Безошибочный пользовательский опыт", "Оптимизация производительности", "Кросс-платформенная совместимость"]}}, "services": {"title": "Наш технологический стек", "subtitle": "Комплексные цифровые решения", "description": "Мы используем передовые технологии в различных областях для создания надежных и масштабируемых решений для ваших бизнес-потребностей.", "frontend": "Frontend-разработка", "frontendDesc": "Создание отзывчивых и интерактивных пользовательских интерфейсов с использованием современных веб-технологий", "backend": "Backend-разработка", "backendDesc": "Создание надежных серверных решений и API для масштабируемых приложений", "mobile": "Мобильная разработка", "mobileDesc": "Разработка кроссплатформенных мобильных приложений с нативной производительностью", "ai": "ИИ и машинное обучение", "aiDesc": "Интеграция интеллектуальных функций и автоматизации в приложения", "mobileApps": "Разработка мобильных приложений", "mobileAppsDesc": "Кроссплатформенные приложения с Flutter для iOS и Android", "webDev": "Веб-разработка", "webDevDesc": "Современные, адаптивные веб-сайты и веб-приложения", "uiuxDesign": "UI/UX-дизайн", "uiuxDesignDesc": "Интуитивно понятный, ориентированный на пользователя дизайн, который восхищает клиентов", "consulting": "Техническое консультирование", "consultingDesc": "Экспертные советы по технологической стратегии и реализации", "aiSolutions": "Интеграция ИИ", "aiSolutionsDesc": "Внедрение возможностей ИИ для улучшения вашего бизнеса", "viewAll": "Просмотреть все услуги"}, "prices": {"title": "Наши цены", "subtitle": "Прозрачные цены для каждого этапа", "description": "Выберите один из наших готовых пакетов или настройте их в соответствии с требованиями вашего проекта. Объедините услуги и получите скидку 15%.", "caseStudyTitle": "Пример: На 40% быстрее выход на рынок", "caseStudyDescription": "Наш финтех-клиент из Вайоминга запустил свой MVP на 40% быстрее, чем в среднем по отрасли, что позволило ему получить дополнительное финансирование и ускорить рост.", "promotionTitle": "Объедините услуги и сэкономьте 15%", "promotionDescription": "Объедините любые две или более услуги и получите скидку 15% на общую стоимость вашего проекта.", "calculatorTitle": "Нужна индивидуальная смета?", "calculatorDescription": "Используйте наш интерактивный калькулятор цен, чтобы получить детальную оценку, адаптированную под ваши конкретные требования к проекту.", "calculatorButton": "Открыть калькулятор цен", "discussButton": "Обсудить ваш проект", "contactButton": "Свяжитесь с нами", "pricingDisclaimer": "* Цены могут варьироваться в зависимости от требований проекта и дополнительных услуг. Свяжитесь с нами для получения индивидуального предложения, адаптированного под ваши конкретные потребности.", "priceVariesInfo": "Цена может варьироваться в зависимости от сложности проекта, дополнительных требований и временных ограничений. Свяжитесь с нами для получения детальной сметы.", "packages": {"mvp": {"title": "Разработка MVP", "timeframe": "3-4 недели", "description": "Быстро запустите свою идею с минимально жизнеспособным продуктом", "features": ["Реализация основного функционала", "Базовый дизайн UI", "Аутентификация пользователей", "Решение для хранения данных", "Развертывание на одной платформе", "Соответствие мировым стандартам"]}, "prototype": {"title": "Быстрый прототип", "timeframe": "1-2 недели", "description": "Протестируйте свою концепцию с функциональным прототипом", "features": ["Интерактивные UI-макеты", "Базовая функциональность", "Реализация пользовательских сценариев", "Презентация для заинтересованных сторон", "Разработка на Flutter"]}, "landingpage": {"title": "Разработка лендинга", "timeframe": "2-4 недели", "description": "Лендинги, соответствующие стандарту WCAG 2.0, с использованием новейших веб-технологий", "features": ["Соответствие WCAG 2.0 AA", "Инклюзивный дизайн для всех пользователей", "Совместимость с программами чтения с экрана", "Высокие показатели производительности", "SEO-оптимизированная структура", "Адаптивный дизайн"]}, "architecture": {"title": "Архитектура проекта", "timeframe": "1-2 недели", "description": "Надежная основа для успеха вашего проекта", "features": ["Технические спецификации", "Проектирование системной архитектуры", "Схема базы данных", "Документация API", "Дорожная карта разработки"]}, "consulting": {"title": "Технический консалтинг", "timeframe": "Постоянно", "description": "Экспертное руководство для ваших технических решений", "features": ["Рекомендации по технологическому стеку", "Проверка кода", "Оптимизация производительности", "Оценка безопасности", "Планирование масштабируемости", "Интеграция платежных шлюзов ОАЭ"]}}}, "packages": {"title": "Наши пакеты", "subtitle": "Индивидуальные решения для ваших потребностей", "mvp": {"title": "Разработка MVP", "timeframe": "3-4 недели", "description": "Быстро запустите свою идею с минимально жизнеспособным продуктом", "features": ["Реализация основного функционала", "Базовый UI-дизайн", "Аутентификация пользователей", "Решение для хранения данных", "Развертывание на одной платформе"]}, "prototype": {"title": "Пакет прототипирования", "timeframe": "1-2 неделя", "description": "Протестируйте свою концепцию с функциональным прототипом", "features": ["Интерактивные UI-макеты", "Базовая функциональность", "Реализация пользовательского потока", "Презентация для заинтересованных сторон"]}, "architecture": {"title": "Архитектура проекта", "timeframe": "1-2 недели", "description": "Прочная основа для успеха вашего проекта", "features": ["Технические спецификации", "Диз<PERSON><PERSON>н системной архитектуры", "Схема базы данных", "Документация API", "Дорожная карта разработки"]}, "consulting": {"title": "Техническое консультирование", "timeframe": "Постоянно", "description": "Экспертное руководство для ваших технических решений", "features": ["Рекомендации по технологическому стеку", "Обзоры кода", "Оптимизация производительности", "Оценка безопасности", "Планирование масштабируемости"]}}, "solutionsPortfolio": {"title": "Наши решения", "subtitle": "Демонстрация передовых цифровых решений", "clickInstruction": "Нажмите на карточку для подробностей и полноэкранного просмотра", "imageCounter": "Изображение {current} из {total}", "keyFeatures": "Ключевые функции", "problemsSolved": "Решаемые проблемы", "viewDetails": "Подробнее", "screenshot": "Скриншот", "screenshots": "Скриншоты", "categories": {"all": "Все Категории", "aiAssistant": "ИИ-Aссистент", "foodDelivery": "Доставка Еды", "hospitality": "Гостиничный Бизнес", "medical": "Медицина", "lifestyle": "Лайфстайл Приложения", "automotive": "Автомобильная Отрасль"}, "items": {"spotzAiAssistant": {"title": "Spotz ИИ-ассистент", "description": "ИИ-ассистент для поиска ближайших мест, предоставления рекомендаций, навигации и экстренных служб.", "imageAlt": "Скриншот интерфейса мобильного приложения Spotz ИИ-ассистент {index}"}, "foodDelivery": {"title": "Доставка еды", "description": "Полное решение для поиска и доставки еды с интуитивным процессом заказа.", "imageAlt": "Скриншот приложения доставки еды {index}"}, "hostIQ": {"title": "HostIQ - СуперПриложение для гостиничного бизнеса", "description": "Комплексное решение для отелей по управлению операциями, опытом гостей и услугами.", "imageAlt": "Скриншот платформы управления гостиничным бизнесом HostIQ {index}"}, "businessManagement": {"title": "Управление локальным бизнесом", "description": "Маркетинговые инструменты, размещение событий, обмен историями и управление бронированием для локального бизнеса.", "imageAlt": "Скриншот приложения для управления бизнесом {index}"}, "lumeusApp": {"title": "Приложе<PERSON><PERSON><PERSON> Lumeus", "description": "Платформа для социальных сетей и связей с расширенными функциями.", "imageAlt": "Скриншот приложения для социальных сетей Lumeus {index}"}, "nearby": {"title": "Поблизости", "description": "Приложение для поиска на основе местоположения, чтобы найти всё в вашем районе.", "imageAlt": "Скриншот интерфейса приложения Поблизости {index}"}, "toggCarControl": {"title": "Управление автомобилем Togg", "description": "Интеллектуальная система управления и контроля автомобилей Togg.", "imageAlt": "Скриншот приложения управления автомобилем Togg {index}"}, "socialMediaPlatform": {"title": "Платформа социальных сетей", "description": "Современная платформа социальных сетей с привлекательным пользовательским опытом.", "imageAlt": "Скриншот интерфейса платформы социальных сетей {index}"}, "default": {"imageAlt": "Скриншот элемента портфолио {index}"}}, "solutions": {"aiAssistant": {"title": "Решения с ИИ-ассистентами", "description": "Интеллектуальные виртуальные помощники, улучшающие пользовательский опыт и автоматизирующие задачи.", "features": ["Обработка естественного языка", "Контекстное понимание", "Распознавание голоса", "Поддержка нескольких платформ", "Возможности интеграции"], "problemsSolved": ["Доступ к информации", "Поддержка принятия решений", "Автоматизация обслуживания клиентов", "Управление задачами"]}, "foodDelivery": {"title": "Платформы доставки еды", "description": "Комплексные системы для ресторанов и служб доставки.", "features": ["Управление заказами", "Отслеживание в реальном времени", "Обработка платежей", "Панель управления рестораном", "Оптимизация доставки"], "problemsSolved": ["Поиск ресторанов", "Логистика доставки", "Выполнение заказов", "Удержание клиентов"]}, "hospitality": {"title": "Решения для управления гостиничным бизнесом", "description": "Цифровые системы, улучшающие опыт гостей и оптимизирующие операции.", "features": ["Управление бронированием", "Услуги для гостей", "Координация персонала", "Контроль запасов", "Аналитическая панель"], "problemsSolved": ["Оптимизация опыта гостей", "Операционная эффективность", "Управление ресурсами", "Предоставление услуг"]}, "business": {"title": "Системы управления бизнесом", "description": "Комплексные платформы для управления операциями и ростом.", "features": ["Интеграция с CRM", "Управление запасами", "Отслеживание финансов", "Управление персоналом", "Инструменты отчетности"], "problemsSolved": ["Оптимизация процессов", "Управление данными", "Распределение ресурсов", "Бизнес-аналитика"]}, "social": {"title": "Платформы социальных сетей", "description": "Привлекательные решения для социальных сетей и сообществ.", "features": ["Профили пользователей", "Об<PERSON>ен контентом", "Система сообщений", "Система уведомлений", "Аналитические инструменты"], "problemsSolved": ["Создание цифровых сообществ", "Вовлечение пользователей", "Обнаружение контента", "Социальное взаимодействие"]}, "automotive": {"title": "Решения автомобильных технологий", "description": "Цифровые интерфейсы и системы управления для транспортных средств.", "features": ["Управление автомобилем", "Навигационные системы", "Диагностические инструменты", "Интеграция развлечений", "Подключение IoT"], "problemsSolved": ["Контроль транспортного средства", "Проблемы навигации", "Управление техническим обслуживанием", "Опыт водителя"]}}}, "portfolio": {"title": "Наша экспертиза", "subtitle": "Отрасли и решения проблем", "all": "Все отрасли", "screenshot": "Снимок экрана", "screenshots": "Снимки экрана", "problemsWeSolve": "Проблемы, которые мы решаем", "noSectorsFound": "Не найдено отраслей для выбранного фильтра.", "categories": {"aiAssistant": "ИИ-ассистент", "foodDelivery": "Доставка еды", "hospitality": "Гостиничный бизнес", "business": "Бизнес", "social": "Социальные сети", "automotive": "Автомобильная индустрия"}, "sectors": {"assistant": "Приложения-ассистенты", "food": "Заказ еды и доставка", "hospitality": "Гостиничный бизнес", "lifestyle": "Лайфстайл-приложения", "social": "Социальные сети", "automotive": "Автомобильная индустрия", "medical": "Медицина и здравоохранение", "business": "Бизнес-решения"}, "sectorDescriptions": {"assistant": "ИИ-помощники, которые повышают продуктивность и предоставляют персональные рекомендации", "food": "Платформы для заказа и доставки еды с отслеживанием в реальном времени", "hospitality": "Цифровые решения для отелей и гостиничного бизнеса для улучшения впечатлений гостей", "lifestyle": "Приложения, которые улучшают повседневную жизнь, благополучие и личностное развитие", "social": "Платформы, соединяющие людей и сообщества через общие интересы", "automotive": "Умные решения для управления автомобилем, навигации и помощи водителю", "medical": "Цифровые решения в области здравоохранения, улучшающие уход за пациентами и медицинские операции", "business": "Корпоративные приложения, оптимизирующие операции и повышающие производительность"}, "problems": {"assistant": {"1": "Информационная перегрузка", "2": "Управление задачами", "3": "Поддержка принятия решений"}, "food": {"1": "Управление заказами", "2": "Логистика доставки", "3": "Поиск ресторанов"}, "hospitality": {"1": "Управление гостями", "2": "Оптимизация сервиса", "3": "Системы бронирования"}, "lifestyle": {"1": "Отслеживание здоровья", "2": "Формирование привычек", "3": "Личная организация"}, "social": {"1": "Вовлеченность пользователей", "2": "Поиск контента", "3": "Построение сообществ"}, "automotive": {"1": "Мониторинг транспортных средств", "2": "Оптимизация навигации", "3": "Опыт водителя"}, "medical": {"1": "Управление пациентами", "2": "Монитор<PERSON>нг здоровья", "3": "Системы медицинских записей"}, "business": {"1": "Оптимизация рабочих процессов", "2": "Управление данными", "3": "Командное сотрудничество"}}, "viewDetails": "Посмотреть детали", "viewAllProjects": "Посмотреть все проекты"}, "clients": {"title": "Наши клиенты", "subtitle": "Компании, с которыми мы работали", "visitWebsite": "Посетить сайт"}, "testimonials": {"title": "Отзывы клиентов", "subtitle": "Что говорят наши клиенты", "readMore": "Читать далее", "readLess": "Свернуть"}, "contact": {"title": "Связаться с нами", "subtitle": "Свяжитесь с нами", "name": "Имя", "email": "Email", "phone": "Телефон", "message": "Сообщение", "send": "Отправить сообщение", "yourName": "Ваше имя", "yourEmail": "Ваш email", "subject": "Тема", "howCanIHelp": "Чем я могу помочь?", "yourMessageHere": "Ваше сообщение здесь", "getInTouch": "Связаться с нами", "sendMessage": "Отправить сообщение", "schedule": "Запланировать звонок", "freeConsultation": "Забронировать бесплатную 15-минутную консультацию", "location": "Местоположение", "submitButton": "Отправить сообщение", "sending": "Отправка...", "messageSent": "Сообщение отправлено!", "errorTryAgain": "Ошибка, попробуйте снова", "orSchedule": "Или запланируйте встречу напрямую, используя ссылку календаря"}, "footer": {"copyright": "© 2025 Innovatio. Все права защищены.", "description": "Мы разрабатываем передовые мобильные и веб-приложения, которые трансформируют бизнес с помощью инновационных технологий.", "quickLinks": "Быстрые ссылки", "footerContact": "Контакты", "legal": "Правовая информация", "newsletter": "Рассылка", "newsletterDesc": "Подпишитесь на нашу рассылку, чтобы получать обновления и информацию.", "emailPlaceholder": "Введите ваш email", "subscribe": "Подписаться", "builtWith": "Создано с помощью", "and": "и", "downloadCV": "Моё резюме", "englishCV": "Английский", "germanCV": "Немецкий"}, "cookies": {"title": "Согласие на использование cookie", "description": "Мы используем файлы cookie для улучшения вашего опыта просмотра, показа персонализированной рекламы или контента и анализа нашего трафика. Нажимая «Принять все», вы соглашаетесь с использованием нами файлов cookie.", "acceptAll": "Принять все", "decline": "Отклонить", "customize": "Настроить", "necessary": "Необходимые cookies", "necessaryDesc": "Эти файлы cookie необходимы для правильного функционирования веб-сайта и не могут быть отключены.", "analytics": "Аналитические cookies", "analyticsDesc": "Эти файлы cookie помогают нам понять, как посетители взаимодействуют с нашим веб-сайтом, и помогают улучшить наши услуги.", "marketing": "Маркетинговые cookies", "marketingDesc": "Эти файлы cookie используются для отслеживания посетителей на веб-сайтах для отображения релевантной рекламы.", "functional": "Функциональные cookies", "functionalDesc": "Эти файлы cookie обеспечивают расширенную функциональность и персонализацию на нашем веб-сайте.", "save": "Сохранить настройки", "settings": "Настройки cookie", "close": "Закрыть", "cookiePolicy": "Политика использования cookie", "privacyPolicy": "Политика конфиденциальности"}, "heroParallax": {"title": "Лучшая студия разработки", "subtitle": "Мы создаем прекрасные продукты с использованием новейших технологий и фреймворков. Мы - команда увлеченных разработчиков и дизайнеров, которые любят создавать удивительные продукты.", "products": {"mobileApp": "Разработка мобильных приложений", "webDev": "Веб-разработка", "uiux": "UI/UX дизайн", "ecommerce": "Решения для электронной коммерции", "ai": "Интеграция ИИ", "cloud": "Облачные решения", "devops": "DevOps", "dataAnalytics": "Анализ данных", "blockchain": "Разработка блокчейн", "arvr": "AR/VR решения", "customSoftware": "Индивидуальное ПО", "mobileGame": "Разработка мобильных игр", "iot": "IoT решения", "api": "Разработка API", "cybersecurity": "Кибербезопасность"}}, "featuresSection": {"features": [{"title": "Создано для разработчиков", "description": "Создано для инженеров, разра<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, мечтателе<PERSON>, мыслителей и деятелей.", "icon": "IconTerminal2"}, {"title": "Простота использования", "description": "Это так же просто, как использовать Apple, и так же дорого, как купить его.", "icon": "IconEaseInOut"}, {"title": "Ценообразование, как ни у кого другого", "description": "Наши цены лучшие на рынке. Без ограничений, без блокировки, кредитная карта не требуется.", "icon": "Icon<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"title": "Гарантия 100% времени безотказной работы", "description": "Нас просто невозможно вывести из строя.", "icon": "IconCloud"}, {"title": "Мультитенантная архитектура", "description": "Вы можете просто делиться паролями вместо покупки новых мест.", "icon": "IconRouteAltLeft"}, {"title": "Круглосуточная поддержка клиентов", "description": "Мы доступны 100% времени. По крайней мере, наши ИИ-агенты.", "icon": "IconHelp"}, {"title": "Гарантия возврата денег", "description": "Если вам не понравится EveryAI, мы убедим вас полюбить нас.", "icon": "IconAdjustmentsBolt"}, {"title": "И все остальное", "description": "У меня просто закончились идеи для текста. Примите мои искренние извинения.", "icon": "IconHeart"}]}, "seo": {"meta": {"home": {"title": "Разработка Мобильных Приложений и Цифровые Решения | Innovatio-Pro", "description": "Профессиональная разработка мобильных приложений на Flutter, интеграция ИИ и цифровые решения. MVP-разработка, прототипирование и полная разработка от Innovatio-Pro.", "keywords": "разработка мобильных приложений, Flutter разработка, MVP разработка, интеграция ИИ, цифровые решения, прототипирование, полная разработка, Innovatio-Pro"}, "templates": {"title": "Решения для Мобильных Приложений и Интеграция ИИ - Innovatio-Pro", "description": "Откройте для себя наши решения для мобильных приложений, интеграцию ИИ и цифровые платформы. Специализируемся на Flutter, MVP-разработке и инновационных мобильных технологиях.", "keywords": "решения мобильных приложений, интеграция ИИ, Flutter приложения, MVP разработка, цифровые платформы, мобильные технологии, разработка приложений"}, "services": {"title": "Услуги Разработки Мобильных Приложений - Flutter, ИИ и Цифровые Решения", "description": "Профессиональные услуги разработки мобильных приложений с использованием Flutter, интеграции ИИ и современных технологий. Специализируемся на MVP, прототипировании и полных мобильных решениях.", "keywords": "услуги мобильных приложений, Flutter разработка, MVP услуги, интеграция ИИ, мобильные решения, услуги разработки приложений, цифровая трансформация"}, "about": {"title": "О Innovatio-Pro - Разработка Мобильных Приложений и Специалист по ИИ", "description": "Узнайте об Innovatio-Pro, вашем специалисте по разработке мобильных приложений на Flutter, интеграции ИИ и инновационных цифровых решений.", "keywords": "Innovatio-Pro, разработчик мобильных приложений, Flutter специалист, интеграция ИИ, цифровые решения, компания разработки приложений"}, "contact": {"title": "Контакты - Разработка Мобильных Приложений и Цифровые Решения | Innovatio-Pro", "description": "Свяжитесь с Innovatio-Pro для профессиональной разработки мобильных приложений, интеграции ИИ и цифровых решений. Доступна бесплатная консультация для вашего следующего проекта.", "keywords": "контакты разработки мобильных приложений, консультация Flutter разработки, услуги интеграции ИИ, запрос цифровых решений, контакты Innovatio-Pro"}, "pricing": {"title": "Цены на Разработку Мобильных Приложений - MVP, Прототипирование и ИИ Решения", "description": "Прозрачные цены на разработку мобильных приложений, MVP-разработку, прототипирование и интеграцию ИИ. От быстрых прототипов до полных мобильных решений.", "keywords": "цены разработки мобильных приложений, стоимость MVP разработки, цены Flutter приложений, тарифы интеграции ИИ, цены прототипирования, предложения разработки приложений"}, "faq": {"title": "FAQ - Разработка Мобильных Приложений и Цифровые Решения | Innovatio-Pro", "description": "Часто задаваемые вопросы о наших услугах разработки мобильных приложений, Flutter разработке, интеграции ИИ и цифровых решениях. Поддержка и ответы от Innovatio-Pro.", "keywords": "FAQ разработки мобильных приложений, вопросы Flutter разработки, помощь интеграции ИИ, поддержка цифровых решений, ответы разработки приложений"}}, "openGraph": {"siteName": "Innovatio-Pro - Разработка Мобильных Приложений и Цифровые Решения", "defaultImage": "/images/og-innovatio-pro-ru.jpg", "defaultImageAlt": "Innovatio-Pro - Разработка Мобильных Приложений на Flutter с Интеграцией ИИ"}, "jsonLd": {"organization": {"name": "Innovatio-Pro", "alternateName": "Innovatio-Pro Мобильная Разработка", "description": "Специалист по разработке мобильных приложений на Flutter, интеграции ИИ и инновационных цифровых решений для современного бизнеса.", "url": "https://innovatio-pro.com", "telephone": "+49-175-9918357", "email": "<EMAIL>", "address": {"streetAddress": "Remote Office", "addressLocality": "Wyoming", "addressRegion": "Wyoming", "postalCode": "82001", "addressCountry": "US"}, "sameAs": ["https://github.com/innovatio-pro", "https://linkedin.com/company/innovatio-pro"]}}}, "blog": {"title": "Технические Инсайты", "description": "Изучайте последние тренды, инсайты и инновации в мобильной разработке. От лучших практик Flutter до стратегий интеграции ИИ - оставайтесь впереди в технологической революции.", "categoriesTitle": "Категории", "categoriesDescription": "Изучайте статьи по различным технологическим областям", "subscriptionTitle": "Оставайтесь в курсе", "subscriptionDescription": "Получайте последние инсайты Flutter, тренды ИИ и технологические инновации на ваш почтовый ящик.", "readMore": "Читать далее", "noArticles": "Статьи не найдены", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "readingTime": "Время чтения", "views": "Просмотры", "tags": "Теги", "floatingButton": {"tooltip": "Технический Блог", "currentArticle": "Текущая статья", "viewAll": "Посмотреть все статьи", "newPosts": "Новые посты"}, "categories": {"all": "Все статьи", "company": "О компании", "flutter": "Flutter разработка", "mobile": "Мобильные тренды", "ai": "Интеграция ИИ", "performance": "Производительность", "caseStudies": "Кейсы", "trends": "Отраслевые тренды"}}}