// Types für das Proposal & Contract System - Innovatio-Pro
export interface CustomerProposal {
    // Basic Information
    clientId: string;
    name: string;
    email: string;
    companyName?: string;
    phone?: string;

    // URLs and Security
    proposalUrl: string;
    contractUrl: string;
    urlPassword: string;

    // Service Information
    interestedService: string; // Maps to "Interested Service" in Notion
    servicePrice: number; // Maps to "Service Price" in Notion
    estimatedBudget?: number; // Maps to "Estimated Budget" in Notion
    projectTimeline?: string; // Maps to "Project Timeline" in Notion
    heardAbout?: string; // Maps to "Heard About" in Notion
    description?: string; // Maps to "Description" in Notion

    // Status and Priority
    status: 'New Lead' | 'On Hold' | 'Qualified' | 'Won' | 'Lost'; // Matches Notion select options
    priority: 'Low' | 'Medium' | 'High' | 'Urgent'; // Matches Notion select options

    // Timestamps
    createdDate: string; // Maps to "Created Date" in Notion
    signatureDate?: string; // Maps to "Signature Date" in Notion

    // Notes and Comments
    notes?: string; // Maps to "Notes" in Notion
    internalComments?: string; // Maps to "Internal Comments" in Notion

    // Signature Data (split into parts due to Notion limits)
    signature1?: string; // Maps to "Signature 1" in Notion
    signature2?: string; // Maps to "Signature 2" in Notion  
    signature3?: string; // Maps to "Signature 3" in Notion

    // Additional fields for internal use
    selectedAddOns?: string[];
    totalPrice?: number;
    locale?: string;
}

// Service Packages für innovatio-pro
export interface ServicePackage {
    id: string;
    name: string;
    description: string;
    basePrice?: number;
    price?: number; // Keep for backward compatibility
    originalPrice?: number;
    timeframe: string;
    category: string;
    features: string[];
    isHourly?: boolean;
}

// Add-ons für innovatio-pro Services
export interface AddOn {
    id: string;
    name: string;
    description: string;
    price: number;
    category: 'design' | 'technical' | 'support' | 'integration';
    compatibleServices: string[];
}

// Proposal Creation Request
export interface CreateProposalRequest {
    clientId: string;
    name: string;
    email: string;
    companyName?: string;
    phone?: string;
    interestedService: string;
    estimatedBudget?: number;
    projectTimeline?: string;
    heardAbout?: string;
    description?: string;
    selectedAddOns?: string[];
    notes?: string;
}

// Proposal Acceptance Request
export interface AcceptProposalRequest {
    signature: string;           // Base64 encoded
    addOns: string[];
    legalConsents: {
        agb: boolean;
        dsgvo: boolean;
        widerruf: boolean;
    };
    finalNotes?: string;
}

// Admin Auth Request
export interface AdminAuthRequest {
    password: string;
}

// Password Verification Request
export interface VerifyPasswordRequest {
    clientId: string;
    password: string;
}

// Contract Completion Request
export interface CompleteContractRequest {
    clientId: string;
    signatureData: string;      // Base64 encoded
    agreements: {
        agb: boolean;
        dsgvo: boolean;
        widerruf: boolean;
    };
    finalComments?: string;
}

// Lead Update Request
export interface UpdateLeadRequest {
    status?: string;
    addOns?: string[];
    comments?: string;
    contractUrl?: string;
    signature?: string;
}

// API Response Types
export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    error?: string;
    message?: string;
}

// Enhanced Contact Form Data (für das angepasste Contact Form)
export interface EnhancedContactFormData {
    name: string;
    email: string;
    companyName?: string;
    phone?: string;
    selectedService?: string;
    estimatedBudget?: string;
    projectTimeline?: string;
    heardAbout?: string;
    message: string;
}

// Request interfaces for API endpoints
export interface UpdateProposalRequest {
    clientId: string;
    companyName?: string;
    phone?: string;
    interestedService?: string;
    selectedAddOns?: string[];
    estimatedBudget?: number;
    projectTimeline?: string;
    heardAbout?: string;
    description?: string;
    notes?: string;
    internalComments?: string;
    status?: CustomerProposal['status'];
    priority?: CustomerProposal['priority'];
}

export interface ContractSubmissionRequest {
    clientId: string;
    signature: string;
    termsAccepted: boolean;
    privacyAccepted: boolean;
    gdprAccepted: boolean;
}

export interface ProposalResponse {
    success: boolean;
    data?: CustomerProposal;
    error?: string;
} 