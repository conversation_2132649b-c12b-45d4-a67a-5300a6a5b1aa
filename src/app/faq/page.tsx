'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Section } from '@/components/ui/Section'
import { useI18n } from '@/providers/I18nProvider'
import Link from 'next/link'

// Comprehensive FAQ data with SEO-optimized content focused on mobile app development and MVP
const faqData = [
  {
    id: "app-development",
    question: "Was bieten Sie für mobile App-Entwicklung an?",
    answer:
      "Ich entwickle persönlich Ihre Flutter-App für iOS und Android. Keine Agentur, kein Team - nur ich. Mit 8 Jahren Erfahrung und 15+ erfolgreichen Projekten. Sie bekommen: komplette App-Entwicklung, UI/UX-Design, Backend-Integration, App Store Upload und Support. Alles aus einer Hand.",
  },
  {
    id: "mvp-development",
    question: "Wie läuft die MVP-Entwicklung ab?",
    answer:
      "Einfach: Wir sprechen 30 Minuten über Ihre Idee. Ich erkläre was technisch möglich ist und was nicht. Sie bekommen ein Festpreis-Angebot. Bei Zusage starte ich sofort. Regelmäßige Updates per WhatsApp. Pünktliche Lieferung ins App Store. So einfach.",
  },
  {
    id: "development-timeline",
    question: "Wie lange dauert die App-Entwicklung?",
    answer:
      "Je nach Komplexität: MVP deutlich schneller als der Markt, vollständige Apps effizienter als Agenturen. Warum? Ich arbeite mit bewährten Flutter-Templates, verwende Firebase als Backend und kenne jeden Stolperstein. Andere brauchen Monate für Meetings - ich programmiere in der Zeit.",
  },
  {
    id: "technology-stack",
    question: "What technology stack do you use for app development?",
    answer:
      "We utilize modern, robust technology stacks tailored to each project's specific requirements. For native development, we use Swift/SwiftUI for iOS and Kotlin/Java for Android. For cross-platform solutions, we leverage Flutter and React Native. Our backend technologies include Node.js, Python, Firebase, and AWS services. We select the optimal stack based on your project needs, performance requirements, and long-term scalability goals.",
  },
  {
    id: "app-cost",
    question: "How much does it cost to develop a mobile app?",
    answer:
      "Mobile app development costs vary widely depending on complexity, features, platforms, and design requirements. Basic MVPs typically start from €15,000-30,000, while more complex applications with advanced features can range from €30,000-100,000+. We provide detailed estimates based on your specific requirements and offer flexible engagement models to accommodate different budget constraints.",
  },
  {
    id: "app-maintenance",
    question: "Do you provide app maintenance and support after launch?",
    answer:
      "Yes, we offer comprehensive post-launch maintenance and support services to ensure your mobile application remains up-to-date, secure, and optimized. Our maintenance packages include bug fixes, performance optimization, security updates, feature enhancements, and technical support. We recommend ongoing maintenance to adapt to platform changes, user feedback, and evolving business requirements.",
  },
  {
    id: "app-design",
    question: "How do you approach mobile app design?",
    answer:
      "Our mobile app design process is user-centered and focuses on creating intuitive, engaging experiences. We begin with user research and persona development, create wireframes and prototypes, design visually appealing interfaces with consistent branding, and conduct usability testing. We emphasize accessibility, performance optimization, and platform-specific design guidelines to ensure your app delivers an exceptional user experience across all devices.",
  },
  {
    id: "cross-platform",
    question: "What are the benefits of cross-platform app development?",
    answer:
      "Cross-platform development offers several advantages: cost efficiency (single codebase for multiple platforms), faster time-to-market, consistent user experience across platforms, easier maintenance, and broader market reach. Using frameworks like Flutter and React Native, we create high-performance cross-platform applications that maintain near-native performance while significantly reducing development time and costs.",
  },
  {
    id: "native-vs-cross",
    question: "Should I choose native or cross-platform app development?",
    answer:
      "The choice between native and cross-platform development depends on your specific requirements. Native development is ideal for applications requiring maximum performance, platform-specific features, or complex functionality. Cross-platform is better suited for MVPs, applications with moderate complexity, or when budget and time constraints are significant factors. We assess your project needs and recommend the most suitable approach.",
  },
  {
    id: "app-testing",
    question: "How do you ensure the quality of mobile applications?",
    answer:
      "We implement a comprehensive quality assurance process that includes functional testing, usability testing, performance testing, security testing, and compatibility testing across different devices and OS versions. We use both automated and manual testing approaches, conduct code reviews, and implement continuous integration/continuous deployment (CI/CD) pipelines to ensure consistent quality throughout the development lifecycle.",
  },
  {
    id: "app-marketing",
    question:
      "Do you provide assistance with app store optimization and marketing?",
    answer:
      "Yes, we offer app store optimization (ASO) and marketing support to maximize your app's visibility and user acquisition. Our services include keyword optimization, compelling app descriptions, screenshot and preview video creation, ratings and reviews management, and implementation of app analytics. We can also assist with broader marketing strategies including social media campaigns, content marketing, and paid advertising.",
  },
  {
    id: "development-process",
    question: "Wie läuft der Entwicklungsprozess ab?",
    answer:
      "Kostenlose Beratung → Festpreis-Angebot → Anzahlung → Los geht's. Regelmäßig bekommen Sie Screenshots vom Fortschritt. Fragen können Sie jederzeit per WhatsApp stellen. Zum vereinbarten Termin laden wir gemeinsam Ihre App in den Store hoch. Fertig.",
  },
  {
    id: "cost-breakdown",
    question: "Was kostet eine App wirklich?",
    answer:
      "MVP: 8.500€ festpreis. Vollständige App: 15.000-25.000€ je nach Features. Warum so günstig? Ich arbeite allein (keine Agentur-Overhead), verwende bewährte Technologien und mache seit 8 Jahren nichts anderes. Andere Apps kosten 50.000€+ weil sie ineffizient arbeiten.",
  },
  {
    id: "why-flutter",
    question: "Warum Flutter und nicht native Apps?",
    answer:
      "Flutter = eine App für iOS und Android. Spart 50% Zeit und Kosten. Google nutzt Flutter für ihre eigenen Apps. Performance ist identisch zu nativen Apps. Updates gehen schneller. Einziger Nachteil: weniger Entwickler können es - aber ich kann's seit 5 Jahren.",
  },
  {
    id: "guarantee",
    question: "Was ist wenn die App nicht der Vereinbarung entspricht?",
    answer:
      "100% Geld zurück. Keine Diskussion. Ist in 8 Jahren noch nie passiert, aber ich versichere es schriftlich. Warum? Weil ich nur realistische Zusagen mache und mit bewährten Prozessen arbeite. Lieber ehrlich als später enttäuscht.",
  },
];

// FAQ categories for better organization
const categories = [
  {
    id: "general",
    name: "Allgemeine Fragen",
    faqs: ["app-development", "development-process", "cost-breakdown"],
  },
  {
    id: "mvp",
    name: "MVP Entwicklung",
    faqs: ["mvp-development", "development-timeline", "guarantee"],
  },
  {
    id: "technical",
    name: "Technische Details",
    faqs: [
      "why-flutter",
      "technology-stack",
      "native-vs-cross",
      "cross-platform",
      "app-testing",
    ],
  },
  { id: "design", name: "Design & UX", faqs: ["app-design"] },
  {
    id: "post-launch",
    name: "Nach dem Launch",
    faqs: ["app-maintenance", "app-marketing"],
  },
];

export default function FAQPage() {
  const { dir } = useI18n();
  const isRtl = dir === "rtl";
  const [activeCategory, setActiveCategory] = useState('general');
  
  // Get FAQs for the active category
  const activeFaqs = categories.find(cat => cat.id === activeCategory)?.faqs || [];
  const filteredFaqs = faqData.filter(faq => activeFaqs.includes(faq.id));
  
  return (
    <main className={`min-h-screen ${isRtl ? "rtl" : ""}`}>
      <Section
        id="faq"
        title="Frequently Asked Questions"
        subtitle="Everything you need to know about our mobile app development and MVP services"
        titleClassName="text-gray-900 dark:text-white"
        subtitleClassName="text-primary dark:text-blue-400 text-center mb-10"
        className={`bg-gray-50 dark:bg-gray-900 ${isRtl ? "rtl-section" : ""} pt-32`}
      >
        {/* SEO-optimized introduction */}
        <div className="max-w-3xl mx-auto mb-12 text-center">
          <p className="text-gray-700 dark:text-gray-300 mb-6">
            Find comprehensive answers to common questions about our mobile app development and MVP services. 
            We specialize in creating high-quality, scalable applications for businesses of all sizes.
          </p>
          <p className="text-gray-700 dark:text-gray-300">
            Can't find what you're looking for? <Link href="/contact" className="text-blue-600 dark:text-blue-400 hover:underline font-medium">Contact us</Link> directly for personalized assistance.
          </p>
        </div>
        
        {/* Category navigation */}
        <div className="flex flex-wrap justify-center gap-3 mb-12">
          {categories.map(category => (
            <button
              key={category.id}
              onClick={() => setActiveCategory(category.id)}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
                activeCategory === category.id
                  ? 'bg-blue-600 text-white shadow-md'
                  : 'bg-gray-200 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-700'
              }`}
            >
              {category.name}
            </button>
          ))}
        </div>
        
        {/* FAQ Accordion */}
        <div className="max-w-3xl mx-auto">
          <AnimatePresence mode="wait">
            <motion.div
              key={activeCategory}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              {filteredFaqs.map((faq, index) => (
                <FaqItem key={faq.id} faq={faq} index={index} />
              ))}
            </motion.div>
          </AnimatePresence>
        </div>
        
        {/* CTA Section */}
        <div className="mt-16 text-center">
          <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Ready to start your project?</h3>
          <p className="text-gray-700 dark:text-gray-300 max-w-2xl mx-auto mb-8">
            Let's discuss how we can help you develop a successful mobile application or MVP that meets your business goals.
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <Link 
              href="/contact" 
              className="px-6 py-3 bg-gradient-to-r from-blue-600 to-violet-600 text-white font-medium rounded-lg shadow-md hover:shadow-lg transition-all duration-300"
            >
              Contact Us
            </Link>
            <a 
              href="https://calendly.com/v-hermann-it" 
              target="_blank"
              rel="noopener noreferrer"
              className="px-6 py-3 bg-white dark:bg-gray-800 text-gray-900 dark:text-white border border-gray-300 dark:border-gray-700 font-medium rounded-lg shadow-sm hover:shadow-md transition-all duration-300"
            >
              Schedule a Call
            </a>
          </div>
        </div>
      </Section>
    </main>
  );
}

// FAQ Item Component with animation
const FaqItem = ({ faq, index }: { faq: { id: string; question: string; answer: string }; index: number }) => {
  const [isOpen, setIsOpen] = useState(false);
  
  return (
    <motion.div 
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      className="mb-4"
    >
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-full flex justify-between items-center p-5 bg-white dark:bg-gray-800 rounded-lg shadow-sm hover:shadow-md transition-all duration-300 text-left"
        aria-expanded={isOpen}
      >
        <h4 className="text-lg font-semibold text-gray-900 dark:text-white pr-8">{faq.question}</h4>
        <motion.div
          animate={{ rotate: isOpen ? 180 : 0 }}
          transition={{ duration: 0.3 }}
          className="flex-shrink-0 text-gray-500 dark:text-gray-400"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </motion.div>
      </button>
      
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden"
          >
            <div className="p-5 bg-gray-50 dark:bg-gray-700/50 rounded-b-lg border-t border-gray-100 dark:border-gray-700">
              <p className="text-gray-700 dark:text-gray-300">{faq.answer}</p>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};
