// Enhanced Contact Form API Route - Innovatio-Pro
import { NextRequest, NextResponse } from 'next/server';
import { createLeadFromContact } from '@/lib/notion';
import { EnhancedContactFormData } from '@/types/proposal';

export async function POST(request: NextRequest) {
    try {
        const formData: EnhancedContactFormData = await request.json();

        // Basic validation
        if (!formData.name || !formData.email || !formData.message) {
            return NextResponse.json(
                {
                    success: false,
                    error: 'Name, email, and message are required'
                },
                { status: 400 }
            );
        }

        // Email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(formData.email)) {
            return NextResponse.json(
                {
                    success: false,
                    error: 'Please provide a valid email address'
                },
                { status: 400 }
            );
        }

        // Create lead in Notion CRM
        const lead = await createLeadFromContact({
            name: formData.name,
            email: formData.email,
            phone: formData.phone,
            company: formData.companyName,
            message: formData.message,
            selectedService: formData.selectedService,
            estimatedBudget: formData.estimatedBudget,
            projectTimeline: formData.projectTimeline,
            additionalServices: [], // Not currently collected in the form
            source: formData.heardAbout,
        });

        // Send notification email (optional - you can implement this later)
        // await sendNotificationEmail(formData, lead);

        return NextResponse.json({
            success: true,
            message: 'Thank you for your message! We will get back to you soon.',
            data: {
                leadId: lead.clientId,
                proposalUrl: lead.proposalUrl,
                // Don't expose sensitive data like password
            }
        });

    } catch (error) {
        console.error('Enhanced contact form error:', error);

        return NextResponse.json(
            {
                success: false,
                error: 'Failed to submit form. Please try again later.'
            },
            { status: 500 }
        );
    }
}

// Handle OPTIONS request for CORS
export async function OPTIONS() {
    return NextResponse.json({}, { status: 200 });
} 