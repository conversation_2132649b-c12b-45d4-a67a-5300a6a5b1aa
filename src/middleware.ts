import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { match as matchLocale } from '@formatjs/intl-localematcher';
import Negotiator from 'negotiator';

// List of all locales
export const locales = ['en', 'de', 'ru', 'tr', 'ar'];
export const defaultLocale = 'en';

// Country to language mapping
const countryLanguageMap: Record<string, string> = {
  // German-speaking countries
  DE: "de", // Germany
  AT: "de", // Austria
  CH: "de", // Switzerland

  // Russian-speaking countries
  RU: "ru", // Russia
  BY: "ru", // Belarus
  KZ: "ru", // Kazakhstan

  // Turkish-speaking countries
  TR: "tr", // Turkey
  CY: "tr", // Cyprus (Northern)

  // Arabic-speaking countries
  AE: "ar", // UAE - Dubai
  SA: "ar", // Saudi Arabia
  EG: "ar", // Egypt
  JO: "ar", // Jordan
  QA: "ar", // Qatar
  BH: "ar", // Bahrain
  KW: "ar", // Kuwait
  OM: "ar", // Oman
  IQ: "ar", // Iraq

  // All others default to English
};

// Get user's country from request headers
function getUserCountry(request: NextRequest): string | null {
  // Check for testing override using URL parameters
  const url = new URL(request.url);
  const countryParam = url.searchParams.get("country");
  if (countryParam) {
    console.log("Country forced via URL parameter:", countryParam);
    return countryParam.toUpperCase();
  }

  // Get hostname for domain-based detection
  const host = request.headers.get("host") || "";
  console.log("Host for country detection:", host);

  // PRODUCTION URL DETECTION - Force German for innovatio-pro.com
  if (host.includes("innovatio-pro.com")) {
    console.log(
      "Production domain detected, returning DE for innovatio-pro.com"
    );
    return "DE";
  }

  // Try to get country from Cloudflare or Vercel headers
  const cfCountry = request.headers.get("cf-ipcountry");
  if (cfCountry) {
    console.log("Country detected from CF header:", cfCountry);
    return cfCountry;
  }

  const xVercelIpCountry = request.headers.get("x-vercel-ip-country");
  if (xVercelIpCountry) {
    console.log("Country detected from Vercel header:", xVercelIpCountry);
    return xVercelIpCountry;
  }

  // Domain-specific detection for development and staging environments
  if (host.includes("vercel.app")) {
    // Check for subdomains or paths that indicate a geo-targeted version
    if (host.includes("-de")) return "DE";
    if (host.includes("-ru")) return "RU";
    if (host.includes("-tr")) return "TR";
    if (host.includes("-ae")) return "AE";
  }

  // For local development, try to get country from IP (for testing)
  if (host.includes("localhost") || host.includes("127.0.0.1")) {
    console.log("Local development detected - attempting IP-based detection");

    // For local testing, we'll use a fallback mechanism
    // This is a simplified approach for development - in production, we rely on Vercel/Cloudflare headers

    // You can use ?country=XX in the URL for testing specific countries
    // Or we can try to detect based on browser language as fallback
    const acceptLanguage = request.headers.get("accept-language");
    if (acceptLanguage) {
      // Extract the primary language code
      const primaryLang = acceptLanguage
        .split(",")[0]
        .trim()
        .split("-")[0]
        .toLowerCase();

      console.log("Browser language detected:", primaryLang);

      // Map language to country (simplified)
      if (primaryLang === "de") return "DE";
      if (primaryLang === "ru") return "RU";
      if (primaryLang === "tr") return "TR";
      if (primaryLang === "ar") return "AE";
    }

    // For local development in Germany, force German
    // This is a special case for your development environment
    return "DE"; // Force German for local development
  }

  console.log("No country detected from headers or domain");
  return null;
}

// Get the preferred locale from the request
function getLocale(request: NextRequest): string {
  const negotiatorHeaders: Record<string, string> = {};
  request.headers.forEach((value, key) => (negotiatorHeaders[key] = value));

  // DIRECT COUNTRY OVERRIDE - Check URL parameter first
  const url = new URL(request.url);
  const countryParam = url.searchParams.get("country");
  if (countryParam) {
    const countryCode = countryParam.toUpperCase();
    console.log("Country parameter found in URL:", countryCode);

    const countryLocale = countryLanguageMap[countryCode];
    if (countryLocale && locales.includes(countryLocale)) {
      console.log(
        "Setting locale directly from URL country parameter:",
        countryLocale
      );
      return countryLocale;
    }
  }

  // Check if locale is explicitly set in URL
  const localeParam = url.searchParams.get("locale");
  if (localeParam && locales.includes(localeParam as any)) {
    console.log("Locale set explicitly in URL:", localeParam);
    return localeParam as any;
  }

  // Check if we should skip geolocation (for testing purposes)
  const skipGeo = url.searchParams.has("skipGeo");
  if (!skipGeo) {
    // First check if we can determine locale from country
    const country = getUserCountry(request);
    if (country) {
      const countryLocale = countryLanguageMap[country];
      if (countryLocale && locales.includes(countryLocale)) {
        console.log(
          "Setting locale based on country detection:",
          country,
          "->",
          countryLocale
        );
        return countryLocale;
      }
    }
  }

  // If not, use negotiator and intl-localematcher to get best locale from browser settings
  let languages = new Negotiator({ headers: negotiatorHeaders }).languages();
  const detectedLocale = matchLocale(languages, locales, defaultLocale);
  console.log("Locale detected from browser settings:", detectedLocale);
  return detectedLocale;
}

// Note: We're using the countryLanguageMap defined in this file, not from siteConfig

// CRITICAL: Production Domain Handling - innovatio-pro.com should ALWAYS redirect to German
export function middleware(request: NextRequest) {
  const url = new URL(request.url);
  const pathname = url.pathname;
  const host = request.headers.get("host") || "";

  console.log("===== PROCESSING REQUEST =====");
  console.log("URL:", url.toString());
  console.log("Host:", host);

  // SPECIAL CASE FOR LOCAL DEVELOPMENT
  // Force German locale for localhost
  if (
    (host.includes("localhost") || host.includes("127.0.0.1")) &&
    !pathname.startsWith("/de") &&
    pathname !== "/de"
  ) {
    console.log("Local development detected - forcing German locale");
    const newPath = `/de${pathname === "/" ? "" : pathname}`;
    console.log(`Redirecting to: ${newPath}`);
    return NextResponse.redirect(new URL(newPath, request.url));
  }

  // STEP 1: Check if this is the production domain (innovatio-pro.com)
  // We need to make sure the website is ALWAYS in German for this domain
  if (host.includes("innovatio-pro.com")) {
    // If we're on any path other than /de, redirect to German version
    if (!pathname.startsWith("/de/") && pathname !== "/de") {
      const newPath = `/de${pathname === "/" ? "" : pathname}`;
      console.log(`PRODUCTION DOMAIN RULE: Force German for ${host}`);
      console.log(`Redirecting to: ${newPath}`);
      return NextResponse.redirect(new URL(newPath, request.url));
    }
    // Already on German path on production domain, no need to redirect
    return;
  }

  // For non-production domains, continue with normal language detection logic

  // Check if path already has a locale
  const pathnameHasLocale = locales.some(
    (locale) => pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`
  );

  // STEP 2: Check for country parameter
  const countryParam = url.searchParams.get("country");
  if (countryParam) {
    const countryCode = countryParam.toUpperCase();
    // Use the countryLanguageMap defined in this file, not from siteConfig
    const mappedLocale = countryLanguageMap[countryCode];

    console.log(
      `COUNTRY PARAM DETECTED: ${countryCode} -> ${
        mappedLocale || "not mapped"
      }`
    );

    if (mappedLocale && locales.includes(mappedLocale)) {
      // Check if we need to redirect (if not already on the right locale path)
      const currentLocale = pathnameHasLocale ? pathname.split("/")[1] : null;
      if (currentLocale !== mappedLocale) {
        // Build the new path with the correct locale
        const pathWithoutLocale = pathnameHasLocale
          ? pathname.substring(pathname.indexOf("/", 1))
          : pathname;
        const newPath = `/${mappedLocale}${
          pathWithoutLocale === "/" ? "" : pathWithoutLocale
        }`;
        console.log(`COUNTRY PARAM: ${countryCode} -> ${mappedLocale}`);
        console.log(`Redirecting to: ${newPath}`);

        // Create the redirect URL without the country parameter
        const newUrl = new URL(newPath, request.url);
        url.searchParams.forEach((value, key) => {
          if (key !== "country") {
            newUrl.searchParams.set(key, value);
          }
        });

        return NextResponse.redirect(newUrl);
      }
      return; // Already on correct locale path for country
    }
  }

  // STEP 3: Handle path without locale
  if (!pathnameHasLocale) {
    // For local development, force German locale for testing
    if (host.includes("localhost") || host.includes("127.0.0.1")) {
      console.log("Local development detected - forcing German locale");
      const newPath = `/de${pathname === "/" ? "" : pathname}`;
      console.log(`NO LOCALE IN PATH: Using de for local development`);
      console.log(`Redirecting to: ${newPath}`);
      return NextResponse.redirect(new URL(newPath, request.url));
    }

    // For production or other environments, try to detect country
    const country = getUserCountry(request);
    let detectedLocale = defaultLocale;

    if (country) {
      const countryLocale = countryLanguageMap[country];
      if (countryLocale && locales.includes(countryLocale)) {
        console.log(
          `Country detected: ${country} -> using locale ${countryLocale}`
        );
        detectedLocale = countryLocale;
      }
    } else {
      // If no country detected, try browser language preferences
      detectedLocale = getLocale(request);
      console.log(
        `No country detected, using browser preferences: ${detectedLocale}`
      );
    }

    const newPath = `/${detectedLocale}${pathname === "/" ? "" : pathname}`;
    console.log(`NO LOCALE IN PATH: Using ${detectedLocale}`);
    console.log(`Redirecting to: ${newPath}`);

    return NextResponse.redirect(new URL(newPath, request.url));
  }

  // Path already has locale and no overrides needed, do nothing
  return;
}

// Matcher configuration for middleware
export const config = {
  // Matcher ignoring `/_next/` and `/api/`
  matcher: ["/((?!api|_next/static|_next/image|favicon.ico).*)"],
};