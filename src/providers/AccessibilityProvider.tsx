'use client'

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'

// Definieren der Typen für die Barrierefreiheits-Einstellungen
export interface AccessibilitySettings {
  fontSize: number // 80-150%
  highContrast: boolean
  reducedMotion: boolean
  textToSpeechEnabled: boolean
}

// Definieren des Kontext-Typs
interface AccessibilityContextType {
  settings: AccessibilitySettings
  updateFontSize: (size: number) => void
  toggleHighContrast: () => void
  toggleReducedMotion: () => void
  toggleTextToSpeech: () => void
  resetSettings: () => void
  speakText: (text: string) => void
  stopSpeaking: () => void
}

// Standardeinstellungen
const defaultSettings: AccessibilitySettings = {
  fontSize: 100, // 100% ist die Standardgröße
  highContrast: false,
  reducedMotion: false,
  textToSpeechEnabled: false,
}

// Erstellen des Kontexts
const AccessibilityContext = createContext<AccessibilityContextType | undefined>(undefined)

// Hook für den Zugriff auf den Kontext
export const useAccessibility = () => {
  const context = useContext(AccessibilityContext)
  if (!context) {
    throw new Error('useAccessibility must be used within an AccessibilityProvider')
  }
  return context
}

// Provider-Komponente
export function AccessibilityProvider({ children }: { children: ReactNode }) {
  // State für die Einstellungen
  const [settings, setSettings] = useState<AccessibilitySettings>(defaultSettings)
  // Referenz für die Speech Synthesis
  const speechSynthesisRef = React.useRef<SpeechSynthesis | null>(null)

  // Laden der Einstellungen aus dem localStorage beim ersten Rendern
  useEffect(() => {
    // Nur auf dem Client ausführen
    if (typeof window !== 'undefined') {
      const savedSettings = localStorage.getItem('accessibility-settings')
      if (savedSettings) {
        try {
          const parsedSettings = JSON.parse(savedSettings)
          setSettings(parsedSettings)
        } catch (error) {
          console.error('Failed to parse accessibility settings:', error)
        }
      }

      // Initialisieren der Speech Synthesis
      if (window.speechSynthesis) {
        speechSynthesisRef.current = window.speechSynthesis
      }
    }
  }, [])

  // Speichern der Einstellungen im localStorage bei Änderungen
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('accessibility-settings', JSON.stringify(settings))
      
      // Anwenden der Einstellungen auf das HTML-Element
      applySettingsToDOM(settings)
    }
  }, [settings])

  // Anwenden der Einstellungen auf das DOM
  const applySettingsToDOM = (settings: AccessibilitySettings) => {
    // Font-Size auf dem HTML-Element setzen
    document.documentElement.style.fontSize = `${settings.fontSize}%`
    
    // High-Contrast-Modus
    if (settings.highContrast) {
      document.documentElement.classList.add('high-contrast')
    } else {
      document.documentElement.classList.remove('high-contrast')
    }
    
    // Reduzierte Bewegung
    if (settings.reducedMotion) {
      document.documentElement.classList.add('reduced-motion')
    } else {
      document.documentElement.classList.remove('reduced-motion')
    }
  }

  // Funktionen zum Aktualisieren der Einstellungen
  const updateFontSize = (size: number) => {
    // Sicherstellen, dass die Größe im erlaubten Bereich liegt
    const clampedSize = Math.min(Math.max(size, 80), 150)
    setSettings(prev => ({ ...prev, fontSize: clampedSize }))
  }

  const toggleHighContrast = () => {
    setSettings(prev => ({ ...prev, highContrast: !prev.highContrast }))
  }

  const toggleReducedMotion = () => {
    setSettings(prev => ({ ...prev, reducedMotion: !prev.reducedMotion }))
  }

  const toggleTextToSpeech = () => {
    setSettings(prev => ({ ...prev, textToSpeechEnabled: !prev.textToSpeechEnabled }))
  }

  const resetSettings = () => {
    setSettings(defaultSettings)
  }

  // Text-to-Speech-Funktionen
  const speakText = (text: string) => {
    if (speechSynthesisRef.current && settings.textToSpeechEnabled) {
      // Stoppen aller laufenden Sprachausgaben
      stopSpeaking()
      
      // Erstellen einer neuen Sprachausgabe
      const utterance = new SpeechSynthesisUtterance(text)
      
      // Sprache basierend auf der aktuellen Seiten-Sprache setzen
      const lang = document.documentElement.lang || 'en'
      utterance.lang = lang
      
      // Sprachausgabe starten
      speechSynthesisRef.current.speak(utterance)
    }
  }

  const stopSpeaking = () => {
    if (speechSynthesisRef.current) {
      speechSynthesisRef.current.cancel()
    }
  }

  // Kontext-Wert
  const value = {
    settings,
    updateFontSize,
    toggleHighContrast,
    toggleReducedMotion,
    toggleTextToSpeech,
    resetSettings,
    speakText,
    stopSpeaking,
  }

  return (
    <AccessibilityContext.Provider value={value}>
      {children}
    </AccessibilityContext.Provider>
  )
}
