'use client'

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { CMSAdapter } from '@/lib/cms/CMSAdapter'
import { LocalCMSAdapter } from '@/lib/cms/adapters/LocalCMSAdapter'
import { CMSType, Locale } from '@/lib/cms/types'

// Kontext-Typ
interface CMSContextType {
  adapter: CMSAdapter | null
  isLoading: boolean
  error: string | null
  setAdapter: (type: CMSType) => Promise<void>
  currentLocale: Locale
  setCurrentLocale: (locale: Locale) => void
}

// Erstellen des Kontexts
const CMSContext = createContext<CMSContextType | undefined>(undefined)

// Hook für den Zugriff auf den Kontext
export const useCMS = () => {
  const context = useContext(CMSContext)
  if (!context) {
    throw new Error('useCMS must be used within a CMSProvider')
  }
  return context
}

// Provider-Komponente
export function CMSProvider({ 
  children,
  defaultCMSType = 'local',
  defaultLocale = 'de'
}: { 
  children: ReactNode
  defaultCMSType?: CMSType
  defaultLocale?: Locale
}) {
  const [adapter, setAdapterState] = useState<CMSAdapter | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [currentLocale, setCurrentLocale] = useState<Locale>(defaultLocale)

  // Funktion zum Setzen des Adapters
  const setAdapter = async (type: CMSType) => {
    setIsLoading(true)
    setError(null)
    
    try {
      let newAdapter: CMSAdapter
      
      // Erstellen des entsprechenden Adapters
      switch (type) {
        case 'local':
          newAdapter = new LocalCMSAdapter()
          break
        case 'sanity':
          // Hier würde der Sanity-Adapter erstellt werden
          throw new Error('Sanity CMS adapter not implemented yet')
        case 'contentful':
          // Hier würde der Contentful-Adapter erstellt werden
          throw new Error('Contentful CMS adapter not implemented yet')
        case 'strapi':
          // Hier würde der Strapi-Adapter erstellt werden
          throw new Error('Strapi CMS adapter not implemented yet')
        default:
          throw new Error(`Unknown CMS type: ${type}`)
      }
      
      // Initialisieren des Adapters
      await newAdapter.initialize()
      
      // Setzen des Adapters
      setAdapterState(newAdapter)
    } catch (err) {
      console.error('Error initializing CMS adapter:', err)
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setIsLoading(false)
    }
  }

  // Initialisieren des Standard-Adapters beim ersten Rendern
  useEffect(() => {
    setAdapter(defaultCMSType)
  }, [defaultCMSType])

  // Kontext-Wert
  const value = {
    adapter,
    isLoading,
    error,
    setAdapter,
    currentLocale,
    setCurrentLocale,
  }

  return (
    <CMSContext.Provider value={value}>
      {children}
    </CMSContext.Provider>
  )
}
