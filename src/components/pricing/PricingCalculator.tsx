"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import MVPCalculator from "@/components/pricing/calculators/MVPCalculator";
import PrototypeCalculator from "@/components/pricing/calculators/PrototypeCalculator";
import HomepageCalculator from "@/components/pricing/calculators/HomepageCalculator";
import ConsultingCalculator from "@/components/pricing/calculators/ConsultingCalculator";
import LeadCaptureForm from "./LeadCaptureForm";
import PricingResult from "./ui/PricingResult";
import { Rocket, Lightbulb, Home, Headphones, ChevronRight, ChevronDown, X, ArrowLeft } from "lucide-react";
import { Section } from "@/components/ui/Section";
import { type Dictionary } from "@/lib/dictionary";
import { calculatePrice } from "./utils/pricingCalculations";

// Safety function to check if required dictionaries exist
function hasMissingTranslations(
  dict: Dictionary["prices"] | undefined
): boolean {
  if (!dict) return true;

  // Basic checks for essential content
  return !dict.title || !dict.subtitle;
}

export type CalculatorType =
  | "mvp"
  | "prototype"
  | "homepage"
  | "consulting"
  | "landingpage";

interface PricingCalculatorProps {
  dictionary?: Dictionary["prices"];
}

// Define the steps for the pricing calculator
type CalculatorStep = "select" | "configure" | "capture" | "result";

const PricingCalculator = ({ dictionary }: PricingCalculatorProps) => {
  // Safety check for missing translations
  if (hasMissingTranslations(dictionary)) {
    return (
      <div id="prices" className="w-full py-24 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-8 text-gray-900 dark:text-white">
            {dictionary?.title || "Our Pricing"}
          </h2>
          <p className="text-center text-gray-600 dark:text-gray-300 mb-12 max-w-3xl mx-auto">
            {dictionary?.description || "Contact us for pricing information"}
          </p>
          <div className="flex justify-center">
            <a
              href="#contact"
              className="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              {dictionary?.contactButton || "Contact Us"}
            </a>
          </div>
        </div>
      </div>
    );
  }
  // State for the current step in the calculator flow
  const [currentStep, setCurrentStep] = useState<CalculatorStep>("select");

  // State for the selected calculator and its data
  const [activeCalculator, setActiveCalculator] =
    useState<CalculatorType | null>(null);
  const [calculatorState, setCalculatorState] = useState<Record<string, any>>(
    {}
  );
  const [userInfo, setUserInfo] = useState({
    name: "",
    email: "",
    company: "",
  });
  const [calculationResults, setCalculationResults] = useState<
    Record<string, any>
  >({});

  // Mobile view state
  const [mobileExpanded, setMobileExpanded] = useState(false);

  // Handle calculator type selection
  const handleCalculatorSelect = (type: CalculatorType) => {
    setActiveCalculator(type);
    setCurrentStep("configure");
    setMobileExpanded(false);
  };

  // Handle calculator configuration submission
  const handleCalculatorSubmit = (data: Record<string, any>) => {
    if (activeCalculator) {
      setCalculatorState({ ...calculatorState, [activeCalculator]: data });
      setCurrentStep("capture");
    }
  };

  // Handle lead capture form submission
  const handleLeadSubmit = (data: {
    name: string;
    email: string;
    company: string;
  }) => {
    setUserInfo(data);

    // Calculate pricing based on the calculator type and data
    if (activeCalculator) {
      const calculatorData = calculatorState[activeCalculator] || {};
      const results = calculatePrice(activeCalculator, calculatorData);
      setCalculationResults(results);

      // In a real implementation, here you would:
      // 1. Submit the lead information to your backend/API
      // 2. Send the email with pricing details
      console.log("Lead captured:", data);
      console.log("Calculator data:", calculatorData);
      console.log("Calculation results:", results);

      // Show results
      setCurrentStep("result");
    }
  };

  // Handle reset to start over
  const handleReset = () => {
    setCurrentStep("select");
    setActiveCalculator(null);
    setCalculatorState({});
  };

  // Go back to previous step
  const handleBack = () => {
    if (currentStep === "configure") setCurrentStep("select");
    else if (currentStep === "capture") setCurrentStep("configure");
    else if (currentStep === "result") setCurrentStep("capture");
  };

  // Calculator type definitions with translations
  const calculatorTypes = [
    {
      id: "mvp" as CalculatorType,
      name:
        dictionary?.calculator?.calculatorTypes?.mvp?.name || "MVP Development",
      icon: <Rocket className="w-5 h-5" />,
      description:
        dictionary?.calculator?.calculatorTypes?.mvp?.description ||
        "Calculate the cost of building your Minimum Viable Product",
      tagline:
        dictionary?.calculator?.calculatorTypes?.mvp?.tagline ||
        "Get your app to market quickly with core features",
    },
    {
      id: "prototype" as CalculatorType,
      name:
        dictionary?.calculator?.calculatorTypes?.prototype?.name ||
        "Rapid Prototype",
      icon: <Lightbulb className="w-5 h-5" />,
      description:
        dictionary?.calculator?.calculatorTypes?.prototype?.description ||
        "Estimate your prototype development costs",
      tagline:
        dictionary?.calculator?.calculatorTypes?.prototype?.tagline ||
        "Test your idea with a functional prototype",
    },
    {
      id: "homepage" as CalculatorType,
      name:
        dictionary?.calculator?.calculatorTypes?.homepage?.name ||
        "Homepage & Landing",
      icon: <Home className="w-5 h-5" />,
      description:
        dictionary?.calculator?.calculatorTypes?.homepage?.description ||
        "Get pricing for your website or landing page",
      tagline:
        dictionary?.calculator?.calculatorTypes?.homepage?.tagline ||
        "Create a professional online presence",
    },
    {
      id: "landingpage" as CalculatorType,
      name: "Accessible Landing Page",
      icon: <Home className="w-5 h-5" />,
      description:
        "Calculate costs for WCAG 2.0 compliant landing pages with latest technologies",
      tagline: "Create accessible pages for all users",
    },
    {
      id: "consulting" as CalculatorType,
      name:
        dictionary?.calculator?.calculatorTypes?.consulting?.name ||
        "Technical Consulting",
      icon: <Headphones className="w-5 h-5" />,
      description:
        dictionary?.calculator?.calculatorTypes?.consulting?.description ||
        "Estimate consulting hours and packages",
      tagline:
        dictionary?.calculator?.calculatorTypes?.consulting?.tagline ||
        "Expert guidance for your technical challenges",
    },
  ];

  // Get the active calculator info
  const activeCalculatorInfo = activeCalculator
    ? calculatorTypes.find((c) => c.id === activeCalculator)
    : null;

  // Render the appropriate calculator component based on the active calculator
  const renderCalculatorContent = () => {
    if (!activeCalculator) return null;

    switch (activeCalculator) {
      case "mvp":
        return <MVPCalculator onSubmit={handleCalculatorSubmit} />;
      case "prototype":
        return <PrototypeCalculator onSubmit={handleCalculatorSubmit} />;
      case "homepage":
        return <HomepageCalculator onSubmit={handleCalculatorSubmit} />;
      case "consulting":
        return <ConsultingCalculator onSubmit={handleCalculatorSubmit} />;
      case "landingpage":
        return <HomepageCalculator onSubmit={handleCalculatorSubmit} />;
      default:
        return null;
    }
  };

  return (
    <Section
      id="pricing-calculator-widget"
      titleClassName="text-gray-900 dark:text-white text-center px-4"
      subtitleClassName="text-blue-600 dark:text-blue-400 text-center px-4"
      role="region"
      aria-label="Pricing Calculator"
      title={
        <>
          {dictionary?.title ?? "Preisrechner"}{" "}
          <span className="text-xs align-super bg-orange-500 text-white px-1.5 py-0.5 rounded-full font-semibold">
            Beta
          </span>
        </>
      }
      subtitle={
        dictionary?.subtitle ?? "Erhalten Sie ein maßgeschneidertes Angebot"
      }
      className="py-8 pb-4 sm:py-12 lg:py-16 relative w-full bg-white dark:bg-transparent"
      containerClassName="w-full max-w-[1400px] p-0"
    >
      {/* Remove the calculator button link since we're now embedded in the pricing section */}

      <div className="w-full mx-auto relative z-10">
        {/* Mobile-friendly step indicator */}
        <div
          className="mb-6 flex items-center justify-center"
          role="progressbar"
          aria-label="Calculator progress"
          aria-valuenow={
            currentStep === "select"
              ? 1
              : currentStep === "configure"
              ? 2
              : currentStep === "capture"
              ? 3
              : 4
          }
          aria-valuemin={1}
          aria-valuemax={4}
        >
          <div className="flex items-center space-x-2 bg-gray-200 dark:bg-gray-800 px-4 py-2 rounded-full shadow-sm">
            <div
              className={`w-2.5 h-2.5 rounded-full ${
                currentStep === "select"
                  ? "bg-blue-600 dark:bg-blue-400"
                  : "bg-gray-400 dark:bg-gray-600"
              }`}
            ></div>
            <div
              className={`w-2.5 h-2.5 rounded-full ${
                currentStep === "configure"
                  ? "bg-blue-600 dark:bg-blue-400"
                  : "bg-gray-400 dark:bg-gray-600"
              }`}
            ></div>
            <div
              className={`w-2.5 h-2.5 rounded-full ${
                currentStep === "capture"
                  ? "bg-blue-600 dark:bg-blue-400"
                  : "bg-gray-400 dark:bg-gray-600"
              }`}
            ></div>
            <div
              className={`w-2.5 h-2.5 rounded-full ${
                currentStep === "result"
                  ? "bg-blue-600 dark:bg-blue-400"
                  : "bg-gray-400 dark:bg-gray-600"
              }`}
            ></div>
          </div>
        </div>

        {/* Back button (only show when not on first step) */}
        <div className="w-full bg-transparent px-4">
          {currentStep !== "select" && (
            <button
              onClick={handleBack}
              className="mb-4 flex items-center text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
              aria-label="Go back to previous step"
              tabIndex={0}
            >
              <ArrowLeft className="w-4 h-4 mr-1" aria-hidden="true" />
              Back
            </button>
          )}
        </div>

        {/* Main content container */}
        <AnimatePresence mode="wait">
          {/* Step 1: Select calculator type */}
          {currentStep === "select" && (
            <motion.div
              key="select-step"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.3 }}
            >
              {/* Mobile view: Collapsible selector */}
              <div className="block md:hidden mb-6 px-4">
                <button
                  onClick={() => setMobileExpanded(!mobileExpanded)}
                  className="w-full bg-white dark:bg-gray-800 rounded-xl p-4 shadow-md border border-gray-200 dark:border-gray-700 flex items-center justify-between"
                  aria-expanded={mobileExpanded}
                  aria-controls="mobile-calculator-options"
                  aria-haspopup="true"
                >
                  <span className="text-gray-800 dark:text-gray-200 font-medium">
                    Select service type
                  </span>
                  <ChevronDown
                    className={`w-5 h-5 text-gray-500 dark:text-gray-400 transition-transform ${
                      mobileExpanded ? "rotate-180" : ""
                    }`}
                    aria-hidden="true"
                  />
                </button>

                {mobileExpanded && (
                  <div
                    id="mobile-calculator-options"
                    className="mt-2 bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden"
                    role="menu"
                    aria-labelledby="calculator-type-selector"
                  >
                    {calculatorTypes.map((calc) => (
                      <button
                        key={calc.id}
                        onClick={() => handleCalculatorSelect(calc.id)}
                        className="w-full p-4 flex items-center border-b border-gray-200 dark:border-gray-700 last:border-0 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                        role="menuitem"
                        tabIndex={0}
                        aria-label={`Select ${calc.name} calculator`}
                      >
                        <div className="bg-blue-100 dark:bg-blue-900/30 p-2 rounded-full text-blue-600 dark:text-blue-400 mr-3">
                          {calc.icon}
                        </div>
                        <div className="flex-1 text-left">
                          <h3 className="font-medium text-gray-800 dark:text-gray-200">
                            {calc.name}
                          </h3>
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            {calc.tagline}
                          </p>
                        </div>
                        <ChevronRight className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                      </button>
                    ))}
                  </div>
                )}
              </div>

              {/* Desktop view: Grid selector */}
              <div
                className="hidden md:grid grid-cols-5 gap-4 px-4 lg:px-8"
                role="group"
                aria-label="Select calculator type"
              >
                {calculatorTypes.map((calc) => (
                  <button
                    key={calc.id}
                    onClick={() => handleCalculatorSelect(calc.id)}
                    className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-md border border-gray-200 dark:border-gray-700 hover:shadow-lg hover:border-blue-500/50 transition-all duration-300 flex flex-col items-center text-center group h-full"
                    tabIndex={0}
                    aria-label={`Select ${calc.name} calculator: ${calc.tagline}`}
                  >
                    <div className="bg-blue-100 dark:bg-blue-900/30 p-2 rounded-full text-blue-600 dark:text-blue-400 mb-2 group-hover:scale-110 transition-transform">
                      {calc.icon}
                    </div>
                    <h3 className="font-semibold text-gray-800 dark:text-gray-200 mb-1 text-sm">
                      {calc.name}
                    </h3>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mb-2">
                      {calc.tagline}
                    </p>
                    <div className="mt-auto text-xs font-medium text-blue-600 dark:text-blue-400 flex items-center">
                      {dictionary?.contactButton || "Angebot anfordern"}{" "}
                      <ChevronRight className="w-3 h-3 ml-1 group-hover:translate-x-1 transition-transform" />
                    </div>
                  </button>
                ))}
              </div>
            </motion.div>
          )}

          {/* Step 2: Configure calculator */}
          {currentStep === "configure" && activeCalculator && (
            <motion.div
              tabIndex={-1}
              key="configure-step"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.3 }}
              className="bg-white dark:bg-gray-800 rounded-none sm:rounded-xl py-5 sm:py-8 shadow-xl dark:shadow-black/30 border-0 sm:border border-gray-200 dark:border-gray-700 w-full"
              style={{
                width: "100%",
                maxWidth: "100%",
                marginLeft: "auto",
                marginRight: "auto",
              }}
            >
              <div className="px-4 sm:px-6 md:px-8 lg:px-12">
                {renderCalculatorContent()}
              </div>
            </motion.div>
          )}

          {/* Step 3: Capture lead */}
          {currentStep === "capture" && activeCalculator && (
            <motion.div
              tabIndex={-1}
              key="capture-step"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.3 }}
              className="bg-white dark:bg-gray-800 rounded-none sm:rounded-xl py-5 sm:py-8 shadow-xl dark:shadow-black/30 border-0 sm:border border-gray-200 dark:border-gray-700 w-full"
              style={{
                width: "100%",
                maxWidth: "100%",
                marginLeft: "auto",
                marginRight: "auto",
              }}
            >
              <div className="px-4 sm:px-6 md:px-8 lg:px-12">
                <LeadCaptureForm
                  onSubmit={handleLeadSubmit}
                  calculatorType={activeCalculator}
                />
              </div>
            </motion.div>
          )}

          {/* Step 4: Show results */}
          {currentStep === "result" && activeCalculator && (
            <motion.div
              tabIndex={-1}
              key="result-step"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.3 }}
              className="bg-white dark:bg-gray-800 rounded-none sm:rounded-xl py-5 sm:py-8 shadow-xl dark:shadow-black/30 border-0 sm:border border-gray-200 dark:border-gray-700 w-full"
              style={{
                width: "100%",
                maxWidth: "100%",
                marginLeft: "auto",
                marginRight: "auto",
              }}
            >
              <div className="px-4 sm:px-6 md:px-8 lg:px-12">
                <PricingResult
                  calculatorType={activeCalculator}
                  result={calculationResults}
                  userInfo={userInfo}
                  onReset={handleReset}
                />
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </Section>
  );
};

export default PricingCalculator;
