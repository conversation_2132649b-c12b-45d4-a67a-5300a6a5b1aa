"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { Mail, User, Building2, ArrowRight, Lock, AlertCircle } from "lucide-react";
import { CalculatorType } from "./PricingCalculator";

interface LeadCaptureFormProps {
  onSubmit: (data: { name: string; email: string; company: string }) => void;
  calculatorType: CalculatorType;
}

const LeadCaptureForm = ({ onSubmit, calculatorType }: LeadCaptureFormProps) => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    company: "",
    agreeToTerms: false
  });
  
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === "checkbox" ? checked : value
    });
    
    // Clear error for this field when user types
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: ""
      });
    }
  };
  
  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.name.trim()) {
      newErrors.name = "Name is required";
    }
    
    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    }
    
    if (!formData.agreeToTerms) {
      newErrors.agreeToTerms = "You must agree to the terms";
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (validateForm()) {
      onSubmit({
        name: formData.name,
        email: formData.email,
        company: formData.company
      });
    }
  };
  
  const getCalculatorTitle = () => {
    switch (calculatorType) {
      case "mvp":
        return "MVP Development";
      case "prototype":
        return "Rapid Prototype";
      case "homepage":
        return "Homepage & Landing";
      case "consulting":
        return "Technical Consulting";
      default:
        return "Custom Quote";
    }
  };

  return (
    <div className="w-full" role="region" aria-label="Kontaktformular">
      <motion.div
        initial={{ opacity: 0, y: 5 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="text-center mb-6 w-full"
      >
        <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
          Get Your {getCalculatorTitle()} Quote
        </h3>
        <p className="text-gray-600 dark:text-gray-300 text-sm">
          Complete the form below to receive a detailed quote via email
        </p>
      </motion.div>

      <form onSubmit={handleSubmit} className="space-y-4 w-full" noValidate aria-describedby="form-description">
        <p id="form-description" className="sr-only">Bitte füllen Sie alle Pflichtfelder aus, um ein individuelles Angebot zu erhalten.</p>
        <div>
          <label
            className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
            htmlFor="name"
            id="name-label"
          >
            Vollständiger Name <span aria-hidden="true">*</span>
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <User className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              className={`pl-10 w-full py-2.5 px-4 bg-gray-50 dark:bg-gray-700/50 border ${
                errors.name
                  ? "border-red-500 dark:border-red-400"
                  : "border-gray-300 dark:border-gray-600"
              } rounded-lg focus:outline-none focus:ring-2 focus:ring-[#3f5781] dark:focus:ring-[#5d7ab3] focus:border-transparent transition-colors`}
              placeholder="Max Mustermann"
              required
              aria-required="true"
              aria-invalid={errors.name ? "true" : "false"}
              aria-describedby={errors.name ? "name-error" : undefined}
            />
            {errors.name && (
              <p className="text-red-500 text-xs mt-1 flex items-center" id="name-error" role="alert">
                <AlertCircle className="h-3 w-3 mr-1" aria-hidden="true" /> {errors.name}
              </p>
            )}
          </div>
        </div>

        <div>
          <label
            className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
            htmlFor="email"
            id="email-label"
          >
            E-Mail-Adresse <span aria-hidden="true">*</span>
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Mail className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              className={`pl-10 w-full py-2.5 px-4 bg-gray-50 dark:bg-gray-700/50 border ${
                errors.email
                  ? "border-red-500 dark:border-red-400"
                  : "border-gray-300 dark:border-gray-600"
              } rounded-lg focus:outline-none focus:ring-2 focus:ring-[#3f5781] dark:focus:ring-[#5d7ab3] focus:border-transparent transition-colors`}
              placeholder="<EMAIL>"
              required
              aria-required="true"
              aria-invalid={errors.email ? "true" : "false"}
              aria-describedby={errors.email ? "email-error" : undefined}
              autoComplete="email"
            />
            {errors.email && (
              <p className="text-red-500 text-xs mt-1 flex items-center" id="email-error" role="alert">
                <AlertCircle className="h-3 w-3 mr-1" aria-hidden="true" /> {errors.email}
              </p>
            )}
          </div>
        </div>

        <div>
          <label
            className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
            htmlFor="company"
            id="company-label"
          >
            Firmenname{" "}
            <span className="text-gray-400 text-xs">(Optional)</span>
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Building2 className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              id="company"
              name="company"
              value={formData.company}
              onChange={handleChange}
              className="pl-10 w-full py-2.5 px-4 bg-gray-50 dark:bg-gray-700/50 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#3f5781] dark:focus:ring-[#5d7ab3] focus:border-transparent transition-colors"
              placeholder="Musterfirma GmbH"
              aria-required="false"
              autoComplete="organization"
            />
          </div>
        </div>

        <div className="pt-2">
          <div className="flex items-start">
            <div className="flex items-center h-5">
              <input
                id="agreeToTerms"
                name="agreeToTerms"
                type="checkbox"
                checked={formData.agreeToTerms}
                onChange={handleChange}
                className="h-4 w-4 text-[#3f5781] dark:text-[#5d7ab3] focus:ring-[#3f5781] dark:focus:ring-[#5d7ab3] border-gray-300 dark:border-gray-600 rounded"
                required
                aria-required="true"
                aria-invalid={errors.agreeToTerms ? "true" : "false"}
                aria-describedby={errors.agreeToTerms ? "terms-error" : undefined}
              />
            </div>
            <div className="ml-3 text-sm">
              <label
                htmlFor="agreeToTerms"
                className="font-medium text-gray-700 dark:text-gray-300"
                id="terms-label"
              >
                Ich stimme zu, ein Preisangebot per E-Mail zu erhalten <span aria-hidden="true">*</span>
              </label>
              <p className="text-gray-500 dark:text-gray-400 text-xs mt-1 flex items-center">
                <Lock className="h-3.5 w-3.5 mr-1" /> Your information is secure
                and will not be shared
              </p>
              {errors.agreeToTerms && (
                <p className="text-red-500 text-xs mt-1 flex items-center" id="terms-error" role="alert">
                  <AlertCircle className="h-3 w-3 mr-1" aria-hidden="true" /> {errors.agreeToTerms}
                </p>
              )}
            </div>
          </div>
        </div>

        <button
          type="submit"
          className="w-full mt-6 flex items-center justify-center py-3 px-4 bg-[#3f5781] hover:bg-[#324567] text-white font-medium rounded-lg transition-all duration-300 shadow-sm hover:shadow focus:outline-none focus:ring-2 focus:ring-[#3f5781] focus:ring-offset-2 dark:focus:ring-offset-gray-800"
          aria-label="Formular absenden und Angebot erhalten"
        >
          Mein Angebot erhalten <ArrowRight className="ml-2 h-4 w-4" aria-hidden="true" />
        </button>
      </form>
    </div>
  );
};

export default LeadCaptureForm;
