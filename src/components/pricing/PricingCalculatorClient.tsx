'use client';

import { type Dictionary } from "@/lib/dictionary";
import dynamic from 'next/dynamic';

// Dynamically import PricingCalculator with SSR disabled to avoid build-time evaluation
const PricingCalculator = dynamic(() => import("./PricingCalculator"), {
  ssr: false,
  loading: () => (
    <div className="h-96 w-full flex items-center justify-center bg-white dark:bg-gray-900">
      <div className="text-center">
        <div className="w-16 h-16 border-t-4 border-b-4 border-blue-500 rounded-full mx-auto mb-4 animate-spin"></div>
        <p className="text-gray-600 dark:text-gray-300">
          Loading pricing calculator...
        </p>
      </div>
    </div>
  ),
});

interface PricingCalculatorClientProps {
  dictionary?: Dictionary["prices"];
}

export default function PricingCalculatorClient({ dictionary }: PricingCalculatorClientProps) {
  return <PricingCalculator dictionary={dictionary} />;
}
