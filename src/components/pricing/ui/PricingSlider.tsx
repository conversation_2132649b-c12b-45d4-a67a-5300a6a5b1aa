"use client";

import { useState, useEffect } from "react";

interface PricingSliderProps {
  min: number;
  max: number;
  step: number;
  value: number;
  onChange: (value: number) => void;
}

const PricingSlider = ({ min, max, step, value, onChange }: PricingSliderProps) => {
  const [sliderValue, setSliderValue] = useState(value);
  
  // Handle internal state change
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = parseInt(e.target.value, 10);
    setSliderValue(newValue);
  };
  
  // Propagate changes to parent after user releases slider
  const handleChangeEnd = () => {
    onChange(sliderValue);
  };
  
  // Keep local state in sync with props
  useEffect(() => {
    setSliderValue(value);
  }, [value]);
  
  // Calculate percentage for styling
  const percentage = ((sliderValue - min) / (max - min)) * 100;
  
  return (
    <div className="relative">
      <input
        type="range"
        min={min}
        max={max}
        step={step}
        value={sliderValue}
        onChange={handleChange}
        onMouseUp={handleChangeEnd}
        onTouchEnd={handleChangeEnd}
        onKeyUp={handleChangeEnd}
        className="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer"
        style={{
          background: `linear-gradient(to right, rgb(37, 99, 235) 0%, rgb(37, 99, 235) ${percentage}%, rgb(229, 231, 235) ${percentage}%, rgb(229, 231, 235) 100%)`,
        }}
      />

      {/* Custom styling for Webkit browsers */}
      <style jsx>{`
        input[type="range"] {
          -webkit-appearance: none;
          height: 8px;
          border-radius: 8px;
        }

        input[type="range"]::-webkit-slider-thumb {
          -webkit-appearance: none;
          appearance: none;
          width: 20px;
          height: 20px;
          border-radius: 50%;
          background: rgb(37, 99, 235); /* blue-600 */
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          cursor: pointer;
          transition: all 0.2s ease;
        }

        input[type="range"]::-webkit-slider-thumb:hover {
          transform: scale(1.1);
          background: rgb(29, 78, 216); /* blue-700 */
        }

        input[type="range"]::-moz-range-thumb {
          width: 20px;
          height: 20px;
          border-radius: 50%;
          background: rgb(37, 99, 235); /* blue-600 */
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          cursor: pointer;
          border: none;
          transition: all 0.2s ease;
        }

        input[type="range"]::-moz-range-thumb:hover {
          transform: scale(1.1);
          background: rgb(29, 78, 216); /* blue-700 */
        }

        /* For dark mode */
        @media (prefers-color-scheme: dark) {
          input[type="range"] {
            background: #374151;
          }

          input[type="range"]::-webkit-slider-thumb {
            background: rgb(59, 130, 246); /* blue-500 for dark mode */
          }

          input[type="range"]::-webkit-slider-thumb:hover {
            background: rgb(37, 99, 235); /* blue-600 for dark mode hover */
          }

          input[type="range"]::-moz-range-thumb {
            background: rgb(59, 130, 246); /* blue-500 for dark mode */
          }

          input[type="range"]::-moz-range-thumb:hover {
            background: rgb(37, 99, 235); /* blue-600 for dark mode hover */
          }
        }
      `}</style>
    </div>
  );
};

export default PricingSlider;
