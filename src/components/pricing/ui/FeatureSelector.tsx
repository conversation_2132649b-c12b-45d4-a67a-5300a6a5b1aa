"use client";

import { useState, useEffect } from "react";
import { Check, X } from "lucide-react";

interface Feature {
  id: string;
  name: string;
  description?: string;
  default?: boolean;
  required?: boolean;
}

interface FeatureSelectorProps {
  features: Feature[];
  selectedFeatures: string[];
  onChange: (selectedIds: string[]) => void;
}

const FeatureSelector = ({ features, selectedFeatures, onChange }: FeatureSelectorProps) => {
  const [selected, setSelected] = useState<string[]>(selectedFeatures);
  
  // Keep local state in sync with props
  useEffect(() => {
    setSelected(selectedFeatures);
  }, [selectedFeatures]);
  
  const toggleFeature = (featureId: string, required = false) => {
    if (required) return; // Can't toggle required features
    
    let updatedFeatures: string[];
    
    if (selected.includes(featureId)) {
      updatedFeatures = selected.filter(id => id !== featureId);
    } else {
      updatedFeatures = [...selected, featureId];
    }
    
    setSelected(updatedFeatures);
    onChange(updatedFeatures);
  };
  
  return (
    <div className="space-y-2">
      {features.map((feature) => {
        const isSelected = selected.includes(feature.id) || feature.required;
        return (
          <div
            key={feature.id}
            className={`p-3 rounded-lg border cursor-pointer transition-all duration-300 flex items-start ${
              isSelected 
                ? "border-[#3f5781] bg-[#3f5781]/5 dark:bg-[#3f5781]/20" 
                : "border-gray-200 dark:border-gray-700 hover:border-[#3f5781]/50 dark:hover:border-[#3f5781]/50"
            } ${feature.required ? "cursor-not-allowed" : "cursor-pointer"}`}
            onClick={() => toggleFeature(feature.id, feature.required)}
          >
            <div className={`w-5 h-5 rounded-md flex items-center justify-center flex-shrink-0 mt-0.5 ${
              isSelected 
                ? "bg-[#3f5781] text-white" 
                : "border-2 border-gray-300 dark:border-gray-600"
            } ${feature.required ? "bg-gray-400 dark:bg-gray-500" : ""}`}>
              {isSelected && <Check className="w-3 h-3" />}
            </div>
            <div className="ml-3 flex-1">
              <div className="flex items-center">
                <span className="font-medium text-gray-800 dark:text-gray-200">
                  {feature.name}
                </span>
                {feature.required && (
                  <span className="ml-2 text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-0.5 rounded-full">
                    Required
                  </span>
                )}
              </div>
              {feature.description && (
                <div className="text-xs text-gray-500 dark:text-gray-400 mt-0.5">
                  {feature.description}
                </div>
              )}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default FeatureSelector;
