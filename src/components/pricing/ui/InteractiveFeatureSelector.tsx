"use client";

import { useState, useRef, useEffect } from "react";
import { X, ChevronDown, Check, Info, Search } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

interface Feature {
  id: string;
  name: string;
  description?: string;
  category?: string; // Optional: might be useful for grouping in the dropdown
}

interface InteractiveFeatureSelectorProps {
  availableFeatures: Feature[];
  selectedFeatures: string[]; // Array of feature IDs
  onChange: (selectedIds: string[]) => void;
  label?: string;
  infoText?: string;
  placeholder?: string;
}

const InteractiveFeatureSelector = ({
  availableFeatures,
  selectedFeatures,
  onChange,
  label = "Select Features",
  infoText,
  placeholder = "Click or type to add features...",
}: InteractiveFeatureSelectorProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const modalRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLDivElement>(null);
  const scrollPositionRef = useRef(0);

  const handleSelect = (featureId: string) => {
    if (!selectedFeatures.includes(featureId)) {
      onChange([...selectedFeatures, featureId]);
    }
    setSearchTerm(""); // Clear search after selection
    // We don't close modal after selection to allow multiple selections
  };

  const handleRemove = (featureId: string) => {
    onChange(selectedFeatures.filter((id) => id !== featureId));
  };

  const openModal = () => {
    // Store current scroll position
    scrollPositionRef.current = window.scrollY;
    setIsOpen(true);
  };

  const closeModal = (e?: React.MouseEvent) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    setIsOpen(false);
    setSearchTerm("");

    // Use setTimeout to ensure the scroll is applied after the modal is closed
    setTimeout(() => {
      window.scrollTo({
        top: scrollPositionRef.current,
        behavior: "auto",
      });
    }, 10);
  };

  const getFeatureName = (id: string) => {
    return availableFeatures.find((f) => f.id === id)?.name || id;
  };

  // Filter features based on search term and exclude already selected ones
  const filteredFeatures = availableFeatures.filter(
    (feature) =>
      feature.name.toLowerCase().includes(searchTerm.toLowerCase()) &&
      !selectedFeatures.includes(feature.id)
  );

  // Close modal when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        modalRef.current &&
        !modalRef.current.contains(event.target as Node)
      ) {
        closeModal();
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
      // Prevent body scrolling when modal is open
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      document.body.style.overflow = "";
    };
  }, [isOpen]);

  return (
    <div className="mb-6">
      {/* Label */}
      {label && (
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          {label}
        </label>
      )}

      {/* Optional Info Text */}
      {infoText && (
        <div className="bg-gray-50 dark:bg-gray-800/50 p-3 rounded-lg border border-gray-200 dark:border-gray-700 mb-3 text-xs text-gray-600 dark:text-gray-400 flex items-start">
          <Info className="w-4 h-4 mr-1.5 flex-shrink-0 mt-0.5" />
          {/* Use dangerouslySetInnerHTML cautiously, ensure infoText is controlled */}
          <span dangerouslySetInnerHTML={{ __html: infoText }} />
        </div>
      )}

      {/* Input Area with Tags */}
      <div className="relative" ref={inputRef}>
        <div
          className="w-full bg-white dark:bg-gray-800 rounded-lg border border-gray-300 dark:border-gray-700 p-2 flex flex-wrap items-center gap-1.5 min-h-[42px] cursor-pointer focus-within:border-blue-600 focus-within:ring-1 focus-within:ring-blue-600 transition-colors duration-200"
          onClick={openModal} // Open modal on click
        >
          {/* Selected Feature Tags */}
          {selectedFeatures.map((id) => (
            <motion.span
              key={id}
              layout // Animate layout changes
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{ type: "spring", stiffness: 500, damping: 30 }}
              className="flex items-center bg-blue-100 dark:bg-blue-600/20 text-blue-600 dark:text-blue-400 text-xs font-medium px-2 py-0.5 rounded-full"
            >
              {getFeatureName(id)}
              <button
                type="button"
                onClick={(e) => {
                  e.stopPropagation(); // Prevent dropdown toggle
                  handleRemove(id);
                }}
                className="ml-1.5 text-blue-600/70 dark:text-blue-400/70 hover:text-blue-600 dark:hover:text-blue-400 focus:outline-none"
                aria-label={`Remove ${getFeatureName(id)}`}
              >
                <X size={12} />
              </button>
            </motion.span>
          ))}

          {/* Placeholder Text */}
          {selectedFeatures.length === 0 && (
            <span className="text-sm text-gray-500 px-1 py-0.5 flex-1">
              {placeholder}
            </span>
          )}

          {/* Search Icon */}
          <Search className="ml-auto text-gray-500 w-5 h-5" />
        </div>

        {/* Modal for selecting features */}
        <AnimatePresence>
          {isOpen && (
            <>
              {/* Backdrop */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.2 }}
                className="fixed inset-0 bg-black/40 dark:bg-black/70 z-40"
                onClick={(e) => closeModal(e)}
              />

              {/* Modal */}
              <motion.div
                ref={modalRef}
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.95 }}
                transition={{ duration: 0.15 }}
                className="fixed inset-x-4 top-1/4 z-50 bg-white dark:bg-gray-800 rounded-xl shadow-2xl max-h-[70vh] flex flex-col overflow-hidden"
              >
                {/* Modal Header */}
                <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
                  <h3 className="font-medium text-gray-900 dark:text-white">
                    Select Features
                  </h3>
                  <button
                    onClick={closeModal}
                    className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                  >
                    <X size={20} />
                  </button>
                </div>

                {/* Search Input */}
                <div className="p-3 sticky top-0 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
                  <div className="relative">
                    <Search className="absolute left-3 top-2.5 w-4 h-4 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Search features..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      autoFocus
                      className="w-full pl-9 pr-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600 dark:bg-gray-700 dark:text-white"
                    />
                    {searchTerm && (
                      <button
                        onClick={() => setSearchTerm("")}
                        className="absolute right-3 top-2.5 text-gray-400"
                      >
                        <X size={16} />
                      </button>
                    )}
                  </div>
                </div>

                {/* Feature List */}
                <div className="overflow-y-auto flex-1">
                  {filteredFeatures.length > 0 ? (
                    <ul className="py-2">
                      {filteredFeatures.map((feature) => (
                        <li
                          key={feature.id}
                          onClick={() => handleSelect(feature.id)}
                          className="px-4 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 active:bg-gray-200 dark:active:bg-gray-600 cursor-pointer"
                        >
                          <div className="font-medium">{feature.name}</div>
                          {feature.description && (
                            <div className="text-xs text-gray-500 dark:text-gray-400 mt-0.5">
                              {feature.description}
                            </div>
                          )}
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <div className="py-8 text-center text-gray-500 dark:text-gray-400">
                      {searchTerm
                        ? "No matching features found."
                        : "All features selected."}
                    </div>
                  )}
                </div>

                {/* Modal Footer */}
                <div className="p-3 border-t border-gray-200 dark:border-gray-700 flex justify-end bg-gray-50 dark:bg-gray-800">
                  <button
                    onClick={closeModal}
                    className="px-4 py-2 text-sm bg-blue-600 hover:bg-blue-700 text-white rounded-lg shadow-sm transition-colors"
                  >
                    Done
                  </button>
                </div>
              </motion.div>
            </>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default InteractiveFeatureSelector;