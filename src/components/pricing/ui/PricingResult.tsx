"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { Mail, Download, Calendar, Clock, DollarSign, CheckCircle, ChevronDown, ChevronUp } from "lucide-react";
import { formatPrice } from "../utils/pricingCalculations";
import { CalculatorType } from "../PricingCalculator";

interface PricingResultProps {
  calculatorType: CalculatorType;
  result: Record<string, any>;
  userInfo: {
    name: string;
    email: string;
    company: string;
  };
  onReset: () => void;
}

const PricingResult = ({ calculatorType, result, userInfo, onReset }: PricingResultProps) => {
  const [showDetails, setShowDetails] = useState(false);
  
  const getCalculatorTitle = () => {
    switch (calculatorType) {
      case "mvp":
        return "MVP Development";
      case "prototype":
        return "Rapid Prototype";
      case "homepage":
        return "Homepage & Landing";
      case "consulting":
        return "Technical Consulting";
      default:
        return "Custom Quote";
    }
  };
  
  const getFeatures = () => {
    switch (calculatorType) {
      case "mvp":
        return [
          "Flutter cross-platform development",
          "Core functionality implementation",
          "User authentication",
          "Data storage solution",
          "Basic admin dashboard",
          "Initial setup & deployment",
          "2 revision rounds"
        ];
      case "prototype":
        return [
          "Flutter-powered rapid development",
          "Cross-platform compatibility",
          "Basic user authentication",
          "Simplified data management",
          "Automated testing framework",
          "Development documentation",
          "1 revision round"
        ];
      case "homepage":
        return [
          "Responsive design",
          "Contact forms & CTAs",
          "AI-powered heatmap analysis",
          "48-hour conversion guarantee",
          "SSL certificate & security setup",
          "Analytics integration",
          "1 year hosting & maintenance"
        ];
      case "consulting":
        return [
          `Up to ${result.hours} hours of expert consultation`,
          "CTO-level technical guidance",
          "Code reviews & best practices",
          "Architecture recommendations",
          "Technology stack optimization",
          "Regular progress updates",
          "Priority communication channel"
        ];
      default:
        return [];
    }
  };
  
  const isConsulting = calculatorType === "consulting";
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4 }}
      className="text-center w-full"
    >
      <div className="mb-6 w-full">
        <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
          Your {getCalculatorTitle()} Quote
        </h3>
        <p className="text-gray-600 dark:text-gray-300 text-sm">
          Thank you, {userInfo.name}! We've sent a detailed quote to{" "}
          {userInfo.email}.
        </p>
      </div>

      <div className="bg-[#3f5781]/5 dark:bg-[#3f5781]/20 rounded-xl p-6 mb-6 text-left w-full">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
          <div>
            <h4 className="text-lg font-bold text-[#3f5781] dark:text-[#8fa3c7]">
              {isConsulting
                ? formatPrice(result.hourlyRate || 0, true)
                : formatPrice(result.price)}
            </h4>
            {!isConsulting && (
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Market rate:{" "}
                <span className="line-through">
                  {formatPrice(result.marketPrice)}
                </span>
              </p>
            )}
          </div>
          <div className="mt-2 sm:mt-0 flex items-center">
            <Clock className="w-4 h-4 text-[#3f5781] dark:text-[#8fa3c7] mr-1.5" />
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {result.timeline}
            </span>
          </div>
        </div>

        {result.savings > 0 && (
          <div className="bg-green-100 dark:bg-green-900/30 px-3 py-2 rounded-lg mb-4 inline-block">
            <span className="text-sm font-semibold text-green-700 dark:text-green-300 flex items-center">
              <CheckCircle className="w-4 h-4 mr-1.5" />
              Save {formatPrice(result.savings)}!
            </span>
          </div>
        )}

        <div className="mt-4">
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="flex items-center text-[#3f5781] dark:text-[#8fa3c7] text-sm font-medium"
          >
            {showDetails ? "Hide details" : "Show details"}
            {showDetails ? (
              <ChevronUp className="w-4 h-4 ml-1" />
            ) : (
              <ChevronDown className="w-4 h-4 ml-1" />
            )}
          </button>

          {showDetails && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              transition={{ duration: 0.3 }}
              className="mt-3"
            >
              <h5 className="font-medium text-gray-800 dark:text-gray-200 mb-2">
                What's included:
              </h5>
              <ul className="space-y-1.5">
                {getFeatures().map((feature, index) => (
                  <li key={index} className="flex items-start text-sm">
                    <CheckCircle className="w-4 h-4 text-green-600 dark:text-green-400 mr-1.5 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-700 dark:text-gray-300">
                      {feature}
                    </span>
                  </li>
                ))}
              </ul>

              {isConsulting && (
                <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <p className="text-sm text-blue-800 dark:text-blue-300">
                    <span className="font-medium">Total estimated cost:</span>{" "}
                    {formatPrice(result.price)} for {result.hours} hours
                  </p>
                </div>
              )}
            </motion.div>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mb-4">
        <a
          href={`mailto:<EMAIL>?subject=Quote Follow-up: ${getCalculatorTitle()}&body=Hi,%0A%0AI've received my ${getCalculatorTitle()} quote and would like to discuss it further.%0A%0ABest regards,%0A${
            userInfo.name
          }`}
          className="flex items-center justify-center py-2.5 px-4 bg-[#3f5781] hover:bg-[#324567] text-white font-medium rounded-lg transition-all duration-300 shadow-sm hover:shadow focus:outline-none focus:ring-2 focus:ring-[#3f5781] focus:ring-offset-2 dark:focus:ring-offset-gray-800"
        >
          <Mail className="w-4 h-4 mr-2" />
          Contact Us
        </a>
        <button
          onClick={onReset}
          className="flex items-center justify-center py-2.5 px-4 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 font-medium rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600 transition-all duration-300 shadow-sm hover:shadow focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
        >
          <Calendar className="w-4 h-4 mr-2" />
          Get Another Quote
        </button>
      </div>

      <button
        onClick={() => {
          // In a real implementation, this would generate and download a PDF
          alert("This would download a PDF quote in a real implementation");
        }}
        className="flex items-center justify-center py-2 px-4 w-full bg-transparent text-[#3f5781] dark:text-[#8fa3c7] font-medium rounded-lg hover:bg-[#3f5781]/5 dark:hover:bg-[#3f5781]/20 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-[#3f5781] focus:ring-offset-2 dark:focus:ring-offset-gray-800"
      >
        <Download className="w-4 h-4 mr-2" />
        Download Quote as PDF
      </button>
    </motion.div>
  );
};

export default PricingResult;
