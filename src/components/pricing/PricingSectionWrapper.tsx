"use client";

import { type Dictionary } from "@/lib/dictionary";
import { Section } from "@/components/ui/Section";
import PricingCalculatorClient from "./PricingCalculatorClient";
import PricesSection from "@/components/sections/PricesSection";
import { Info, Mail } from "lucide-react";
import { useState } from "react";
import { Button } from "@/components/ui/Button";

interface PricingSectionWrapperProps {
  dictionary?: Dictionary["prices"];
  locale: string;
}

// This is a server component that safely renders either the Arabic pricing section
// or the client-side pricing calculator to avoid SSR issues
export default function PricingSectionWrapper({
  dictionary,
  locale,
}: PricingSectionWrapperProps) {
  const [showPriceInfo, setShowPriceInfo] = useState(false);

  // Function to create email with package details for Arabic
  const createContactEmailAr = (
    title: string,
    price: string,
    timeframe: string,
    description: string,
    features: string[]
  ) => {
    const subject = encodeURIComponent(`استفسار حول ${title}`);
    const featuresText = features.join("\n- ") || "";
    const body = encodeURIComponent(
      `مرحباً،

أنا مهتم بباقة ${title} (${price}).

تفاصيل الباقة:
- الإطار الزمني: ${timeframe}
- ${description}

الميزات المتضمنة:
- ${featuresText}

يرجى تزويدي بمزيد من المعلومات حول هذه الخدمة.

شكراً لكم!`
    );

    return `mailto:<EMAIL>?subject=${subject}&body=${body}`;
  };

  if (locale === "ar") {
    return (
      <Section id="prices" className="bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-8 text-gray-900 dark:text-white">
            {dictionary?.title || "أسعارنا"}
          </h2>

          <p className="text-center text-gray-600 dark:text-gray-300 mb-12 max-w-3xl mx-auto">
            {dictionary?.description ||
              "يرجى الاتصال بنا للحصول على معلومات حول الأسعار والباقات المخصصة"}
          </p>

          {/* Pricing disclaimer */}
          <div className="mb-8 text-center">
            <p className="text-gray-600 dark:text-gray-300 text-sm italic max-w-3xl mx-auto">
              {dictionary?.pricingDisclaimer ||
                "* الأسعار قد تختلف بناءً على متطلبات المشروع والخدمات الإضافية. اتصل بنا للحصول على عرض أسعار مخصص لاحتياجاتك الخاصة."}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-10">
            {/* MVP Package */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 border border-gray-200 dark:border-gray-700 hover:border-blue-600 dark:hover:border-blue-600 transition-all duration-300">
              <div className="flex justify-between items-center mb-2">
                <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                  تطوير MVP
                </h3>
                <button
                  onClick={() => setShowPriceInfo(!showPriceInfo)}
                  className="text-gray-400 hover:text-blue-400 transition-colors"
                  aria-label="More information"
                >
                  <Info className="h-5 w-5" />
                </button>
              </div>

              {showPriceInfo && (
                <div className="mt-2 p-3 bg-gray-700 dark:bg-gray-700 rounded-lg text-xs text-gray-300 dark:text-gray-300 mb-3 border border-gray-600 dark:border-gray-600">
                  الأسعار قد تختلف بناءً على متطلبات المشروع والخدمات الإضافية.
                  اتصل بنا للحصول على عرض أسعار مخصص.
                </div>
              )}

              <div className="text-sm text-gray-400 dark:text-gray-400 mb-4">
                3-4 أسابيع
              </div>
              <div className="text-2xl font-bold text-blue-400 dark:text-blue-400 mb-4">
                €8,500
              </div>
              <p className="text-gray-300 dark:text-gray-300 mb-6">
                أطلق فكرتك بسرعة مع منتج قابل للتطبيق
              </p>
              <ul className="list-inside space-y-2 text-gray-300 dark:text-gray-300 mb-6">
                <li className="flex items-start">
                  <span className="text-green-400 dark:text-green-400 mr-2">
                    ✓
                  </span>
                  تنفيذ الوظائف الأساسية
                </li>
                <li className="flex items-start">
                  <span className="text-green-400 dark:text-green-400 mr-2">
                    ✓
                  </span>
                  تصميم واجهة مستخدم أساسية
                </li>
                <li className="flex items-start">
                  <span className="text-green-400 dark:text-green-400 mr-2">
                    ✓
                  </span>
                  مصادقة المستخدم
                </li>
                <li className="flex items-start">
                  <span className="text-green-400 dark:text-green-400 mr-2">
                    ✓
                  </span>
                  حل تخزين البيانات
                </li>
              </ul>
              <a
                href={createContactEmailAr(
                  "تطوير MVP",
                  "€8,500",
                  "3-4 أسابيع",
                  "أطلق فكرتك بسرعة مع منتج قابل للتطبيق",
                  [
                    "تنفيذ الوظائف الأساسية",
                    "تصميم واجهة مستخدم أساسية",
                    "مصادقة المستخدم",
                    "حل تخزين البيانات",
                  ]
                )}
                className="block w-full"
              >
                <Button
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white transition-all duration-300"
                  size="lg"
                >
                  <span className="flex items-center gap-2">
                    <Mail className="w-5 h-5" />
                    <span>اتصل بنا</span>
                  </span>
                </Button>
              </a>
            </div>

            {/* Landing Page Package */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 border border-gray-200 dark:border-gray-700 hover:border-blue-600 dark:hover:border-blue-600 transition-all duration-300">
              <div className="flex justify-between items-center mb-2">
                <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                  تطوير الصفحات المقصودة
                </h3>
                <button
                  onClick={() => setShowPriceInfo(!showPriceInfo)}
                  className="text-gray-400 hover:text-blue-400 transition-colors"
                  aria-label="More information"
                >
                  <Info className="h-5 w-5" />
                </button>
              </div>

              {showPriceInfo && (
                <div className="mt-2 p-3 bg-gray-700 dark:bg-gray-700 rounded-lg text-xs text-gray-300 dark:text-gray-300 mb-3 border border-gray-600 dark:border-gray-600">
                  الأسعار قد تختلف بناءً على متطلبات المشروع والخدمات الإضافية.
                  اتصل بنا للحصول على عرض أسعار مخصص.
                </div>
              )}

              <div className="text-sm text-gray-400 dark:text-gray-400 mb-4">
                2-4 أسابيع
              </div>
              <div className="text-2xl font-bold text-blue-400 dark:text-blue-400 mb-4">
                €3,000
              </div>
              <p className="text-gray-300 dark:text-gray-300 mb-6">
                صفحات هبوط متوافقة مع معايير WCAG 2.0 باستخدام أحدث تقنيات الويب
              </p>
              <ul className="list-inside space-y-2 text-gray-300 dark:text-gray-300 mb-6">
                <li className="flex items-start">
                  <span className="text-green-400 dark:text-green-400 mr-2">
                    ✓
                  </span>
                  توافق مع معايير WCAG 2.0 AA
                </li>
                <li className="flex items-start">
                  <span className="text-green-400 dark:text-green-400 mr-2">
                    ✓
                  </span>
                  تصميم شامل لجميع المستخدمين
                </li>
                <li className="flex items-start">
                  <span className="text-green-400 dark:text-green-400 mr-2">
                    ✓
                  </span>
                  توافق مع قارئات الشاشة
                </li>
                <li className="flex items-start">
                  <span className="text-green-400 dark:text-green-400 mr-2">
                    ✓
                  </span>
                  تحسين لمحركات البحث
                </li>
              </ul>
              <a
                href={createContactEmailAr(
                  "تطوير الصفحات المقصودة",
                  "€3,000",
                  "2-4 أسابيع",
                  "صفحات هبوط متوافقة مع معايير WCAG 2.0 باستخدام أحدث تقنيات الويب",
                  [
                    "توافق مع معايير WCAG 2.0 AA",
                    "تصميم شامل لجميع المستخدمين",
                    "توافق مع قارئات الشاشة",
                    "تحسين لمحركات البحث",
                  ]
                )}
                className="block w-full"
              >
                <Button
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white transition-all duration-300"
                  size="lg"
                >
                  <span className="flex items-center gap-2">
                    <Mail className="w-5 h-5" />
                    <span>اتصل بنا</span>
                  </span>
                </Button>
              </a>
            </div>

            {/* Consulting Package */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 border border-gray-200 dark:border-gray-700 hover:border-blue-600 dark:hover:border-blue-600 transition-all duration-300">
              <div className="flex justify-between items-center mb-2">
                <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                  الاستشارات التقنية
                </h3>
                <button
                  onClick={() => setShowPriceInfo(!showPriceInfo)}
                  className="text-gray-400 hover:text-blue-400 transition-colors"
                  aria-label="More information"
                >
                  <Info className="h-5 w-5" />
                </button>
              </div>

              {showPriceInfo && (
                <div className="mt-2 p-3 bg-gray-700 dark:bg-gray-700 rounded-lg text-xs text-gray-300 dark:text-gray-300 mb-3 border border-gray-600 dark:border-gray-600">
                  الأسعار قد تختلف بناءً على متطلبات المشروع والخدمات الإضافية.
                  اتصل بنا للحصول على عرض أسعار مخصص.
                </div>
              )}

              <div className="text-sm text-gray-400 dark:text-gray-400 mb-4">
                مستمر
              </div>
              <div className="text-2xl font-bold text-blue-400 dark:text-blue-400 mb-4">
                €85/ساعة
              </div>
              <p className="text-gray-300 dark:text-gray-300 mb-6">
                إرشادات خبيرة لقراراتك التقنية
              </p>
              <ul className="list-inside space-y-2 text-gray-300 dark:text-gray-300 mb-6">
                <li className="flex items-start">
                  <span className="text-green-400 dark:text-green-400 mr-2">
                    ✓
                  </span>
                  توصيات مجموعة التكنولوجيا
                </li>
                <li className="flex items-start">
                  <span className="text-green-400 dark:text-green-400 mr-2">
                    ✓
                  </span>
                  مراجعات الكود
                </li>
                <li className="flex items-start">
                  <span className="text-green-400 dark:text-green-400 mr-2">
                    ✓
                  </span>
                  تحسين الأداء
                </li>
                <li className="flex items-start">
                  <span className="text-green-400 dark:text-green-400 mr-2">
                    ✓
                  </span>
                  تكامل بوابات الدفع الإماراتية
                </li>
              </ul>
              <a
                href={createContactEmailAr(
                  "الاستشارات التقنية",
                  "€85/ساعة",
                  "مستمر",
                  "إرشادات خبيرة لقراراتك التقنية",
                  [
                    "توصيات مجموعة التكنولوجيا",
                    "مراجعات الكود",
                    "تحسين الأداء",
                    "تكامل بوابات الدفع الإماراتية",
                  ]
                )}
                className="block w-full"
              >
                <Button
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white transition-all duration-300"
                  size="lg"
                >
                  <span className="flex items-center gap-2">
                    <Mail className="w-5 h-5" />
                    <span>اتصل بنا</span>
                  </span>
                </Button>
              </a>
            </div>
          </div>

          {/* Promotion banner */}
          <div className="mb-10 p-6 bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl max-w-3xl mx-auto">
            <h3 className="text-lg font-semibold text-indigo-600 dark:text-indigo-300 mb-2">
              اجمع الخدمات ووفر 15%
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              احصل على خصم 15% عند طلب أي خدمتين أو أكثر من خدماتنا
            </p>
            <a
              href="mailto:<EMAIL>?subject=%D8%A7%D8%B3%D8%AA%D9%81%D8%B3%D8%A7%D8%B1%20%D8%AD%D9%88%D9%84%20%D8%AD%D8%B2%D9%85%D8%A9%20%D8%A7%D9%84%D8%AE%D8%AF%D9%85%D8%A7%D8%AA&body=%D9%85%D8%B1%D8%AD%D8%A8%D8%A7%D9%8B%D8%8C%0A%0A%D8%A3%D9%86%D8%A7%20%D9%85%D9%87%D8%AA%D9%85%20%D8%A8%D8%A7%D9%84%D8%AC%D9%85%D8%B9%20%D8%A8%D9%8A%D9%86%20%D8%B9%D8%AF%D8%A9%20%D8%AE%D8%AF%D9%85%D8%A7%D8%AA%20%D9%84%D9%84%D8%AD%D8%B5%D9%88%D9%84%20%D8%B9%D9%84%D9%89%20%D8%AE%D8%B5%D9%85%2015%25.%0A%0A%D9%8A%D8%B1%D8%AC%D9%89%20%D8%AA%D8%B2%D9%88%D9%8A%D8%AF%D9%8A%20%D8%A8%D9%85%D8%B2%D9%8A%D8%AF%20%D9%85%D9%86%20%D8%A7%D9%84%D9%85%D8%B9%D9%84%D9%88%D9%85%D8%A7%D8%AA.%0A%0A%D8%B4%D9%83%D8%B1%D8%A7%D9%8B%20%D9%84%D9%83%D9%85!"
              className="inline-block"
            >
              <Button
                className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white shadow-lg transition-all duration-300 rounded-lg"
                size="lg"
              >
                <span className="flex items-center gap-2">
                  <Mail className="w-5 h-5" />
                  <span>ناقش مشروعك معنا</span>
                </span>
              </Button>
            </a>
          </div>

          <div className="flex justify-center">
            <a
              href="mailto:<EMAIL>?subject=%D8%A7%D8%B3%D8%AA%D9%81%D8%B3%D8%A7%D8%B1%20%D8%AD%D9%88%D9%84%20%D8%A7%D9%84%D8%AE%D8%AF%D9%85%D8%A7%D8%AA&body=%D9%85%D8%B1%D8%AD%D8%A8%D8%A7%D9%8B%D8%8C%0A%0A%D8%A3%D9%86%D8%A7%20%D9%85%D9%87%D8%AA%D9%85%20%D8%A8%D8%A7%D9%84%D8%AD%D8%B5%D9%88%D9%84%20%D8%B9%D9%84%D9%89%20%D9%85%D8%B2%D9%8A%D8%AF%20%D9%85%D9%86%20%D8%A7%D9%84%D9%85%D8%B9%D9%84%D9%88%D9%85%D8%A7%D8%AA%20%D8%AD%D9%88%D9%84%20%D8%AE%D8%AF%D9%85%D8%A7%D8%AA%D9%83%D9%85.%0A%0A%D9%8A%D8%B1%D8%AC%D9%89%20%D8%A7%D9%84%D8%AA%D9%88%D8%A7%D8%B5%D9%84%20%D9%85%D8%B9%D9%8A%20%D9%84%D9%85%D9%86%D8%A7%D9%82%D8%B4%D8%A9%20%D8%A7%D8%AD%D8%AA%D9%8A%D8%A7%D8%AC%D8%A7%D8%AA%D9%8A.%0A%0A%D8%B4%D9%83%D8%B1%D8%A7%D9%8B%20%D9%84%D9%83%D9%85!"
              className="px-8 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg shadow-lg flex items-center gap-2 transition-all duration-300"
            >
              <Mail className="w-5 h-5" />
              <span>{dictionary?.contactButton || "اتصل بنا"}</span>
            </a>
          </div>
        </div>
      </Section>
    );
  }

  // For all other locales, use the PricesSection which includes pricing packages and calculator
  return <PricesSection dictionary={dictionary} />;
}
