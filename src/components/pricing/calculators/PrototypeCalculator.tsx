"use client";

import { useState } from "react";
import { Lightbulb, ChevronRight, Clock, Zap, Info, Check } from "lucide-react";
import PricingSlider from "@/components/pricing/ui/PricingSlider";
// Removed FeatureSelector import if it existed, adding InteractiveFeatureSelector
import InteractiveFeatureSelector from "@/components/pricing/ui/InteractiveFeatureSelector";

interface PrototypeCalculatorProps {
  onSubmit: (data: Record<string, any>) => void;
}

type Complexity = "simple" | "medium" | "complex";
type Timeline = "normal" | "fast" | "urgent";

const PrototypeCalculator = ({ onSubmit }: PrototypeCalculatorProps) => {
  const [formData, setFormData] = useState({
    complexity: "medium" as Complexity,
    userRoles: 2,
    integrations: ["auth"],
    selectedFeatures: ["ui_design", "navigation"] as string[],
    customFeatures: "",
    needsReview: false,
    timeline: "normal" as Timeline,
  });

  const handleChange = (field: string, value: any) => {
    setFormData({
      ...formData,
      [field]: value
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  // Complexity options
  const complexityLevels = [
    { id: "simple", label: "Simple", description: "Basic user flows, minimal screens", factor: 0.8 },
    { id: "medium", label: "Medium", description: "Multiple user flows, standard screens", factor: 1.0 },
    { id: "complex", label: "Complex", description: "Advanced UI, many screens", factor: 1.4 }
  ];

  // Timeline options - with 40% faster time-to-market
  const timelines = [
    { id: "normal", label: "Standard (1-2 weeks)", factor: 1.0, description: "Regular development pace" },
    { id: "fast", label: "Accelerated (5-7 days)", factor: 1.3, description: "30% faster delivery" },
    { id: "urgent", label: "Priority (3-4 days)", factor: 1.6, description: "Dedicated team, fastest delivery" }
  ];
  
  // Standard prototype features
  const prototypeFeatures = [
    // UI Design features
    { id: "ui_design", name: "UI Design", category: "design", description: "Visual design of screens", required: true },
    { id: "navigation", name: "Navigation Flow", category: "design", description: "Screen transitions", required: true },
    { id: "animations", name: "Basic Animations", category: "design", description: "Simple UI animations" },
    { id: "dark_mode", name: "Dark Mode", category: "design", description: "Dark theme support" },
    
    // Mockup features
    { id: "login_mockup", name: "Login Screen Mockup", category: "mockup", description: "Non-functional login UI" },
    { id: "profile_mockup", name: "Profile Screen Mockup", category: "mockup", description: "User profile UI" },
    { id: "dashboard_mockup", name: "Dashboard Mockup", category: "mockup", description: "Main screen UI" },
    { id: "settings_mockup", name: "Settings Screen Mockup", category: "mockup", description: "App settings UI" },
    
    // Interaction features
    { id: "clickable", name: "Clickable Prototype", category: "interaction", description: "Interactive elements" },
    { id: "form_validation", name: "Form Validation", category: "interaction", description: "Input field validation" },
    { id: "transitions", name: "Screen Transitions", category: "interaction", description: "Animated transitions" },
    { id: "gestures", name: "Gesture Interactions", category: "interaction", description: "Swipe, pinch, etc." },
    
    // Presentation features
    { id: "demo_mode", name: "Demo Mode", category: "presentation", description: "Guided demo flow" },
    { id: "shareable", name: "Shareable Link", category: "presentation", description: "Share prototype online" },
    { id: "feedback", name: "Feedback Collection", category: "presentation", description: "Gather user comments" }
  ];
  
  // Integration options
  const integrationOptions = [
    { id: "auth", name: "Auth UI Mockup", description: "User login and registration screens", required: true },
    { id: "payment", name: "Payment UI Mockup", description: "Payment form screens" },
    { id: "api", name: "API Data Mockup", description: "Simulated data from APIs" },
    { id: "analytics", name: "Analytics Dashboard Mockup", description: "Data visualization UI" },
    { id: "location", name: "Map UI Mockup", description: "Maps and location UI" },
    { id: "storage", name: "File Upload UI Mockup", description: "File management UI" }
  ];

  return (
    <form onSubmit={handleSubmit} className="w-full">
      <div className="mb-6">
        <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-1">
          Rapid Prototype Calculator
        </h3>
        <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">
          <span className="bg-amber-100 dark:bg-amber-900/30 text-amber-800 dark:text-amber-200 px-2 py-0.5 rounded text-xs font-medium mr-2">
            UI Only
          </span>
          Configure your prototype requirements for a visual and navigational
          prototype (not app store ready, no backend logic)
        </p>
      </div>

      {/* Timeline Selection - in a row */}
      <div className="mb-6 w-full">
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Development Timeline
        </label>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 w-full">
          {timelines.map((timeline) => (
            <div
              key={timeline.id}
              className={`p-3 rounded-lg border cursor-pointer transition-all duration-300 ${
                formData.timeline === timeline.id
                  ? "border-[#3f5781] bg-[#3f5781]/5 dark:bg-[#3f5781]/20"
                  : "border-gray-200 dark:border-gray-700 hover:border-[#3f5781]/50 dark:hover:border-[#3f5781]/50"
              }`}
              onClick={() => handleChange("timeline", timeline.id)}
            >
              <div className="flex items-center mb-1">
                <div
                  className={`w-4 h-4 rounded-full flex items-center justify-center flex-shrink-0 ${
                    formData.timeline === timeline.id
                      ? "bg-[#3f5781] text-white"
                      : "border-2 border-gray-300 dark:border-gray-600"
                  }`}
                >
                  {formData.timeline === timeline.id && (
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                  )}
                </div>
                <div className="ml-2 font-medium text-gray-800 dark:text-gray-200">
                  {timeline.label}
                </div>
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400 pl-6">
                {timeline.description}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Complexity Selection */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Prototype Complexity
        </label>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
          {complexityLevels.map((level) => (
            <div
              key={level.id}
              className={`p-3 rounded-lg border cursor-pointer transition-all duration-300 ${
                formData.complexity === level.id
                  ? "border-[#3f5781] bg-[#3f5781]/5 dark:bg-[#3f5781]/20"
                  : "border-gray-200 dark:border-gray-700 hover:border-[#3f5781]/50 dark:hover:border-[#3f5781]/50"
              }`}
              onClick={() => handleChange("complexity", level.id)}
            >
              <div className="flex items-center mb-1">
                <div
                  className={`w-4 h-4 rounded-full flex items-center justify-center flex-shrink-0 ${
                    formData.complexity === level.id
                      ? "bg-[#3f5781] text-white"
                      : "border-2 border-gray-300 dark:border-gray-600"
                  }`}
                >
                  {formData.complexity === level.id && (
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                  )}
                </div>
                <div className="ml-2 font-medium text-gray-800 dark:text-gray-200">
                  {level.label}
                </div>
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400 pl-6">
                {level.description}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* User Roles Slider */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-2">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Number of User Roles
          </label>
          <span className="text-sm font-semibold text-[#3f5781] dark:text-[#8fa3c7] bg-[#3f5781]/10 dark:bg-[#3f5781]/30 px-2 py-0.5 rounded-full">
            {formData.userRoles}
          </span>
        </div>
        <PricingSlider
          min={1}
          max={5}
          step={1}
          value={formData.userRoles}
          onChange={(value) => handleChange("userRoles", value)}
        />
        <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
          <span>Basic (1)</span>
          <span>Complex (5+)</span>
        </div>
        <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
          <span className="italic">Examples:</span> User, Admin, Editor, Viewer,
          Moderator
        </p>
      </div>

      {/* Prototype Features & Integrations - Responsive */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Select Prototype Features & Integrations
        </label>

        {/* Info Text (Common for both views) */}
        <div className="bg-gray-50 dark:bg-gray-800/50 p-3 rounded-lg border border-gray-200 dark:border-gray-700 mb-3 text-xs text-gray-600 dark:text-gray-400 flex items-start">
          <Info className="w-4 h-4 mr-1.5 flex-shrink-0 mt-0.5" />
          <span
            dangerouslySetInnerHTML={{
              __html:
                "Select the UI features and integrations you need for your <strong>visual prototype</strong> (no backend logic). Required items cannot be deselected.",
            }}
          />
        </div>

        {/* --- UI Mockup Integrations --- */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            UI Mockup Integrations
          </label>
          {/* Mobile View: Interactive Tag Selector */}
          <div className="block md:hidden">
            <InteractiveFeatureSelector
              availableFeatures={integrationOptions.filter((f) => !f.required)}
              selectedFeatures={formData.integrations}
              onChange={(newSelectedIds) => {
                const requiredIds = integrationOptions
                  .filter((f) => f.required)
                  .map((f) => f.id);
                const combinedIds = Array.from(
                  new Set([...requiredIds, ...newSelectedIds])
                );
                handleChange("integrations", combinedIds);
              }}
              placeholder="Tap to add integrations..."
              label=""
              infoText=""
            />
          </div>
          {/* Desktop View: Checkbox Grid */}
          <div className="hidden md:block bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
            <div className="grid grid-cols-2 gap-x-4 gap-y-3">
              {integrationOptions.map((integration) => (
                <div
                  key={`integration-${integration.id}-desktop`}
                  className="flex items-start"
                >
                  <input
                    type="checkbox"
                    id={`integration-${integration.id}-desktop`}
                    checked={formData.integrations.includes(integration.id)}
                    disabled={integration.required}
                    onChange={(e) => {
                      const updatedIntegrations = e.target.checked
                        ? [...formData.integrations, integration.id]
                        : formData.integrations.filter(
                            (i) => i !== integration.id
                          );
                      const requiredIds = integrationOptions
                        .filter((f) => f.required)
                        .map((f) => f.id);
                      const finalIntegrations = Array.from(
                        new Set([...requiredIds, ...updatedIntegrations])
                      );
                      handleChange("integrations", finalIntegrations);
                    }}
                    className={`h-4 w-4 rounded border-gray-300 dark:border-gray-600 text-[#3f5781] focus:ring-[#3f5781] mt-0.5 ${
                      integration.required
                        ? "cursor-not-allowed opacity-70"
                        : "cursor-pointer"
                    }`}
                  />
                  <label
                    htmlFor={`integration-${integration.id}-desktop`}
                    className={`ml-2 block text-sm ${
                      integration.required
                        ? "cursor-not-allowed"
                        : "cursor-pointer"
                    }`}
                  >
                    <span className="font-medium text-gray-800 dark:text-gray-200">
                      {integration.name}
                    </span>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {integration.description}
                    </p>
                    {integration.required && (
                      <span className="text-xs text-[#3f5781] dark:text-[#8fa3c7] font-medium">
                        {" "}
                        (Required)
                      </span>
                    )}
                  </label>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* --- UI Features --- */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            UI Features
          </label>
          {/* Mobile View: Interactive Tag Selector */}
          <div className="block md:hidden">
            <InteractiveFeatureSelector
              availableFeatures={prototypeFeatures.filter((f) => !f.required)}
              selectedFeatures={formData.selectedFeatures}
              onChange={(newSelectedIds) => {
                const requiredIds = prototypeFeatures
                  .filter((f) => f.required)
                  .map((f) => f.id);
                const combinedIds = Array.from(
                  new Set([...requiredIds, ...newSelectedIds])
                );
                handleChange("selectedFeatures", combinedIds);
              }}
              placeholder="Tap to add UI features..."
              label=""
              infoText=""
            />
          </div>
          {/* Desktop View: Checkbox Grid */}
          <div className="hidden md:block bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
            <div className="grid grid-cols-2 gap-x-4 gap-y-3">
              {prototypeFeatures.map((feature) => (
                <div key={`${feature.id}-desktop`} className="flex items-start">
                  <input
                    type="checkbox"
                    id={`${feature.id}-desktop`}
                    checked={formData.selectedFeatures.includes(feature.id)}
                    disabled={feature.required}
                    onChange={(e) => {
                      const updatedFeatures = e.target.checked
                        ? [...formData.selectedFeatures, feature.id]
                        : formData.selectedFeatures.filter(
                            (f) => f !== feature.id
                          );
                      const requiredIds = prototypeFeatures
                        .filter((f) => f.required)
                        .map((f) => f.id);
                      const finalFeatures = Array.from(
                        new Set([...requiredIds, ...updatedFeatures])
                      );
                      handleChange("selectedFeatures", finalFeatures);
                    }}
                    className={`h-4 w-4 rounded border-gray-300 dark:border-gray-600 text-[#3f5781] focus:ring-[#3f5781] mt-0.5 ${
                      feature.required
                        ? "cursor-not-allowed opacity-70"
                        : "cursor-pointer"
                    }`}
                  />
                  <label
                    htmlFor={`${feature.id}-desktop`}
                    className={`ml-2 block text-sm ${
                      feature.required ? "cursor-not-allowed" : "cursor-pointer"
                    }`}
                  >
                    <span className="text-gray-800 dark:text-gray-200 font-medium">
                      {feature.name}
                      {feature.required && (
                        <span className="ml-1 text-xs text-[#3f5781] dark:text-[#8fa3c7]">
                          (Required)
                        </span>
                      )}
                    </span>
                    <span className="text-xs text-gray-500 dark:text-gray-400 block">
                      {feature.description}
                    </span>
                  </label>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Custom Features */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Custom Features (Optional)
        </label>
        <textarea
          value={formData.customFeatures}
          onChange={(e) => {
            const hasContent = e.target.value.trim().length > 0;
            handleChange("customFeatures", e.target.value);
            handleChange("needsReview", hasContent);
          }}
          placeholder="Describe any custom UI features you need that aren't listed above..."
          className="w-full rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 p-3 text-sm text-gray-700 dark:text-gray-300 focus:border-[#3f5781] focus:ring-[#3f5781] transition-colors duration-200"
          rows={3}
        />
        {formData.needsReview && (
          <div className="mt-2 text-xs text-amber-600 dark:text-amber-400 flex items-center">
            <Info className="w-4 h-4 mr-1" />
            <span>Custom features require review before final pricing</span>
          </div>
        )}
      </div>

      <div className="flex justify-between items-center mt-8">
        <div className="text-sm text-gray-600 dark:text-gray-400 flex items-center">
          <Clock className="w-4 h-4 mr-1 text-amber-500" />
          <span>UI prototype only - not app store ready</span>
        </div>
        <button
          type="submit"
          className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg flex items-center transition-all duration-300 font-medium"
        >
          Continue to Quote
          <ChevronRight className="w-4 h-4 ml-1" />
        </button>
      </div>
    </form>
  );
};

export default PrototypeCalculator;
