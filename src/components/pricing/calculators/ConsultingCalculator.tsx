"use client";

import { useState } from "react";
import { ChevronRight, Code, Users, Calendar } from "lucide-react";
import PricingSlider from "@/components/pricing/ui/PricingSlider";
import FeatureSelector from "@/components/pricing/ui/FeatureSelector"; // Keep original for desktop
import InteractiveFeatureSelector from "@/components/pricing/ui/InteractiveFeatureSelector"; // Add new for mobile

interface ConsultingCalculatorProps {
  onSubmit: (data: Record<string, any>) => void;
}

type ProjectPhase = "planning" | "development" | "maintenance" | "optimization";

const ConsultingCalculator = ({ onSubmit }: ConsultingCalculatorProps) => {
  const [formData, setFormData] = useState({
    projectPhase: "development" as ProjectPhase,
    teamSize: 3,
    expertiseNeeded: ["code-review", "architecture"],
    duration: 4, // weeks
  });

  const handleChange = (field: string, value: any) => {
    setFormData({
      ...formData,
      [field]: value,
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  // Project phases
  const projectPhases = [
    { id: "planning", label: "Planning & Strategy", factor: 0.9 },
    { id: "development", label: "Active Development", factor: 1.0 },
    { id: "maintenance", label: "Maintenance & Support", factor: 0.8 },
    { id: "optimization", label: "Performance Optimization", factor: 1.1 },
  ];

  // Expertise options
  const expertiseOptions = [
    {
      id: "code-review",
      name: "Code Reviews",
      description: "Quality assessment and improvement",
    },
    {
      id: "architecture",
      name: "Architecture Design",
      description: "System design and planning",
    },
    {
      id: "tech-selection",
      name: "Technology Selection",
      description: "Choosing the right tech stack",
    },
    {
      id: "performance",
      name: "Performance Optimization",
      description: "Speed and efficiency improvements",
    },
    {
      id: "security",
      name: "Security Audits",
      description: "Identifying and fixing vulnerabilities",
    },
    {
      id: "scaling",
      name: "Scaling Strategy",
      description: "Planning for growth",
    },
    {
      id: "devops",
      name: "DevOps & CI/CD",
      description: "Automated testing and deployment",
    },
    {
      id: "legacy",
      name: "Legacy System Migration",
      description: "Modernizing older systems",
    },
  ];

  return (
    <form onSubmit={handleSubmit} className="w-full">
      <div className="mb-6">
        <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-1">
          Technical Consulting Calculator
        </h3>
        <p className="text-gray-600 dark:text-gray-300 text-sm">
          Configure your consulting needs to get a custom quote
        </p>
      </div>

      {/* Project Phase Selection */}
      <div className="mb-6 w-full">
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Project Phase
        </label>
        <div className="grid grid-cols-2 gap-2 w-full">
          {projectPhases.map((phase) => (
            <button
              key={phase.id}
              type="button"
              onClick={() => handleChange("projectPhase", phase.id)}
              className={`py-2 px-3 text-sm rounded-lg transition-all duration-300 flex items-center justify-center ${
                formData.projectPhase === phase.id
                  ? "bg-blue-600 text-white shadow-sm"
                  : "bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
              }`}
            >
              {phase.label}
            </button>
          ))}
        </div>
      </div>

      {/* Team Size Slider */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-2">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Team Size
          </label>
          <span className="text-sm font-semibold text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/30 px-2 py-0.5 rounded-full">
            {formData.teamSize}{" "}
            {formData.teamSize === 1 ? "Developer" : "Developers"}
          </span>
        </div>
        <PricingSlider
          min={1}
          max={10}
          step={1}
          value={formData.teamSize}
          onChange={(value) => handleChange("teamSize", value)}
        />
        <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
          <span>Solo (1)</span>
          <span>Large Team (10+)</span>
        </div>
      </div>

      {/* Expertise Needed - Responsive */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Expertise Needed
        </label>

        {/* Info Text (Optional, add if needed) */}
        {/*
        <div className="bg-gray-50 dark:bg-gray-800/50 p-3 rounded-lg border border-gray-200 dark:border-gray-700 mb-3 text-xs text-gray-600 dark:text-gray-400 flex items-start">
           <Info className="w-4 h-4 mr-1.5 flex-shrink-0 mt-0.5" />
           <span>Select the areas where you require technical expertise.</span>
         </div>
         */}

        {/* Mobile View: Interactive Tag Selector */}
        <div className="block md:hidden">
          <InteractiveFeatureSelector
            availableFeatures={expertiseOptions} // Assuming expertiseOptions has {id, name, description?} structure
            selectedFeatures={formData.expertiseNeeded}
            onChange={(newSelectedIds) =>
              handleChange("expertiseNeeded", newSelectedIds)
            }
            placeholder="Tap to add expertise areas..."
            label=""
            infoText=""
          />
        </div>

        {/* Desktop View: Original FeatureSelector (or Checkbox Grid if FeatureSelector is simple) */}
        {/* Assuming FeatureSelector renders a checkbox grid or similar */}
        <div className="hidden md:block">
          <FeatureSelector
            features={expertiseOptions}
            selectedFeatures={formData.expertiseNeeded}
            onChange={(selected) => handleChange("expertiseNeeded", selected)}
          />
        </div>
      </div>

      {/* Duration Slider */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-2">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Estimated Duration
          </label>
          <span className="text-sm font-semibold text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/30 px-2 py-0.5 rounded-full">
            {formData.duration} {formData.duration === 1 ? "Week" : "Weeks"}
          </span>
        </div>
        <PricingSlider
          min={1}
          max={12}
          step={1}
          value={formData.duration}
          onChange={(value) => handleChange("duration", value)}
        />
        <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
          <span>Short-term (1 week)</span>
          <span>Long-term (12+ weeks)</span>
        </div>
      </div>

      {/* Consulting Highlight */}
      <div className="mb-6 p-4 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
        <div className="flex items-center">
          <Code className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
          <h4 className="font-medium text-blue-600 dark:text-blue-400">
            Expert Technical Consultation
          </h4>
        </div>
        <p className="text-sm text-gray-700 dark:text-gray-300 mt-2">
          Our technical consulting services leverage decades of industry
          experience across a wide range of technologies and industries. We
          provide both strategic guidance and hands-on implementation support.
        </p>
        <div className="mt-3 flex items-center gap-4">
          <div className="flex items-center text-sm">
            <Users className="w-4 h-4 text-green-600 mr-1" />
            <span className="text-green-600 font-medium">
              CTO-level expertise
            </span>
          </div>
          <div className="flex items-center text-sm">
            <Calendar className="w-4 h-4 text-green-600 mr-1" />
            <span className="text-green-600 font-medium">
              Flexible scheduling
            </span>
          </div>
        </div>
      </div>

      <div className="pt-2">
        <button
          type="submit"
          className="w-full flex items-center justify-center py-3 px-4 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-all duration-300 shadow-sm hover:shadow focus:outline-none focus:ring-2 focus:ring-blue-600 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
        >
          Continue <ChevronRight className="ml-2 h-4 w-4" />
        </button>
      </div>
    </form>
  );
};

export default ConsultingCalculator;
