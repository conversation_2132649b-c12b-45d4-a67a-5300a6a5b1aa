"use client";

import { useState } from "react";
import { Home, ChevronRight, BarChart, Globe, Info, Check } from "lucide-react";
import PricingSlider from "@/components/pricing/ui/PricingSlider";
// Removed FeatureSelector import if it existed, adding InteractiveFeatureSelector
import InteractiveFeatureSelector from "@/components/pricing/ui/InteractiveFeatureSelector";

interface HomepageCalculatorProps {
  onSubmit: (data: Record<string, any>) => void;
}

const HomepageCalculator = ({ onSubmit }: HomepageCalculatorProps) => {
  const [formData, setFormData] = useState({
    pageCount: 5,
    features: ["contact", "responsive"],
    selectedFeatures: ["contact", "responsive"] as string[],
    seo: true,
    multilingual: false,
    customFeatures: "",
    needsReview: false,
    timeline: "normal" as "normal" | "fast" | "urgent"
  });

  const handleChange = (field: string, value: any) => {
    setFormData({
      ...formData,
      [field]: value
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  // Feature options
  const featureOptions = [
    // Essential features
    { id: "responsive", name: "Responsive Design", category: "essential", description: "Optimized for all devices", required: true },
    { id: "contact", name: "Contact Form", category: "essential", description: "Allow visitors to contact you", required: true },
    
    // Content features
    { id: "blog", name: "Blog Section", category: "content", description: "Regular content updates" },
    { id: "gallery", name: "Image Gallery", category: "content", description: "Showcase your work" },
    { id: "portfolio", name: "Portfolio Showcase", category: "content", description: "Display your projects" },
    { id: "testimonials", name: "Testimonials Section", category: "content", description: "Show client feedback" },
    
    // Marketing features
    { id: "analytics", name: "Analytics Integration", category: "marketing", description: "Track visitor behavior" },
    { id: "social", name: "Social Media Integration", category: "marketing", description: "Connect with your social profiles" },
    { id: "newsletter", name: "Newsletter Signup", category: "marketing", description: "Collect visitor emails" },
    { id: "seo_advanced", name: "Advanced SEO Package", category: "marketing", description: "Enhanced search optimization" },
    
    // Business features
    { id: "payment", name: "Payment Processing", category: "business", description: "Accept online payments" },
    { id: "booking", name: "Booking/Appointment System", category: "business", description: "Schedule appointments online" },
    { id: "chat", name: "Live Chat", category: "business", description: "Real-time visitor support" },
    { id: "members", name: "Membership Area", category: "business", description: "Restricted content access" }
  ];
  
  // Timeline options
  const timelines = [
    { id: "normal", label: "Standard (2-3 weeks)", factor: 1.0, description: "Regular development pace" },
    { id: "fast", label: "Accelerated (1-2 weeks)", factor: 1.3, description: "30% faster delivery" },
    { id: "urgent", label: "Priority (3-5 days)", factor: 1.6, description: "Dedicated team, fastest delivery" }
  ];

  return (
    <form onSubmit={handleSubmit} className="w-full">
      <div className="mb-6">
        <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-1">
          Homepage & Landing Page Calculator
        </h3>
        <p className="text-gray-600 dark:text-gray-300 text-sm">
          Configure your website requirements to get a custom quote
        </p>
      </div>

      {/* Page Count Slider */}
      <div className="mb-6 w-full">
        <div className="flex justify-between items-center mb-2">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Number of Pages
          </label>
          <span className="text-sm font-semibold text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-600/20 px-2 py-0.5 rounded-full">
            {formData.pageCount}
          </span>
        </div>
        <PricingSlider
          min={1}
          max={15}
          step={1}
          value={formData.pageCount}
          onChange={(value) => handleChange("pageCount", value)}
        />
        <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
          <span>Landing Page (1)</span>
          <span>Full Website (15+)</span>
        </div>
        <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
          <span className="italic">Example pages:</span> Home, About, Services,
          Portfolio, Contact, Blog, etc.
        </p>
      </div>

      {/* Timeline Selection - in a row */}
      <div className="mb-6 w-full">
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Development Timeline
        </label>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 w-full">
          {timelines.map((timeline) => (
            <div
              key={timeline.id}
              className={`p-3 rounded-lg border cursor-pointer transition-all duration-300 ${
                formData.timeline === timeline.id
                  ? "border-blue-600 bg-blue-50 dark:bg-blue-600/20"
                  : "border-gray-200 dark:border-gray-700 hover:border-blue-500/50"
              }`}
              onClick={() => handleChange("timeline", timeline.id)}
            >
              <div className="flex items-center mb-1">
                <div
                  className={`w-4 h-4 rounded-full flex items-center justify-center flex-shrink-0 ${
                    formData.timeline === timeline.id
                      ? "bg-blue-600 text-white"
                      : "border-2 border-gray-300 dark:border-gray-600"
                  }`}
                >
                  {formData.timeline === timeline.id && (
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                  )}
                </div>
                <div className="ml-2 font-medium text-gray-800 dark:text-gray-200">
                  {timeline.label}
                </div>
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400 pl-6">
                {timeline.description}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Feature Selection - Responsive */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Website Features
        </label>

        {/* Info Text (Common for both views) */}
        <div className="bg-gray-50 dark:bg-gray-800/50 p-3 rounded-lg border border-gray-200 dark:border-gray-700 mb-3 text-xs text-gray-600 dark:text-gray-400 flex items-start">
          <Info className="w-4 h-4 mr-1.5 flex-shrink-0 mt-0.5" />
          <span
            dangerouslySetInnerHTML={{
              __html:
                "Select the features you need for your <strong>responsive website</strong>. Required features cannot be deselected.",
            }}
          />
        </div>

        {/* Mobile View: Interactive Tag Selector */}
        <div className="block md:hidden">
          <InteractiveFeatureSelector
            availableFeatures={featureOptions.filter((f) => !f.required)} // Only show non-required in dropdown
            selectedFeatures={formData.selectedFeatures}
            onChange={(newSelectedIds) => {
              // Ensure required features are always included
              const requiredIds = featureOptions
                .filter((f) => f.required)
                .map((f) => f.id);
              const combinedIds = Array.from(
                new Set([...requiredIds, ...newSelectedIds])
              );
              handleChange("selectedFeatures", combinedIds);
              handleChange("features", combinedIds); // Keep both in sync
            }}
            placeholder="Tap to add features..."
            label=""
            infoText=""
          />
        </div>

        {/* Desktop View: Checkbox Grid */}
        <div className="hidden md:block bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="grid grid-cols-2 gap-x-4 gap-y-3">
            {featureOptions.map((feature) => (
              <div key={`${feature.id}-desktop`} className="flex items-start">
                <input
                  type="checkbox"
                  id={`${feature.id}-desktop`}
                  checked={formData.selectedFeatures.includes(feature.id)}
                  disabled={feature.required}
                  onChange={(e) => {
                    const updatedFeatures = e.target.checked
                      ? [...formData.selectedFeatures, feature.id]
                      : formData.selectedFeatures.filter(
                          (f) => f !== feature.id
                        );
                    // Ensure required features aren't removed if somehow changed via dev tools etc.
                    const requiredIds = featureOptions
                      .filter((f) => f.required)
                      .map((f) => f.id);
                    const finalFeatures = Array.from(
                      new Set([...requiredIds, ...updatedFeatures])
                    );
                    handleChange("selectedFeatures", finalFeatures);
                    handleChange("features", finalFeatures); // Keep both in sync
                  }}
                  className={`h-4 w-4 rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-600 mt-0.5 ${
                    feature.required
                      ? "cursor-not-allowed opacity-70"
                      : "cursor-pointer"
                  }`}
                />
                <label
                  htmlFor={`${feature.id}-desktop`}
                  className={`ml-2 block text-sm ${
                    feature.required ? "cursor-not-allowed" : "cursor-pointer"
                  }`}
                >
                  <span className="text-gray-800 dark:text-gray-200 font-medium">
                    {feature.name}
                    {feature.required && (
                      <span className="ml-1 text-xs text-blue-600 dark:text-blue-400">
                        (Required)
                      </span>
                    )}
                  </span>
                  <span className="text-xs text-gray-500 dark:text-gray-400 block">
                    {feature.description}
                  </span>
                </label>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* SEO & Multilingual Options */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
        <div
          className={`p-4 rounded-lg border cursor-pointer transition-all duration-300 ${
            formData.seo
              ? "border-blue-600 bg-blue-50 dark:bg-blue-600/20"
              : "border-gray-200 dark:border-gray-700"
          }`}
          onClick={() => handleChange("seo", !formData.seo)}
        >
          <div className="flex items-center">
            <div
              className={`w-5 h-5 rounded-full flex items-center justify-center ${
                formData.seo
                  ? "bg-blue-600 text-white"
                  : "border-2 border-gray-300 dark:border-gray-600"
              }`}
            >
              {formData.seo && <Check className="w-3 h-3" />}
            </div>
            <div className="ml-3">
              <h4 className="font-medium text-gray-800 dark:text-gray-200">
                Search Engine Optimization
              </h4>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Optimize your site for search engines
              </p>
            </div>
          </div>
        </div>

        <div
          className={`p-4 rounded-lg border cursor-pointer transition-all duration-300 ${
            formData.multilingual
              ? "border-blue-600 bg-blue-50 dark:bg-blue-600/20"
              : "border-gray-200 dark:border-gray-700"
          }`}
          onClick={() => handleChange("multilingual", !formData.multilingual)}
        >
          <div className="flex items-center">
            <div
              className={`w-5 h-5 rounded-full flex items-center justify-center ${
                formData.multilingual
                  ? "bg-blue-600 text-white"
                  : "border-2 border-gray-300 dark:border-gray-600"
              }`}
            >
              {formData.multilingual && <Check className="w-3 h-3" />}
            </div>
            <div className="ml-3">
              <h4 className="font-medium text-gray-800 dark:text-gray-200">
                Multilingual Support
              </h4>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Add multiple language versions
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Conversion Optimization Highlight */}
      <div className="mb-6 p-4 bg-[#3f5781]/10 dark:bg-[#3f5781]/20 rounded-lg">
        <div className="flex items-center">
          <BarChart className="w-5 h-5 text-[#3f5781] dark:text-[#8fa3c7] mr-2" />
          <h4 className="font-medium text-[#3f5781] dark:text-[#8fa3c7]">
            AI-Powered Conversion Optimization
          </h4>
        </div>
        <p className="text-sm text-gray-700 dark:text-gray-300 mt-2">
          All our websites include AI-powered heatmap analysis and conversion
          rate optimization. We analyze user behavior to maximize engagement and
          conversions.
        </p>
        <div className="mt-3 flex items-center text-sm">
          <svg
            className="w-4 h-4 text-green-600 mr-1"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          <span className="text-green-600 font-medium">
            Includes 48-hour conversion guarantee
          </span>
        </div>
      </div>

      {/* Custom Features */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Custom Features
        </label>
        <div className="relative">
          <textarea
            className="w-full p-3 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 text-sm min-h-[100px]"
            placeholder="Describe any custom features you need for your website..."
            value={formData.customFeatures}
            onChange={(e) => {
              handleChange("customFeatures", e.target.value);
              handleChange("needsReview", e.target.value.trim().length > 0);
            }}
          ></textarea>
          <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            Custom features require review before final pricing
          </div>
        </div>
      </div>

      <div className="mt-8">
        <button
          type="submit"
          className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-lg font-medium transition-colors duration-300 flex items-center justify-center"
        >
          Calculate Price <ChevronRight className="ml-2 h-4 w-4" />
        </button>
      </div>
    </form>
  );
};

export default HomepageCalculator;
