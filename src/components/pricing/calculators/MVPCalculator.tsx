"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { Rocket, ChevronRight, Info, Check } from "lucide-react";
import PricingSlider from "@/components/pricing/ui/PricingSlider";
// Removed FeatureSelector import
import InteractiveFeatureSelector from "@/components/pricing/ui/InteractiveFeatureSelector";
// Removed duplicate Info import

interface MVPCalculatorProps {
  onSubmit: (data: Record<string, any>) => void;
}

type Industry = "ecommerce" | "health" | "finance" | "social" | "productivity" | "other";
type Timeline = "normal" | "fast" | "urgent";

const MVPCalculator = ({ onSubmit }: MVPCalculatorProps) => {
  const [formData, setFormData] = useState({
    industry: "other" as Industry,
    timeline: "normal" as Timeline,
    selectedFeatures: ["auth_email", "profile"] as string[],
    customFeatures: "",
    needsReview: false,
  });

  const handleChange = (field: string, value: any) => {
    setFormData({
      ...formData,
      [field]: value
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  // Industry options
  const industries = [
    { id: "ecommerce", label: "E-commerce", factor: 1.2 },
    { id: "health", label: "Healthcare", factor: 1.3 },
    { id: "finance", label: "Finance", factor: 1.4 },
    { id: "social", label: "Social Media", factor: 1.1 },
    { id: "productivity", label: "Productivity", factor: 1.0 },
    { id: "other", label: "Other", factor: 1.0 }
  ];

  // Timeline options - with 40% faster time-to-market
  const timelines = [
    { id: "normal", label: "Standard (4-5 weeks)", factor: 1.0, description: "Regular development pace" },
    { id: "fast", label: "Accelerated (3-4 weeks)", factor: 1.3, description: "30% faster delivery" },
    { id: "urgent", label: "Priority (2-3 weeks)", factor: 1.6, description: "Dedicated team, fastest delivery" }
  ];
  
  // Standard app features
  const standardFeatures = [
    // Authentication options
    { id: "auth_email", name: "Email Authentication", category: "auth", description: "Login with email and password" },
    { id: "auth_google", name: "Google Sign-in", category: "auth", description: "Login with Google account" },
    { id: "auth_apple", name: "Apple Sign-in", category: "auth", description: "Login with Apple ID" },
    { id: "auth_facebook", name: "Facebook Login", category: "auth", description: "Login with Facebook account" },
    { id: "auth_phone", name: "Phone Authentication", category: "auth", description: "Login with phone number" },
    
    // User profile features
    { id: "profile", name: "User Profiles", category: "user", description: "User profile management" },
    { id: "user_roles", name: "User Roles & Permissions", category: "user", description: "Different access levels" },
    
    // Common features
    { id: "push", name: "Push Notifications", category: "common", description: "Send notifications to users" },
    { id: "offline", name: "Offline Mode", category: "common", description: "App works without internet" },
    { id: "analytics", name: "Analytics Integration", category: "common", description: "Track user behavior" },
    { id: "in_app_purchase", name: "In-App Purchases", category: "payment", description: "Sell digital goods" },
    { id: "subscription", name: "Subscription Management", category: "payment", description: "Recurring payment model" },
    
    // Device features
    { id: "camera", name: "Camera Integration", category: "device", description: "Access device camera" },
    { id: "maps", name: "Maps & Location", category: "device", description: "Location-based features" },
    { id: "barcode", name: "Barcode/QR Scanner", category: "device", description: "Scan codes with camera" },
    { id: "biometric", name: "Biometric Authentication", category: "device", description: "Fingerprint/Face ID" },
    
    // Data & Storage
    { id: "cloud_sync", name: "Cloud Synchronization", category: "data", description: "Sync data across devices" },
    { id: "file_upload", name: "File Upload/Download", category: "data", description: "Handle files in app" },
    
    // Social features
    { id: "social_sharing", name: "Social Sharing", category: "social", description: "Share to social networks" },
    { id: "chat", name: "Chat/Messaging", category: "social", description: "In-app communication" }
  ];

  return (
    <form onSubmit={handleSubmit} className="w-full">
      <div className="mb-6">
        <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-1">
          MVP Development Calculator
        </h3>
        <p className="text-gray-600 dark:text-gray-300 text-sm">
          Configure your MVP requirements to get a custom quote
        </p>
      </div>

      {/* Industry Selection */}
      <div className="mb-6 w-full">
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Select Your Industry
        </label>
        <div className="grid grid-cols-2 sm:grid-cols-3 gap-2 w-full">
          {industries.map((industry) => (
            <button
              key={industry.id}
              type="button"
              onClick={() => handleChange("industry", industry.id)}
              className={`py-2 px-3 text-sm rounded-lg transition-all duration-300 flex items-center justify-center ${
                formData.industry === industry.id
                  ? "bg-blue-600 text-white shadow-sm"
                  : "bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600"
              }`}
            >
              {industry.label}
            </button>
          ))}
        </div>
      </div>

      {/* Timeline Selection - in a row */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Development Timeline
        </label>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
          {timelines.map((timeline) => (
            <div
              key={timeline.id}
              className={`p-3 rounded-lg border cursor-pointer transition-all duration-300 ${
                formData.timeline === timeline.id
                  ? "border-blue-600 bg-blue-50 dark:bg-blue-600/20"
                  : "border-gray-200 dark:border-gray-700 hover:border-blue-500/50"
              }`}
              onClick={() => handleChange("timeline", timeline.id)}
            >
              <div className="flex items-center mb-1">
                <div
                  className={`w-4 h-4 rounded-full flex items-center justify-center flex-shrink-0 ${
                    formData.timeline === timeline.id
                      ? "bg-blue-600 text-white"
                      : "border-2 border-gray-300 dark:border-gray-600"
                  }`}
                >
                  {formData.timeline === timeline.id && (
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                  )}
                </div>
                <div className="ml-2 font-medium text-gray-800 dark:text-gray-200">
                  {timeline.label}
                </div>
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400 pl-6">
                {timeline.description}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Feature Selection - Responsive */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Select App Features
        </label>

        {/* Info Text (Common for both views) */}
        <div className="bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg border border-gray-200 dark:border-gray-700 mb-3 text-xs text-gray-600 dark:text-gray-300 flex items-start">
          <Info className="w-4 h-4 mr-1.5 flex-shrink-0 mt-0.5" />
          <span
            dangerouslySetInnerHTML={{
              __html:
                "Select the features you need for your <strong>fully functional, App Store ready</strong> MVP. These are your core features.",
            }}
          />
        </div>

        {/* Mobile View: Interactive Tag Selector */}
        <div className="block md:hidden">
          <InteractiveFeatureSelector
            availableFeatures={standardFeatures}
            selectedFeatures={formData.selectedFeatures}
            onChange={(newSelectedIds) =>
              handleChange("selectedFeatures", newSelectedIds)
            }
            placeholder="Tap to add features..."
            // Pass empty label/info as they are handled above
            label=""
            infoText=""
          />
        </div>

        {/* Desktop View: Checkbox Grid */}
        <div className="hidden md:block bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="grid grid-cols-2 gap-x-4 gap-y-3">
            {standardFeatures.map((feature) => (
              <div key={`${feature.id}-desktop`} className="flex items-start">
                <input
                  type="checkbox"
                  id={`${feature.id}-desktop`}
                  checked={formData.selectedFeatures.includes(feature.id)}
                  onChange={(e) => {
                    const updatedFeatures = e.target.checked
                      ? [...formData.selectedFeatures, feature.id]
                      : formData.selectedFeatures.filter(
                          (f) => f !== feature.id
                        );
                    handleChange("selectedFeatures", updatedFeatures);
                  }}
                  className="h-4 w-4 rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-600 mt-0.5"
                />
                <label
                  htmlFor={`${feature.id}-desktop`}
                  className="ml-2 block text-sm cursor-pointer"
                >
                  <span className="text-gray-800 dark:text-gray-200 font-medium">
                    {feature.name}
                  </span>
                  <span className="text-xs text-gray-500 dark:text-gray-400 block">
                    {feature.description}
                  </span>
                </label>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Custom Features */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Custom Features (Optional)
        </label>
        <textarea
          value={formData.customFeatures}
          onChange={(e) => {
            const hasContent = e.target.value.trim().length > 0;
            handleChange("customFeatures", e.target.value);
            handleChange("needsReview", hasContent);
          }}
          placeholder="Describe any custom features you need that aren't listed above..."
          className="w-full rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 p-3 text-sm text-gray-700 dark:text-gray-300 focus:border-blue-600 focus:ring-blue-600 transition-colors duration-200"
          rows={3}
        />
        {formData.needsReview && (
          <div className="mt-2 text-xs text-amber-600 dark:text-amber-400 flex items-center">
            <Info className="w-3 h-3 mr-1" />
            We'll need to review your custom requirements for accurate pricing
          </div>
        )}
      </div>

      {/* Code Review Option */}
      <div className="mb-8">
        <div className="flex items-start">
          <input
            type="checkbox"
            id="needsReview"
            checked={formData.needsReview}
            onChange={(e) => handleChange("needsReview", e.target.checked)}
            className="h-4 w-4 rounded border-gray-300 dark:border-gray-700 text-blue-600 focus:ring-blue-600 mt-0.5"
          />
          <label htmlFor="needsReview" className="ml-2 block text-sm">
            <span className="text-gray-800 dark:text-gray-200 font-medium">
              I'd like a code review of my existing project
            </span>
            <span className="text-xs text-gray-500 dark:text-gray-400 block">
              Our experts will evaluate your code quality, architecture and
              provide recommendations
            </span>
          </label>
        </div>
      </div>

      {/* Submit Button */}
      <div className="flex justify-end">
        <button
          type="submit"
          className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors flex items-center text-sm font-medium"
        >
          Continue
          <ChevronRight className="w-4 h-4 ml-1" />
        </button>
      </div>
    </form>
  );
};

export default MVPCalculator;
