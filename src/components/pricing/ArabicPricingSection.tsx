import { type Dictionary } from "@/lib/dictionary";
import { Section } from "@/components/ui/Section";

interface ArabicPricingSectionProps {
  dictionary?: Dictionary["prices"];
}

export default function ArabicPricingSection({ dictionary }: ArabicPricingSectionProps) {
  return (
    <Section id="prices" className="bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl md:text-4xl font-bold text-center mb-8 text-gray-900 dark:text-white">
          {dictionary?.title || "أسعارنا"}
        </h2>
        
        <p className="text-center text-gray-600 dark:text-gray-400 mb-12 max-w-3xl mx-auto">
          {dictionary?.description || "يرجى الاتصال بنا للحصول على معلومات حول الأسعار والباقات المخصصة"}
        </p>
        
        {/* Dubai-specific rates and pricing info */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 mb-10">
          <div className="flex flex-col md:flex-row justify-between gap-6">
            <div className="flex-1">
              <h3 className="text-xl font-bold mb-4 text-gray-900 dark:text-white">
                خدماتنا في دبي
              </h3>
              <ul className="list-disc list-inside space-y-2 text-gray-700 dark:text-gray-300">
                <li>سعر ساعة التطوير: €85</li>
                <li>حلول متوافقة مع متطلبات دبي</li>
                <li>تكامل بوابات الدفع الإماراتية</li>
                <li>دعم متعدد اللغات</li>
              </ul>
            </div>
            <div className="flex-1">
              <h3 className="text-xl font-bold mb-4 text-gray-900 dark:text-white">
                عروضنا الترويجية
              </h3>
              <div className="bg-blue-50 dark:bg-blue-900/30 p-4 rounded-lg">
                <p className="font-medium text-blue-700 dark:text-blue-300">
                  خصم 15% عند دمج الخدمات المتعددة
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                  احصل على خصم 15% عند طلب أي خدمتين أو أكثر من خدماتنا
                </p>
              </div>
            </div>
          </div>
        </div>
        
        <div className="flex justify-center">
          <a 
            href="#contact" 
            className="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            {dictionary?.contactButton || "اتصل بنا"}
          </a>
        </div>
      </div>
    </Section>
  );
}
