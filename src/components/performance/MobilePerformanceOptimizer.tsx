"use client";

import { useEffect, useState, useCallback } from "react";

interface MobilePerformanceOptimizerProps {
  children: React.ReactNode;
  enableOptimizations?: boolean;
  debug?: boolean;
}

/**
 * Mobile Performance Optimizer Component
 * Applies aggressive optimizations for mobile devices
 */
export function MobilePerformanceOptimizer({
  children,
  enableOptimizations = true,
  debug = false,
}: MobilePerformanceOptimizerProps) {
  const [isMobile, setIsMobile] = useState(false);
  const [isLowEndDevice, setIsLowEndDevice] = useState(false);
  const [optimizationsApplied, setOptimizationsApplied] = useState(false);

  // Device detection
  const detectDevice = useCallback(() => {
    if (typeof window === 'undefined') return;

    const isMobileDevice = window.innerWidth <= 768;
    const isLowEnd = 
      (navigator as any).hardwareConcurrency <= 4 ||
      (navigator as any).deviceMemory <= 4 ||
      /Android.*Chrome\/[.0-9]*\s/.test(navigator.userAgent);

    setIsMobile(isMobileDevice);
    setIsLowEndDevice(isLowEnd);

    if (debug) {
      console.log('Device Detection:', {
        isMobile: isMobileDevice,
        isLowEnd,
        hardwareConcurrency: (navigator as any).hardwareConcurrency,
        deviceMemory: (navigator as any).deviceMemory,
        userAgent: navigator.userAgent,
      });
    }
  }, [debug]);

  // Apply mobile optimizations
  const applyMobileOptimizations = useCallback(() => {
    if (!enableOptimizations || typeof window === 'undefined') return;

    const root = document.documentElement;

    if (isMobile) {
      // Reduce animations on mobile
      root.style.setProperty('--animation-duration', '0.2s');
      root.style.setProperty('--transition-duration', '0.2s');
      
      // Disable complex effects on low-end devices
      if (isLowEndDevice) {
        root.style.setProperty('--motion-reduce', '1');
        root.style.setProperty('--backdrop-blur', 'none');
        root.style.setProperty('--box-shadow', 'none');
        
        // Add low-end device class
        document.body.classList.add('low-end-device');
      }

      // Add mobile optimization classes
      document.body.classList.add('mobile-optimized');
      
      // Optimize scroll behavior
      document.body.style.overscrollBehavior = 'contain';
      
      // Disable smooth scrolling on mobile for better performance
      document.body.style.scrollBehavior = 'auto';
    }

    setOptimizationsApplied(true);

    if (debug) {
      console.log('Mobile optimizations applied:', {
        isMobile,
        isLowEndDevice,
        optimizationsEnabled: enableOptimizations,
      });
    }
  }, [isMobile, isLowEndDevice, enableOptimizations, debug]);

  // Preload critical resources
  const preloadCriticalResources = useCallback(() => {
    if (typeof window === 'undefined') return;

    const criticalResources = [
      { href: '/fonts/inter-var.woff2', as: 'font', type: 'font/woff2' },
    ];

    criticalResources.forEach(({ href, as, type }) => {
      const existingLink = document.querySelector(`link[href="${href}"]`);
      if (!existingLink) {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.href = href;
        link.as = as;
        if (type) link.type = type;
        if (as === 'font') link.crossOrigin = 'anonymous';
        document.head.appendChild(link);
      }
    });

    if (debug) {
      console.log('Critical resources preloaded');
    }
  }, [debug]);

  // Optimize images for mobile
  const optimizeImages = useCallback(() => {
    if (!isMobile || typeof window === 'undefined') return;

    // Add intersection observer for lazy loading
    const images = document.querySelectorAll('img[data-src]');
    
    const imageObserver = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement;
          const src = img.getAttribute('data-src');
          if (src) {
            img.src = src;
            img.removeAttribute('data-src');
            imageObserver.unobserve(img);
          }
        }
      });
    }, {
      rootMargin: '50px',
      threshold: 0.1,
    });

    images.forEach((img) => imageObserver.observe(img));

    if (debug) {
      console.log(`Optimized ${images.length} images for lazy loading`);
    }
  }, [isMobile, debug]);

  // Reduce DOM complexity
  const reduceDOMComplexity = useCallback(() => {
    if (!isMobile || typeof window === 'undefined') return;

    // Hide non-essential elements on mobile
    const nonEssentialSelectors = [
      '.desktop-only',
      '.high-res-only',
      '.complex-animation',
    ];

    nonEssentialSelectors.forEach((selector) => {
      const elements = document.querySelectorAll(selector);
      elements.forEach((el) => {
        (el as HTMLElement).style.display = 'none';
      });
    });

    if (debug) {
      console.log('DOM complexity reduced for mobile');
    }
  }, [isMobile, debug]);

  // Initialize optimizations
  useEffect(() => {
    detectDevice();
  }, [detectDevice]);

  useEffect(() => {
    if (isMobile || isLowEndDevice) {
      applyMobileOptimizations();
      preloadCriticalResources();
      
      // Delay non-critical optimizations
      setTimeout(() => {
        optimizeImages();
        reduceDOMComplexity();
      }, 100);
    }
  }, [
    isMobile,
    isLowEndDevice,
    applyMobileOptimizations,
    preloadCriticalResources,
    optimizeImages,
    reduceDOMComplexity,
  ]);

  // Performance monitoring
  useEffect(() => {
    if (!debug || typeof window === 'undefined') return;

    const observer = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        if (entry.entryType === 'largest-contentful-paint') {
          console.log('LCP:', entry.startTime);
        }
        if (entry.entryType === 'first-input') {
          console.log('FID:', entry.processingStart - entry.startTime);
        }
        if (entry.entryType === 'layout-shift') {
          console.log('CLS:', entry.value);
        }
      });
    });

    observer.observe({ entryTypes: ['largest-contentful-paint', 'first-input', 'layout-shift'] });

    return () => observer.disconnect();
  }, [debug]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (optimizationsApplied && typeof window !== 'undefined') {
        document.body.classList.remove('mobile-optimized', 'low-end-device');
      }
    };
  }, [optimizationsApplied]);

  return (
    <>
      {children}
      {debug && (
        <div className="fixed bottom-4 left-4 z-50 bg-black bg-opacity-80 text-white p-3 rounded-lg text-xs font-mono max-w-xs">
          <div>Mobile: {isMobile ? 'Yes' : 'No'}</div>
          <div>Low-end: {isLowEndDevice ? 'Yes' : 'No'}</div>
          <div>Optimized: {optimizationsApplied ? 'Yes' : 'No'}</div>
          <div>Screen: {typeof window !== 'undefined' ? `${window.innerWidth}x${window.innerHeight}` : 'N/A'}</div>
        </div>
      )}
    </>
  );
}

export default MobilePerformanceOptimizer;
