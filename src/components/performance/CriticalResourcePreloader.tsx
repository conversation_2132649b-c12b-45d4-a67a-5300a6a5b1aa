"use client";

import { useEffect } from "react";

interface CriticalResourcePreloaderProps {
  heroImages?: string[];
  enablePreloading?: boolean;
}

/**
 * Critical Resource Preloader Component
 * Preloads critical resources to improve LCP and reduce TBT
 */
export function CriticalResourcePreloader({
  heroImages = [],
  enablePreloading = true,
}: CriticalResourcePreloaderProps) {
  useEffect(() => {
    if (!enablePreloading || typeof window === "undefined") return;

    const preloadCriticalResources = () => {
      // 1. Preload critical hero images (especially LCP image)
      if (heroImages.length > 0) {
        heroImages.slice(0, 2).forEach((imageSrc, index) => {
          const pathParts = imageSrc.split("/");
          const filename = pathParts[pathParts.length - 1];
          const nameWithoutExt = filename.split(".")[0];
          const directory = pathParts[pathParts.length - 2];

          // Preload AVIF first (best compression)
          const avifSrc = `/images/optimized/${directory}/${nameWithoutExt}.avif`;
          const webpSrc = `/images/optimized/${directory}/${nameWithoutExt}.webp`;

          const preloadAvif = document.createElement("link");
          preloadAvif.rel = "preload";
          preloadAvif.as = "image";
          preloadAvif.href = avifSrc;
          preloadAvif.type = "image/avif";
          if (index === 0) preloadAvif.fetchPriority = "high"; // First image has highest priority
          document.head.appendChild(preloadAvif);

          // Fallback to WebP
          const preloadWebp = document.createElement("link");
          preloadWebp.rel = "preload";
          preloadWebp.as = "image";
          preloadWebp.href = webpSrc;
          preloadWebp.type = "image/webp";
          document.head.appendChild(preloadWebp);
        });
      }

      // 2. Preload critical fonts
      const criticalFonts = ["/fonts/inter-var.woff2"];

      criticalFonts.forEach((fontPath) => {
        const link = document.createElement("link");
        link.rel = "preload";
        link.as = "font";
        link.type = "font/woff2";
        link.crossOrigin = "anonymous";
        link.href = fontPath;
        document.head.appendChild(link);
      });

      // 3. Prefetch likely navigation targets (reduce subsequent page load times)
      const prefetchTargets = [
        "/de/services",
        "/de/portfolio",
        "/de/contact",
        "/de/about",
      ];

      // Delay prefetching to avoid competing with critical resources
      setTimeout(() => {
        prefetchTargets.forEach((href) => {
          const link = document.createElement("link");
          link.rel = "prefetch";
          link.href = href;
          document.head.appendChild(link);
        });
      }, 2000);

      // 4. Preload critical CSS for faster rendering
      const criticalStyles = document.querySelectorAll(
        'style[data-styled="active"]'
      );
      criticalStyles.forEach((style) => {
        if (style.textContent && style.textContent.length > 1000) {
          // Mark large stylesheets as critical
          style.setAttribute("data-critical", "true");
        }
      });
    };

    // 5. Optimize timing to avoid blocking main thread
    if (document.readyState === "loading") {
      document.addEventListener("DOMContentLoaded", preloadCriticalResources);
    } else {
      // Use scheduler API if available, otherwise requestIdleCallback
      if ("scheduler" in window && "postTask" in (window as any).scheduler) {
        (window as any).scheduler.postTask(preloadCriticalResources, {
          priority: "user-blocking",
        });
      } else if ("requestIdleCallback" in window) {
        requestIdleCallback(preloadCriticalResources, { timeout: 1000 });
      } else {
        setTimeout(preloadCriticalResources, 100);
      }
    }

    // Cleanup function
    return () => {
      document.removeEventListener(
        "DOMContentLoaded",
        preloadCriticalResources
      );
    };
  }, [heroImages, enablePreloading]);

  // Additional performance optimizations
  useEffect(() => {
    if (!enablePreloading || typeof window === "undefined") return;

    // 6. Optimize main thread by deferring non-critical work
    const optimizeMainThread = () => {
      // Reduce animation frame rate on low-end devices
      const isLowEndDevice =
        navigator.hardwareConcurrency <= 4 ||
        (navigator as any).deviceMemory <= 4;

      if (isLowEndDevice) {
        document.documentElement.style.setProperty(
          "--animation-duration",
          "0.3s"
        );
        document.documentElement.style.setProperty("--motion-reduce", "1");
      }

      // Lazy load non-critical resources after main content
      setTimeout(() => {
        // Load analytics after main content is rendered
        if (process.env.NODE_ENV === "production") {
          // Analytics scripts would go here
        }
      }, 3000);
    };

    // 7. Service Worker registration for caching
    if ("serviceWorker" in navigator && process.env.NODE_ENV === "production") {
      navigator.serviceWorker
        .register("/sw.js", {
          scope: "/",
          updateViaCache: "imports",
        })
        .catch((err) => {
          console.log("ServiceWorker registration failed: ", err);
        });
    }

    // Run optimizations after initial render
    requestAnimationFrame(optimizeMainThread);
  }, [enablePreloading]);

  // This component doesn't render anything
  return null;
}

export default CriticalResourcePreloader;
