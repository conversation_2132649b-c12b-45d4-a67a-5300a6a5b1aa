"use client";

import { useEffect, useState } from "react";

interface WebVitalsProps {
  debug?: boolean;
}

const WebVitalsOptimizer = ({ debug = false }: WebVitalsProps) => {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    if (!isMounted || typeof window === "undefined") return;

    // Performance optimizations
    const optimizePerformance = () => {
      // Preload critical resources
      const criticalResources = ["/fonts/inter-var.woff2", "/images/logo.svg"];

      criticalResources.forEach((resource) => {
        const link = document.createElement("link");
        link.rel = "preload";
        link.href = resource;
        link.as = resource.includes("font") ? "font" : "image";
        if (resource.includes("font")) {
          link.type = "font/woff2";
          link.crossOrigin = "anonymous";
        }
        document.head.appendChild(link);
      });

      // Prefetch likely navigation targets
      const prefetchLinks = ["/de/blog", "/de/contact", "/de/services"];

      prefetchLinks.forEach((href) => {
        const link = document.createElement("link");
        link.rel = "prefetch";
        link.href = href;
        document.head.appendChild(link);
      });

      // Service Worker registration for caching
      if ("serviceWorker" in navigator) {
        navigator.serviceWorker.register("/sw.js").catch((err) => {
          if (debug) console.log("ServiceWorker registration failed: ", err);
        });
      }
    };

    // Run optimizations
    optimizePerformance();

    return () => {
      // Cleanup if needed
    };
  }, [isMounted, debug]);

  // Performance monitoring overlay (only in debug mode and after mount)
  if (debug && isMounted) {
    return (
      <div className="fixed bottom-4 right-4 z-50 bg-black bg-opacity-80 text-white p-3 rounded-lg text-xs font-mono">
        <div>Performance Monitor Active</div>
        <div>Check console for optimizations</div>
      </div>
    );
  }

  return null;
};

export default WebVitalsOptimizer;
