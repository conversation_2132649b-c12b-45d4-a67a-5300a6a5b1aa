"use client";

import { useEffect, useState } from "react";

interface AIContentOptimizerProps {
  locale: string;
  pageType: "home" | "about" | "services" | "blog" | "contact" | "product";
  primaryKeywords: string[];
  secondaryKeywords?: string[];
  contentCategory: string;
  entityType: "Organization" | "Service" | "Product" | "Article" | "WebPage";
}

const AIContentOptimizer = ({
  locale,
  pageType,
  primaryKeywords,
  secondaryKeywords = [],
  contentCategory,
  entityType,
}: AIContentOptimizerProps) => {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    if (!isMounted) return;
    // Add semantic markup for AI understanding
    const addSemanticMarkup = () => {
      // Add data attributes to key elements for AI parsing
      const headings = document.querySelectorAll("h1, h2, h3, h4, h5, h6");
      headings.forEach((heading, index) => {
        heading.setAttribute(
          "data-ai-heading-level",
          heading.tagName.toLowerCase()
        );
        heading.setAttribute(
          "data-ai-content-importance",
          index < 3 ? "high" : "medium"
        );
      });

      // Mark key content sections
      const sections = document.querySelectorAll("section, article, main");
      sections.forEach((section) => {
        const headingInSection = section.querySelector("h1, h2, h3");
        if (headingInSection) {
          const sectionTopic =
            headingInSection.textContent?.toLowerCase() || "";

          // Categorize sections based on content
          if (
            sectionTopic.includes("service") ||
            sectionTopic.includes("angebot")
          ) {
            section.setAttribute("data-ai-section-type", "services");
          } else if (
            sectionTopic.includes("about") ||
            sectionTopic.includes("über")
          ) {
            section.setAttribute("data-ai-section-type", "about");
          } else if (
            sectionTopic.includes("contact") ||
            sectionTopic.includes("kontakt")
          ) {
            section.setAttribute("data-ai-section-type", "contact");
          } else if (
            sectionTopic.includes("portfolio") ||
            sectionTopic.includes("project")
          ) {
            section.setAttribute("data-ai-section-type", "portfolio");
          } else {
            section.setAttribute("data-ai-section-type", "content");
          }
        }
      });

      // Mark important lists (features, benefits, etc.)
      const lists = document.querySelectorAll("ul, ol");
      lists.forEach((list) => {
        const parentSection = list.closest("section");
        const items = list.querySelectorAll("li");

        if (items.length >= 3) {
          list.setAttribute("data-ai-list-type", "feature-list");
          items.forEach((item, index) => {
            item.setAttribute("data-ai-list-item", `${index + 1}`);
          });
        }
      });

      // Mark call-to-action elements
      const ctaElements = document.querySelectorAll(
        'button, .cta, .button, a[href*="contact"], a[href*="pricing"]'
      );
      ctaElements.forEach((cta) => {
        cta.setAttribute("data-ai-element", "call-to-action");
      });

      // Mark testimonials and reviews
      const testimonials = document.querySelectorAll(
        '.testimonial, [data-testid*="testimonial"], .review'
      );
      testimonials.forEach((testimonial) => {
        testimonial.setAttribute("data-ai-content-type", "testimonial");
      });
    };

    // Add JSON-LD for enhanced AI understanding
    const addEnhancedStructuredData = () => {
      const structuredData = {
        "@context": "https://schema.org",
        "@graph": [
          {
            "@type": "WebPage",
            "@id": `https://innovatio-pro.com${window.location.pathname}#webpage`,
            url: `https://innovatio-pro.com${window.location.pathname}`,
            name: document.title,
            description: document
              .querySelector('meta[name="description"]')
              ?.getAttribute("content"),
            inLanguage: locale,
            isPartOf: {
              "@id": "https://innovatio-pro.com#website",
            },
            primaryImageOfPage: {
              "@id": `https://innovatio-pro.com${window.location.pathname}#primaryimage`,
            },
            breadcrumb: {
              "@id": `https://innovatio-pro.com${window.location.pathname}#breadcrumb`,
            },
            mainEntity: {
              "@id": `https://innovatio-pro.com${window.location.pathname}#main-entity`,
            },
          },
          {
            "@type": "WebSite",
            "@id": "https://innovatio-pro.com#website",
            url: "https://innovatio-pro.com",
            name: "Innovatio",
            description:
              "Leading digital solutions company specializing in web and mobile application development",
            inLanguage: locale,
            potentialAction: {
              "@type": "SearchAction",
              target: {
                "@type": "EntryPoint",
                urlTemplate:
                  "https://innovatio-pro.com/search?q={search_term_string}",
              },
              "query-input": "required name=search_term_string",
            },
          },
          {
            "@type": entityType,
            "@id": `https://innovatio-pro.com${window.location.pathname}#main-entity`,
            ...(entityType === "Organization" && {
              name: "Innovatio",
              alternateName: "Innovatio Digital Solutions",
              description: "Professional digital transformation company",
              url: "https://innovatio-pro.com",
              logo: "https://innovatio-pro.com/images/logo.png",
              sameAs: [
                "https://linkedin.com/company/innovatio",
                "https://github.com/innovatio",
              ],
              contactPoint: {
                "@type": "ContactPoint",
                contactType: "customer service",
                availableLanguage: [
                  "English",
                  "German",
                  "Russian",
                  "Turkish",
                  "Arabic",
                ],
              },
              areaServed: "Worldwide",
              serviceArea: {
                "@type": "Place",
                name: "Global",
              },
              knowsAbout: primaryKeywords.concat(secondaryKeywords),
            }),
            ...(entityType === "Service" && {
              name: primaryKeywords[0] || "Digital Solutions",
              description: `Professional ${contentCategory} services`,
              provider: {
                "@type": "Organization",
                name: "Innovatio",
              },
              areaServed: "Worldwide",
              category: contentCategory,
              serviceType: contentCategory,
            }),
          },
          // FAQ Schema if page contains FAQ content
          ...(document.querySelector(".faq, [data-faq]")
            ? [
                {
                  "@type": "FAQPage",
                  "@id": `https://innovatio-pro.com${window.location.pathname}#faq`,
                  mainEntity: Array.from(
                    document.querySelectorAll(".faq-item, [data-faq-item]")
                  ).map((item, index) => {
                    const question = item.querySelector(
                      ".faq-question, [data-faq-question]"
                    )?.textContent;
                    const answer = item.querySelector(
                      ".faq-answer, [data-faq-answer]"
                    )?.textContent;

                    return {
                      "@type": "Question",
                      name: question || `Question ${index + 1}`,
                      acceptedAnswer: {
                        "@type": "Answer",
                        text: answer || "Answer not available",
                      },
                    };
                  }),
                },
              ]
            : []),
        ],
      };

      const script = document.createElement("script");
      script.type = "application/ld+json";
      script.textContent = JSON.stringify(structuredData);
      script.id = "ai-enhanced-structured-data";

      // Remove existing script if present
      const existingScript = document.getElementById(
        "ai-enhanced-structured-data"
      );
      if (existingScript) {
        existingScript.remove();
      }

      document.head.appendChild(script);
    };

    // Add meta tags for AI content classification
    const addAIMetaTags = () => {
      const metaTags = [
        { name: "ai:content-category", content: contentCategory },
        { name: "ai:entity-type", content: entityType },
        { name: "ai:primary-keywords", content: primaryKeywords.join(", ") },
        {
          name: "ai:secondary-keywords",
          content: secondaryKeywords.join(", "),
        },
        { name: "ai:page-type", content: pageType },
        { name: "ai:content-language", content: locale },
        { name: "ai:content-intent", content: getContentIntent(pageType) },
        { name: "ai:target-audience", content: getTargetAudience(pageType) },
      ];

      metaTags.forEach(({ name, content }) => {
        if (content) {
          const existingMeta = document.querySelector(`meta[name="${name}"]`);
          if (existingMeta) {
            existingMeta.setAttribute("content", content);
          } else {
            const meta = document.createElement("meta");
            meta.name = name;
            meta.content = content;
            document.head.appendChild(meta);
          }
        }
      });
    };

    // Run optimizations
    addSemanticMarkup();
    addEnhancedStructuredData();
    addAIMetaTags();

    // Update when content changes
    const observer = new MutationObserver(() => {
      addSemanticMarkup();
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });

    return () => {
      observer.disconnect();
    };
  }, [
    isMounted,
    locale,
    pageType,
    primaryKeywords,
    secondaryKeywords,
    contentCategory,
    entityType,
  ]);

  return null;
};

// Helper functions
function getContentIntent(pageType: string): string {
  const intentMap: Record<string, string> = {
    home: "brand-awareness",
    about: "informational",
    services: "commercial",
    blog: "educational",
    contact: "transactional",
    product: "commercial",
  };
  return intentMap[pageType] || "informational";
}

function getTargetAudience(pageType: string): string {
  const audienceMap: Record<string, string> = {
    home: "business-owners, entrepreneurs, developers",
    about: "potential-clients, partners",
    services: "business-decision-makers",
    blog: "developers, tech-enthusiasts",
    contact: "potential-clients",
    product: "business-owners, project-managers",
  };
  return audienceMap[pageType] || "general-audience";
}

export default AIContentOptimizer;
