import Head from "next/head";
import { Fragment } from "react";

interface AdvancedSEOProps {
  title: string;
  description: string;
  keywords?: string;
  canonical?: string;
  locale: string;
  alternateLanguages?: Array<{
    locale: string;
    href: string;
  }>;
  openGraph?: {
    title?: string;
    description?: string;
    image?: string;
    type?: "website" | "article" | "product";
    url?: string;
  };
  structuredData?: Array<object>;
  aiOptimization?: {
    primaryTopic: string;
    secondaryTopics: string[];
    entityType: "Organization" | "Service" | "Product" | "Article";
    expertise: string[];
    targetIntent:
      | "informational"
      | "commercial"
      | "transactional"
      | "navigational";
  };
}

const AdvancedSEO = ({
  title,
  description,
  keywords,
  canonical,
  locale,
  alternateLanguages = [],
  openGraph,
  structuredData = [],
  aiOptimization,
}: AdvancedSEOProps) => {
  const defaultDomain = "https://innovatio-pro.com";

  // Enhanced meta tags for AI understanding
  const generateAIOptimizedMeta = () => {
    if (!aiOptimization) return null;

    return [
      // Primary topic for AI understanding
      <meta
        key="ai-primary-topic"
        name="topic"
        content={aiOptimization.primaryTopic}
      />,

      // Entity type for better categorization
      <meta
        key="ai-entity-type"
        name="entity-type"
        content={aiOptimization.entityType}
      />,

      // Search intent optimization
      <meta
        key="ai-intent"
        name="search-intent"
        content={aiOptimization.targetIntent}
      />,

      // Expertise areas
      <meta
        key="ai-expertise"
        name="expertise"
        content={aiOptimization.expertise.join(", ")}
      />,

      // Secondary topics for contextual understanding
      <meta
        key="ai-secondary-topics"
        name="secondary-topics"
        content={aiOptimization.secondaryTopics.join(", ")}
      />,
    ];
  };

  // Enhanced structured data for AI crawlers
  const generateEnhancedStructuredData = () => {
    const baseStructuredData = [
      // Website schema
      {
        "@context": "https://schema.org",
        "@type": "WebSite",
        name: "Innovatio",
        url: defaultDomain,
        potentialAction: {
          "@type": "SearchAction",
          target: `${defaultDomain}/search?q={search_term_string}`,
          "query-input": "required name=search_term_string",
        },
        inLanguage: locale,
      },

      // Organization schema
      {
        "@context": "https://schema.org",
        "@type": "Organization",
        name: "Innovatio",
        legalName: "Innovatio Digital Solutions",
        url: defaultDomain,
        logo: `${defaultDomain}/images/logo.png`,
        description:
          "Leading digital solutions company specializing in web and mobile application development with AI integration",
        sameAs: [
          "https://linkedin.com/company/innovatio",
          "https://github.com/innovatio",
        ],
        foundingDate: "2025",
        numberOfEmployees: "10-50",
        slogan: "Transforming Businesses Through Digital Innovation",
        knowsAbout: [
          "Web Development",
          "Mobile App Development",
          "AI Integration",
          "Digital Transformation",
          "Next.js Development",
          "Flutter Development",
          "UX/UI Design",
        ],
        areaServed: "Worldwide",
        serviceArea: {
          "@type": "GeoCircle",
          geoMidpoint: {
            "@type": "GeoCoordinates",
            latitude: 50.1109,
            longitude: 8.6821,
          },
          geoRadius: "Global",
        },
        address: {
          "@type": "PostalAddress",
          addressCountry: "DE",
          addressRegion: "Hessen",
          addressLocality: "Frankfurt am Main",
        },
        contactPoint: {
          "@type": "ContactPoint",
          telephone: "+49-************",
          contactType: "customer service",
          availableLanguage: [
            "English",
            "German",
            "Russian",
            "Turkish",
            "Arabic",
          ],
          email: "<EMAIL>",
        },
      },
    ];

    if (aiOptimization) {
      // Service-specific schema based on AI optimization
      baseStructuredData.push({
        "@context": "https://schema.org",
        "@type": "Service",
        name: aiOptimization.primaryTopic,
        description: description,
        provider: {
          "@type": "Organization",
          name: "Innovatio",
        },
        areaServed: "Worldwide",
        category: aiOptimization.secondaryTopics.join(", "),
        hasOfferCatalog: {
          "@type": "OfferCatalog",
          name: "Digital Solutions Services",
          itemListElement: aiOptimization.expertise.map((expertise) => ({
            "@type": "Offer",
            itemOffered: {
              "@type": "Service",
              name: expertise,
            },
          })),
        },
      });
    }

    return [...baseStructuredData, ...structuredData];
  };

  return (
    <Head>
      {/* AI-optimized meta tags */}
      {generateAIOptimizedMeta()}

      {/* Enhanced robots for AI crawlers */}
      <meta
        name="robots"
        content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1"
      />
      <meta name="googlebot" content="index, follow, max-image-preview:large" />
      <meta name="bingbot" content="index, follow, max-image-preview:large" />

      {/* AI-friendly content categorization */}
      <meta name="content-type" content="text/html; charset=utf-8" />
      <meta name="language" content={locale} />
      <meta name="revisit-after" content="7 days" />
      <meta name="content-language" content={locale} />

      {/* Enhanced OpenGraph for AI understanding */}
      {openGraph && (
        <>
          <meta property="og:type" content={openGraph.type || "website"} />
          <meta property="og:site_name" content="Innovatio" />
          <meta property="og:locale" content={locale} />
          {alternateLanguages.map(({ locale: altLocale, href }) => (
            <meta
              key={altLocale}
              property="og:locale:alternate"
              content={altLocale}
            />
          ))}
        </>
      )}

      {/* AI-optimized article tags */}
      <meta name="article:publisher" content="Innovatio" />
      <meta name="article:author" content="Innovatio Team" />

      {/* Enhanced structured data */}
      {generateEnhancedStructuredData().map((data, index) => (
        <script
          key={index}
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(data) }}
        />
      ))}

      {/* AI crawler hints */}
      <meta name="format-detection" content="telephone=no" />
      <meta name="msapplication-TileColor" content="#3b82f6" />
      <meta name="theme-color" content="#3b82f6" />

      {/* Preconnect to external domains for performance */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link
        rel="preconnect"
        href="https://fonts.gstatic.com"
        crossOrigin="anonymous"
      />
      <link rel="preconnect" href="https://images.unsplash.com" />

      {/* DNS prefetch for AI crawlers */}
      <link rel="dns-prefetch" href="https://www.google-analytics.com" />
      <link rel="dns-prefetch" href="https://www.googletagmanager.com" />
    </Head>
  );
};

export default AdvancedSEO;
