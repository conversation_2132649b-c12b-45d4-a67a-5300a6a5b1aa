'use client'

import React, { useEffect, useState } from 'react'
import { useCMS } from '@/providers/CMSProvider'
import { useI18n } from '@/providers/I18nProvider'
import { HeroContent, ServiceContent, PortfolioItem, Testimonial, FAQ } from '@/lib/cms/types'
import { Loader2 } from 'lucide-react'

interface CMSContentProps {
  contentType: 'hero' | 'services' | 'portfolio' | 'testimonials' | 'faqs'
  category?: string
  render: (data: any) => React.ReactNode
  fallback?: React.ReactNode
}

export function CMSContent({
  contentType,
  category,
  render,
  fallback,
}: CMSContentProps) {
  const { adapter, currentLocale } = useCMS()
  const { t } = useI18n()
  const [content, setContent] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const loadContent = async () => {
      if (!adapter) return
      
      setIsLoading(true)
      setError(null)
      
      try {
        let data: any = null
        
        switch (contentType) {
          case 'hero':
            data = await adapter.getHeroContent(currentLocale)
            break
          case 'services':
            data = await adapter.getServices(currentLocale)
            break
          case 'portfolio':
            data = await adapter.getPortfolioItems(currentLocale, category)
            break
          case 'testimonials':
            data = await adapter.getTestimonials(currentLocale)
            break
          case 'faqs':
            data = await adapter.getFAQs(currentLocale, category)
            break
          default:
            throw new Error(`Unbekannter Content-Typ: ${contentType}`)
        }
        
        setContent(data)
      } catch (err) {
        console.error(`Fehler beim Laden von ${contentType}:`, err)
        setError(err instanceof Error ? err.message : 'Unbekannter Fehler')
      } finally {
        setIsLoading(false)
      }
    }
    
    loadContent()
  }, [adapter, contentType, currentLocale, category])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-6 w-6 animate-spin text-primary" />
        <span className="ml-2">{t('Lade Inhalte...')}</span>
      </div>
    )
  }

  if (error) {
    console.error(`Fehler beim Laden von ${contentType}:`, error)
    return fallback || null
  }

  if (!content) {
    return fallback || null
  }

  return render(content)
}
