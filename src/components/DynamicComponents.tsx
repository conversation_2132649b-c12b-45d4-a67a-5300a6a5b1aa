"use client";

import dynamic from "next/dynamic";

// Simple loading component
const LoadingSpinner = () => (
  <div className="flex items-center justify-center py-8">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
  </div>
);

// Keep only the section components that work well with dynamic imports
export const DynamicTrustedBySection = dynamic(
  () => import("@/components/sections/TrustedBySection"),
  {
    loading: () => <LoadingSpinner />,
    ssr: true,
  }
);

export const DynamicSolutionsPortfolioSection = dynamic(
  () =>
    import("@/components/sections/SolutionsPortfolioSection").then((mod) => ({
      default: mod.SolutionsPortfolioSection,
    })),
  {
    loading: () => <LoadingSpinner />,
    ssr: true,
  }
);

export const DynamicServiceSection = dynamic(
  () =>
    import("@/components/sections/ServiceSection").then((mod) => ({
      default: mod.ServiceSection,
    })),
  {
    loading: () => <LoadingSpinner />,
    ssr: true,
  }
);

export const DynamicAboutSection = dynamic(
  () =>
    import("@/components/sections/AboutSection").then((mod) => ({
      default: mod.AboutSection,
    })),
  {
    loading: () => <LoadingSpinner />,
    ssr: true,
  }
);

export const DynamicFooterSection = dynamic(
  () =>
    import("@/components/sections/FooterSection").then((mod) => ({
      default: mod.FooterSection,
    })),
  {
    loading: () => <LoadingSpinner />,
    ssr: true,
  }
);

export const DynamicTestimonialsSection = dynamic(
  () =>
    import("@/components/sections/TestimonialsSection").then((mod) => ({
      default: mod.TestimonialsSection,
    })),
  {
    loading: () => <LoadingSpinner />,
    ssr: false,
  }
);

export const DynamicPricingSectionWrapper = dynamic(
  () => import("@/components/pricing/PricingSectionWrapper"),
  {
    loading: () => <LoadingSpinner />,
    ssr: false,
  }
);

export const DynamicContactSection = dynamic(
  () =>
    import("@/components/sections/ContactSection").then((mod) => ({
      default: mod.ContactSection,
    })),
  {
    loading: () => <LoadingSpinner />,
    ssr: false,
  }
);

// Note: FloatingContact and AIContentOptimizer are now handled in DynamicComponentWrapper
// with regular imports and client-side only rendering to avoid webpack module loading issues

// Utility functions
export const preloadCriticalComponents = () => {};
export const preloadNonCriticalComponents = () => {};
