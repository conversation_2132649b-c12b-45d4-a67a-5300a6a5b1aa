'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { useTheme } from 'next-themes'
import { motion, AnimatePresence } from 'framer-motion'
import { usePathname } from "next/navigation";
import { useI18n, type Locale } from "@/providers/I18nProvider";
import { NavigationLink } from "@/types";
import { LanguageSelector } from "../ui/LanguageSelector";
import { Menu, X } from "lucide-react";

// Updated navigation links with translation keys
const defaultNavigationLinks = [
  { key: "home", href: "#hero" },
  { key: "services", href: "#services" },
  { key: "solutions", href: "#solutions-portfolio" },
  { key: "about", href: "#about" },
  { key: "prices", href: "#prices" },
  { key: "testimonials", href: "#testimonials" },
  { key: "contact", href: "#contact" },
];

interface HeaderProps {
  navigation?: NavigationLink[];
}

export function SimpleHeader({
  navigation = defaultNavigationLinks as NavigationLink[],
}: HeaderProps) {
  const [scrolled, setScrolled] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const { theme, setTheme } = useTheme();
  const { locale, setLocale, t, dir, loading } = useI18n();
  const pathname = usePathname();
  const supportedLocales = ["en", "de", "ru", "tr", "ar"];

  // Toggle theme function
  const toggleTheme = () => {
    setTheme(theme === "dark" ? "light" : "dark");
  };

  // Check if we're in dark mode
  const isDarkMode = theme === "dark";

  // RTL support
  const rtlClass = dir === "rtl" ? "rtl-layout" : "";

  // Toggle mobile menu
  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  // Close mobile menu when clicking on a link
  const handleLinkClick = () => {
    setMobileMenuOpen(false);
  };

  // Generate proper href with locale
  const getHrefWithLocale = (href: string) => {
    if (href.startsWith("#")) {
      // For anchor links, check if we're on a page that's not the homepage
      const isOnHomepage =
        pathname === `/${locale}` ||
        pathname === `/${locale}/` ||
        pathname === "/" ||
        pathname === "";

      if (!isOnHomepage) {
        // Navigate to homepage with anchor
        return `/${locale}${href}`;
      }

      return href; // Stay on current page with anchor
    }
    return `/${locale}${href}`; // Add locale prefix for page routes
  };

  // Handle scroll events to change header appearance with throttling
  useEffect(() => {
    let ticking = false;

    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          const offset = window.scrollY;
          setScrolled(offset > 10);
          ticking = false;
        });
        ticking = true;
      }
    };

    handleScroll();
    window.addEventListener("scroll", handleScroll, { passive: true });
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Wenn die Übersetzungen noch geladen werden, zeigen wir nichts an
  if (loading) {
    return null;
  }

  // Define consistent styling for the header - same dark background for both modes
  const headerStyle = "bg-[#11142b]";

  // Add blur effect when scrolled - optimize with will-change
  const headerEffects = scrolled ? "backdrop-blur-lg" : "";
  const headerTransform = scrolled ? "will-change-transform" : "";

  // Text color is always white for better contrast against dark background
  const textColor = "text-white";

  return (
    <header
      className={`fixed top-0 left-0 right-0 z-[100] transition-all duration-300 ${headerStyle} ${headerEffects} ${textColor} ${rtlClass} ${headerTransform} py-1`}
      style={{ transform: "translateZ(0)" }}
    >
      <div className="container mx-auto px-4 flex items-center justify-between h-16">
        <div className="flex items-center">
          <Link href={`/${locale}/#hero`} className="flex items-center gap-2">
            {/* Always show logo and company name */}
            <motion.div
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3 }}
              className="flex items-center gap-1"
            >
              <Image
                src="/images/w_company_logo.png"
                alt="Innovatio Logo"
                width={24}
                height={24}
                priority
                className="h-auto"
              />
              <span className="font-semibold text-white text-base">
                Innovatio-Pro
              </span>
            </motion.div>
          </Link>
        </div>

        {/* Mobile Menu Button */}
        <div className="md:hidden">
          <motion.button
            onClick={toggleMobileMenu}
            className="relative flex items-center justify-center p-3 rounded-xl bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20 transition-all duration-300 shadow-lg"
            aria-label="Toggle mobile menu"
            whileTap={{ scale: 0.95 }}
            whileHover={{ scale: 1.05 }}
          >
            <motion.div
              animate={mobileMenuOpen ? { rotate: 180 } : { rotate: 0 }}
              transition={{ duration: 0.3 }}
            >
              {mobileMenuOpen ? (
                <X className="w-6 h-6 text-white" />
              ) : (
                <Menu className="w-6 h-6 text-white" />
              )}
            </motion.div>
          </motion.button>
        </div>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center gap-4">
          {navigation.map((link) => (
            <Link
              key={link.key || ""}
              href={getHrefWithLocale(link.href || "#")}
              className="text-white text-sm hover:text-gray-200 transition-colors"
            >
              {t(`nav.${link.key || ""}`) ||
                (link.key
                  ? link.key.charAt(0).toUpperCase() + link.key.slice(1)
                  : "Link")}
            </Link>
          ))}

          {/* Theme toggle button */}
          <button
            onClick={toggleTheme}
            className="flex items-center justify-center p-2 rounded-full hover:bg-white/10 transition-colors"
            aria-label={
              isDarkMode ? "Switch to light mode" : "Switch to dark mode"
            }
          >
            {isDarkMode ? (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="w-5 h-5 text-white"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M12 3v2.25m6.364.386l-1.591 1.591M21 12h-2.25m-.386 6.364l-1.591-1.591M12 18.75V21m-4.773-4.227l-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0z"
                />
              </svg>
            ) : (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="w-5 h-5 text-white"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M21.752 15.002A9.718 9.718 0 0118 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 003 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 009.002-5.998z"
                />
              </svg>
            )}
          </button>

          <LanguageSelector
            currentLocale={locale}
            locales={supportedLocales}
            onChange={(code) => setLocale(code as Locale)}
            className="text-white"
          />
        </nav>
      </div>

      {/* Enhanced Mobile Menu Dropdown */}
      <AnimatePresence>
        {mobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0, y: -20, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -20, scale: 0.95 }}
            transition={{ duration: 0.4, ease: "easeOut" }}
            className="md:hidden absolute top-full left-0 right-0 z-50"
          >
            <div className="mx-4 mt-2 bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 dark:border-gray-700/30 overflow-hidden">
              {/* Navigation Links */}
              <div className="py-4">
                {navigation.map((link, index) => (
                  <motion.div
                    key={link.key || ""}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1, duration: 0.3 }}
                  >
                    <Link
                      href={getHrefWithLocale(link.href || "#")}
                      className="group flex items-center px-6 py-4 text-gray-800 dark:text-gray-200 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-300 border-r-4 border-transparent hover:border-blue-500"
                      onClick={handleLinkClick}
                    >
                      <div className="flex items-center justify-between w-full">
                        <motion.div
                          className="opacity-0 group-hover:opacity-100 transition-opacity"
                          whileHover={{ x: -5 }}
                        >
                          <svg className="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                          </svg>
                        </motion.div>
                        <span className="font-medium text-lg group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors text-right">
                          {t(`nav.${link.key || ""}`) ||
                            (link.key
                              ? link.key.charAt(0).toUpperCase() + link.key.slice(1)
                              : "Link")}
                        </span>
                      </div>
                    </Link>
                  </motion.div>
                ))}
              </div>

              {/* Divider */}
              <div className="border-t border-gray-200 dark:border-gray-700"></div>

              {/* Controls Section */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3, duration: 0.3 }}
                className="p-6 bg-gray-50/50 dark:bg-gray-800/50"
              >
                <div className="flex items-center justify-between">
                  {/* Theme Toggle */}
                  <motion.button
                    onClick={toggleTheme}
                    className="flex items-center gap-3 px-4 py-3 rounded-xl bg-white dark:bg-gray-700 shadow-md hover:shadow-lg transition-all duration-300 border border-gray-200 dark:border-gray-600"
                    aria-label={isDarkMode ? "Switch to light mode" : "Switch to dark mode"}
                    whileTap={{ scale: 0.95 }}
                    whileHover={{ scale: 1.02 }}
                  >
                    <motion.div
                      animate={{ rotate: isDarkMode ? 180 : 0 }}
                      transition={{ duration: 0.5 }}
                    >
                      {isDarkMode ? (
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                          strokeWidth={1.5}
                          stroke="currentColor"
                          className="w-5 h-5 text-yellow-500"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            d="M12 3v2.25m6.364.386l-1.591 1.591M21 12h-2.25m-.386 6.364l-1.591-1.591M12 18.75V21m-4.773-4.227l-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0z"
                          />
                        </svg>
                      ) : (
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                          strokeWidth={1.5}
                          stroke="currentColor"
                          className="w-5 h-5 text-blue-600"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            d="M21.752 15.002A9.718 9.718 0 0118 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 003 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 009.002-5.998z"
                          />
                        </svg>
                      )}
                    </motion.div>
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      {isDarkMode ? "Light" : "Dark"}
                    </span>
                  </motion.button>

                  {/* Language Selector */}
                  <div className="flex items-center">
                    <LanguageSelector
                      currentLocale={locale}
                      locales={supportedLocales}
                      onChange={(code) => setLocale(code as Locale)}
                      className="text-gray-700 dark:text-gray-300"
                    />
                  </div>
                </div>

                {/* Additional Info */}
                <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
                  <div className="flex items-center justify-center gap-2 text-xs text-gray-500 dark:text-gray-400">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    <span>Innovatio-Pro • Ready to help</span>
                  </div>
                </div>
              </motion.div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </header>
  );
}
