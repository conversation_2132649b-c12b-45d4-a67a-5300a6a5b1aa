'use client'

import React, { useState, useEffect } from 'react'
import { useCMS } from '@/providers/CMSProvider'
import { useI18n } from '@/providers/I18nProvider'
import { Button } from '@/components/ui/Button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/Tabs'
import { CMSType, Locale } from '@/lib/cms/types'
import { HeroEditor } from './editors/HeroEditor'
import { ServicesEditor } from './editors/ServicesEditor'
import { PortfolioEditor } from './editors/PortfolioEditor'
import { PricingEditor } from './editors/PricingEditor'
import { TestimonialsEditor } from './editors/TestimonialsEditor'
import { FAQEditor } from './editors/FAQEditor'
import { SettingsEditor } from './editors/SettingsEditor'
import { MediaLibrary } from './MediaLibrary'
import { Loader2, Settings, LogOut } from 'lucide-react'

export function AdminPanel() {
  const { adapter, isLoading, error, setAdapter, currentLocale, setCurrentLocale } = useCMS()
  const { t } = useI18n()
  const [activeTab, setActiveTab] = useState('hero')
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const [loginForm, setLoginForm] = useState({ email: '', password: '' })

  // Prüfen, ob der Benutzer angemeldet ist
  useEffect(() => {
    const checkLogin = async () => {
      if (adapter) {
        const user = await adapter.getCurrentUser()
        setIsLoggedIn(!!user)
      }
    }
    
    checkLogin()
  }, [adapter])

  // Anmelden
  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!adapter) return
    
    try {
      await adapter.login(loginForm.email, loginForm.password)
      setIsLoggedIn(true)
    } catch (error) {
      console.error('Login failed:', error)
      alert(t('Login fehlgeschlagen'))
    }
  }

  // Abmelden
  const handleLogout = async () => {
    if (!adapter) return
    
    try {
      await adapter.logout()
      setIsLoggedIn(false)
    } catch (error) {
      console.error('Logout failed:', error)
    }
  }

  // CMS-Typ ändern
  const handleCMSTypeChange = (type: CMSType) => {
    setAdapter(type)
  }

  // Sprache ändern
  const handleLocaleChange = (locale: Locale) => {
    setCurrentLocale(locale)
  }

  // Wenn noch geladen wird
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2 text-lg">{t('Lade CMS...')}</span>
      </div>
    )
  }

  // Wenn ein Fehler aufgetreten ist
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <div className="bg-red-50 dark:bg-red-900/20 text-red-800 dark:text-red-200 p-4 rounded-lg mb-4">
          <h3 className="text-lg font-semibold mb-2">{t('Fehler beim Laden des CMS')}</h3>
          <p>{error}</p>
        </div>
        <Button onClick={() => setAdapter('local')}>{t('Erneut versuchen')}</Button>
      </div>
    )
  }

  // Wenn nicht angemeldet
  if (!isLoggedIn) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <div className="w-full max-w-md bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h2 className="text-2xl font-bold mb-6 text-center">{t('CMS Admin Login')}</h2>
          
          <form onSubmit={handleLogin} className="space-y-4">
            <div>
              <label htmlFor="email" className="block text-sm font-medium mb-1">
                {t('E-Mail')}
              </label>
              <input
                type="email"
                id="email"
                value={loginForm.email}
                onChange={(e) => setLoginForm({ ...loginForm, email: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md"
                required
              />
            </div>
            
            <div>
              <label htmlFor="password" className="block text-sm font-medium mb-1">
                {t('Passwort')}
              </label>
              <input
                type="password"
                id="password"
                value={loginForm.password}
                onChange={(e) => setLoginForm({ ...loginForm, password: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md"
                required
              />
            </div>
            
            <div>
              <Button type="submit" className="w-full">{t('Anmelden')}</Button>
            </div>
          </form>
          
          <div className="mt-6">
            <p className="text-sm text-gray-500 dark:text-gray-400 text-center">
              {t('Für den lokalen CMS-Adapter können Sie beliebige Anmeldedaten verwenden.')}
            </p>
          </div>
        </div>
      </div>
    )
  }

  // Hauptansicht des Admin-Panels
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex justify-between items-center">
            <h1 className="text-xl font-bold text-gray-900 dark:text-white">
              {t('CMS Admin')}
            </h1>
            
            <div className="flex items-center space-x-4">
              {/* CMS-Typ-Auswahl */}
              <select
                value={adapter?.getType() || 'local'}
                onChange={(e) => handleCMSTypeChange(e.target.value as CMSType)}
                className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-sm"
              >
                <option value="local">{t('Lokales CMS')}</option>
                <option value="sanity" disabled>{t('Sanity CMS')}</option>
                <option value="contentful" disabled>{t('Contentful')}</option>
                <option value="strapi" disabled>{t('Strapi')}</option>
              </select>
              
              {/* Sprach-Auswahl */}
              <select
                value={currentLocale}
                onChange={(e) => handleLocaleChange(e.target.value as Locale)}
                className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-sm"
              >
                <option value="de">Deutsch</option>
                <option value="en">English</option>
                <option value="ru">Русский</option>
                <option value="ar">العربية</option>
                <option value="tr">Türkçe</option>
              </select>
              
              {/* Einstellungen-Button */}
              <Button variant="ghost" size="icon">
                <Settings className="h-5 w-5" />
              </Button>
              
              {/* Abmelden-Button */}
              <Button variant="ghost" size="icon" onClick={handleLogout}>
                <LogOut className="h-5 w-5" />
              </Button>
            </div>
          </div>
        </div>
      </header>
      
      {/* Hauptinhalt */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-8">
            <TabsTrigger value="hero">{t('Hero')}</TabsTrigger>
            <TabsTrigger value="services">{t('Dienstleistungen')}</TabsTrigger>
            <TabsTrigger value="portfolio">{t('Portfolio')}</TabsTrigger>
            <TabsTrigger value="pricing">{t('Preise')}</TabsTrigger>
            <TabsTrigger value="testimonials">{t('Testimonials')}</TabsTrigger>
            <TabsTrigger value="faq">{t('FAQ')}</TabsTrigger>
            <TabsTrigger value="settings">{t('Einstellungen')}</TabsTrigger>
            <TabsTrigger value="media">{t('Medien')}</TabsTrigger>
          </TabsList>
          
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <TabsContent value="hero">
              <HeroEditor />
            </TabsContent>
            
            <TabsContent value="services">
              <ServicesEditor />
            </TabsContent>
            
            <TabsContent value="portfolio">
              <PortfolioEditor />
            </TabsContent>
            
            <TabsContent value="pricing">
              <PricingEditor />
            </TabsContent>
            
            <TabsContent value="testimonials">
              <TestimonialsEditor />
            </TabsContent>
            
            <TabsContent value="faq">
              <FAQEditor />
            </TabsContent>
            
            <TabsContent value="settings">
              <SettingsEditor />
            </TabsContent>
            
            <TabsContent value="media">
              <MediaLibrary />
            </TabsContent>
          </div>
        </Tabs>
      </main>
    </div>
  )
}
