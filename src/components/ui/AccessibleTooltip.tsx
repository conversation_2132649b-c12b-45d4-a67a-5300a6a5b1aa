'use client'

import React, { useState, useRef, useEffect } from 'react'
import { createPortal } from 'react-dom'
import { cn } from '@/lib/utils'
import { useAccessibility } from '@/providers/AccessibilityProvider'

interface AccessibleTooltipProps {
  content: React.ReactNode
  children: React.ReactNode
  position?: 'top' | 'right' | 'bottom' | 'left'
  className?: string
  contentClassName?: string
  delay?: number
  showOnFocus?: boolean
  maxWidth?: number
  'aria-label'?: string
}

export function AccessibleTooltip({
  content,
  children,
  position = 'top',
  className,
  contentClassName,
  delay = 300,
  showOnFocus = true,
  maxWidth = 250,
  'aria-label': ariaLabel,
}: AccessibleTooltipProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [tooltipPosition, setTooltipPosition] = useState({ top: 0, left: 0 })
  const triggerRef = useRef<HTMLDivElement>(null)
  const tooltipRef = useRef<HTMLDivElement>(null)
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)
  const { settings } = useAccessibility()
  
  // Generiere eine eindeutige ID für ARIA-Attribute
  const id = React.useId()
  const tooltipId = `tooltip-${id}`
  
  // Positioniere den Tooltip
  const updateTooltipPosition = () => {
    if (!triggerRef.current || !tooltipRef.current) return
    
    const triggerRect = triggerRef.current.getBoundingClientRect()
    const tooltipRect = tooltipRef.current.getBoundingClientRect()
    
    let top = 0
    let left = 0
    
    switch (position) {
      case 'top':
        top = triggerRect.top - tooltipRect.height - 8
        left = triggerRect.left + (triggerRect.width / 2) - (tooltipRect.width / 2)
        break
      case 'right':
        top = triggerRect.top + (triggerRect.height / 2) - (tooltipRect.height / 2)
        left = triggerRect.right + 8
        break
      case 'bottom':
        top = triggerRect.bottom + 8
        left = triggerRect.left + (triggerRect.width / 2) - (tooltipRect.width / 2)
        break
      case 'left':
        top = triggerRect.top + (triggerRect.height / 2) - (tooltipRect.height / 2)
        left = triggerRect.left - tooltipRect.width - 8
        break
    }
    
    // Stelle sicher, dass der Tooltip im Viewport bleibt
    const padding = 10
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight
    
    // Horizontale Anpassung
    if (left < padding) {
      left = padding
    } else if (left + tooltipRect.width > viewportWidth - padding) {
      left = viewportWidth - tooltipRect.width - padding
    }
    
    // Vertikale Anpassung
    if (top < padding) {
      top = padding
    } else if (top + tooltipRect.height > viewportHeight - padding) {
      top = viewportHeight - tooltipRect.height - padding
    }
    
    setTooltipPosition({ top, left })
  }
  
  // Zeige den Tooltip an
  const showTooltip = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
    
    timeoutRef.current = setTimeout(() => {
      setIsVisible(true)
      // Warte auf das nächste Frame, um die Position zu aktualisieren
      requestAnimationFrame(updateTooltipPosition)
    }, delay)
  }
  
  // Verstecke den Tooltip
  const hideTooltip = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
      timeoutRef.current = null
    }
    
    setIsVisible(false)
  }
  
  // Aktualisiere die Position, wenn sich die Fenstergröße ändert
  useEffect(() => {
    if (isVisible) {
      const handleResize = () => {
        updateTooltipPosition()
      }
      
      window.addEventListener('resize', handleResize)
      window.addEventListener('scroll', handleResize)
      
      return () => {
        window.removeEventListener('resize', handleResize)
        window.removeEventListener('scroll', handleResize)
      }
    }
  }, [isVisible])
  
  // Bereinige den Timeout beim Unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])
  
  // Bestimme die ARIA-Attribute
  const ariaProps = ariaLabel
    ? { 'aria-label': ariaLabel }
    : { 'aria-describedby': isVisible ? tooltipId : undefined }
  
  return (
    <>
      <div
        ref={triggerRef}
        className={cn('inline-block', className)}
        onMouseEnter={showTooltip}
        onMouseLeave={hideTooltip}
        onFocus={showOnFocus ? showTooltip : undefined}
        onBlur={showOnFocus ? hideTooltip : undefined}
        {...ariaProps}
      >
        {children}
      </div>
      
      {isVisible && createPortal(
        <div
          ref={tooltipRef}
          id={tooltipId}
          role="tooltip"
          className={cn(
            'fixed z-50 px-2 py-1 text-sm rounded shadow-md',
            'bg-gray-800 text-white dark:bg-gray-700',
            settings.highContrast && 'bg-black text-white border border-white',
            contentClassName
          )}
          style={{
            top: `${tooltipPosition.top}px`,
            left: `${tooltipPosition.left}px`,
            maxWidth: `${maxWidth}px`,
          }}
        >
          {content}
        </div>,
        document.body
      )}
    </>
  )
}
