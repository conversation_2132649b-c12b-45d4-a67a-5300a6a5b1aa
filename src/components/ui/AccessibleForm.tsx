'use client'

import React, { useState } from 'react'
import { cn } from '@/lib/utils'
import { useAccessibility } from '@/providers/AccessibilityProvider'

interface FormFieldProps {
  id: string
  label: string
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'textarea'
  required?: boolean
  error?: string
  hint?: string
  className?: string
  value: string
  onChange: (value: string) => void
  placeholder?: string
  min?: number
  max?: number
  rows?: number
  autoComplete?: string
}

export function FormField({
  id,
  label,
  type = 'text',
  required = false,
  error,
  hint,
  className,
  value,
  onChange,
  placeholder,
  min,
  max,
  rows = 4,
  autoComplete,
}: FormFieldProps) {
  const [focused, setFocused] = useState(false)
  const { settings } = useAccessibility()
  
  // Generiere eindeutige IDs für ARIA-Attribute
  const hintId = `${id}-hint`
  const errorId = `${id}-error`
  
  // Bestimme die ARIA-Attribute
  const ariaProps = {
    'aria-required': required,
    'aria-invalid': !!error,
    'aria-describedby': [
      hint ? hintId : null,
      error ? errorId : null,
    ].filter(Boolean).join(' ') || undefined,
  }
  
  // Gemeinsame Eigenschaften für Input und Textarea
  const commonProps = {
    id,
    value,
    onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => onChange(e.target.value),
    onFocus: () => setFocused(true),
    onBlur: () => setFocused(false),
    placeholder,
    required,
    className: cn(
      'w-full px-3 py-2 border rounded-md transition-colors focus:outline-none focus:ring-2',
      error
        ? 'border-red-500 focus:border-red-500 focus:ring-red-200'
        : 'border-gray-300 dark:border-gray-600 focus:border-primary focus:ring-primary/20',
      settings.highContrast && 'border-white focus:border-yellow-400 focus:ring-yellow-400/50',
      className
    ),
    autoComplete,
    ...ariaProps,
  }
  
  return (
    <div className="mb-4">
      <label
        htmlFor={id}
        className={cn(
          'block text-sm font-medium mb-1',
          required && 'after:content-["*"] after:ml-0.5 after:text-red-500',
          error && 'text-red-500',
          settings.highContrast && 'text-white'
        )}
      >
        {label}
      </label>
      
      {type === 'textarea' ? (
        <textarea
          {...commonProps}
          rows={rows}
        />
      ) : (
        <input
          {...commonProps}
          type={type}
          min={min}
          max={max}
        />
      )}
      
      {hint && !error && (
        <p id={hintId} className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          {hint}
        </p>
      )}
      
      {error && (
        <p id={errorId} className="mt-1 text-sm text-red-500" role="alert">
          {error}
        </p>
      )}
    </div>
  )
}

interface AccessibleFormProps {
  onSubmit: (e: React.FormEvent) => void
  children: React.ReactNode
  className?: string
  id?: string
  'aria-label'?: string
  'aria-describedby'?: string
}

export function AccessibleForm({
  onSubmit,
  children,
  className,
  id,
  'aria-label': ariaLabel,
  'aria-describedby': ariaDescribedby,
}: AccessibleFormProps) {
  return (
    <form
      onSubmit={onSubmit}
      className={className}
      id={id}
      aria-label={ariaLabel}
      aria-describedby={ariaDescribedby}
      noValidate // Wir übernehmen die Validierung selbst für bessere Barrierefreiheit
    >
      {children}
    </form>
  )
}
