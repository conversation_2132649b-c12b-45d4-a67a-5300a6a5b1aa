"use client";

import React, {
  useRef,
  useEffect,
  useState,
  forwardRef,
  useImperativeHandle,
} from "react";
import { cn } from "@/lib/utils";

interface SignaturePadProps {
  className?: string;
  width?: number;
  height?: number;
  backgroundColor?: string;
  penColor?: string;
  onSignatureChange?: (isEmpty: boolean) => void;
}

export interface SignaturePadRef {
  clear: () => void;
  getSignatureData: () => string | null;
  isEmpty: () => boolean;
}

const SignaturePad = forwardRef<SignaturePadRef, SignaturePadProps>(
  (
    {
      className,
      width = 400,
      height = 200,
      backgroundColor = "#ffffff",
      penColor = "#000000",
      onSignatureChange,
    },
    ref
  ) => {
    const canvasRef = useRef<HTMLCanvasElement>(null);
    const [isDrawing, setIsDrawing] = useState(false);
    const [isEmpty, setIsEmpty] = useState(true);

    useImperativeHandle(ref, () => ({
      clear: () => {
        const canvas = canvasRef.current;
        if (!canvas) return;

        const ctx = canvas.getContext("2d");
        if (!ctx) return;

        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.fillStyle = backgroundColor;
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        setIsEmpty(true);
        onSignatureChange?.(true);
      },
      getSignatureData: () => {
        const canvas = canvasRef.current;
        if (!canvas || isEmpty) return null;

        return canvas.toDataURL("image/png");
      },
      isEmpty: () => isEmpty,
    }));

    useEffect(() => {
      const canvas = canvasRef.current;
      if (!canvas) return;

      const ctx = canvas.getContext("2d");
      if (!ctx) return;

      // Set canvas size
      canvas.width = width;
      canvas.height = height;

      // Set background
      ctx.fillStyle = backgroundColor;
      ctx.fillRect(0, 0, width, height);

      // Set pen properties
      ctx.strokeStyle = penColor;
      ctx.lineWidth = 2;
      ctx.lineCap = "round";
      ctx.lineJoin = "round";
    }, [width, height, backgroundColor, penColor]);

    const getEventPos = (
      e:
        | React.MouseEvent<HTMLCanvasElement>
        | React.TouchEvent<HTMLCanvasElement>
    ) => {
      const canvas = canvasRef.current;
      if (!canvas) return { x: 0, y: 0 };

      const rect = canvas.getBoundingClientRect();
      const scaleX = canvas.width / rect.width;
      const scaleY = canvas.height / rect.height;

      if ("touches" in e) {
        // Touch event
        const touch = e.touches[0] || e.changedTouches[0];
        return {
          x: (touch.clientX - rect.left) * scaleX,
          y: (touch.clientY - rect.top) * scaleY,
        };
      } else {
        // Mouse event
        return {
          x: (e.clientX - rect.left) * scaleX,
          y: (e.clientY - rect.top) * scaleY,
        };
      }
    };

    const startDrawing = (
      e:
        | React.MouseEvent<HTMLCanvasElement>
        | React.TouchEvent<HTMLCanvasElement>
    ) => {
      e.preventDefault();
      setIsDrawing(true);

      const { x, y } = getEventPos(e);
      const ctx = canvasRef.current?.getContext("2d");
      if (!ctx) return;

      ctx.beginPath();
      ctx.moveTo(x, y);
    };

    const draw = (
      e:
        | React.MouseEvent<HTMLCanvasElement>
        | React.TouchEvent<HTMLCanvasElement>
    ) => {
      e.preventDefault();
      if (!isDrawing) return;

      const { x, y } = getEventPos(e);
      const ctx = canvasRef.current?.getContext("2d");
      if (!ctx) return;

      ctx.lineTo(x, y);
      ctx.stroke();

      if (isEmpty) {
        setIsEmpty(false);
        onSignatureChange?.(false);
      }
    };

    const stopDrawing = () => {
      setIsDrawing(false);
    };

    return (
      <div
        className={cn(
          "inline-block border border-gray-300 rounded-lg overflow-hidden",
          className
        )}
      >
        <canvas
          ref={canvasRef}
          className="block touch-none cursor-crosshair"
          onMouseDown={startDrawing}
          onMouseMove={draw}
          onMouseUp={stopDrawing}
          onMouseLeave={stopDrawing}
          onTouchStart={startDrawing}
          onTouchMove={draw}
          onTouchEnd={stopDrawing}
          style={{
            width: "100%",
            height: "100%",
            maxWidth: `${width}px`,
            maxHeight: `${height}px`,
          }}
        />
      </div>
    );
  }
);

SignaturePad.displayName = "SignaturePad";

export { SignaturePad };
