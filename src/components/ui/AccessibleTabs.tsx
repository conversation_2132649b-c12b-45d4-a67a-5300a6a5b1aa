'use client'

import React, { useState, useEffect } from 'react'
import { cn } from '@/lib/utils'
import { useAccessibility } from '@/providers/AccessibilityProvider'

interface TabItem {
  id: string
  label: string
  content: React.ReactNode
  icon?: React.ReactNode
}

interface AccessibleTabsProps {
  tabs: TabItem[]
  defaultTabId?: string
  className?: string
  tabListClassName?: string
  tabClassName?: string
  activeTabClassName?: string
  tabPanelClassName?: string
  orientation?: 'horizontal' | 'vertical'
  onChange?: (tabId: string) => void
  'aria-label'?: string
}

export function AccessibleTabs({
  tabs,
  defaultTabId,
  className,
  tabListClassName,
  tabClassName,
  activeTabClassName,
  tabPanelClassName,
  orientation = 'horizontal',
  onChange,
  'aria-label': ariaLabel = 'Tabs',
}: AccessibleTabsProps) {
  // Verwende die erste Tab-ID als Standard, wenn keine angegeben ist
  const [activeTabId, setActiveTabId] = useState(defaultTabId || tabs[0]?.id)
  const { settings } = useAccessibility()
  
  // Aktualisiere den aktiven Tab, wenn sich defaultTabId ändert
  useEffect(() => {
    if (defaultTabId) {
      setActiveTabId(defaultTabId)
    }
  }, [defaultTabId])
  
  // Behandle Tab-Wechsel
  const handleTabChange = (tabId: string) => {
    setActiveTabId(tabId)
    if (onChange) {
      onChange(tabId)
    }
  }
  
  // Tastatursteuerung für die Tabs
  const handleKeyDown = (e: React.KeyboardEvent, tabId: string, index: number) => {
    let newIndex = -1
    
    if (orientation === 'horizontal') {
      // Horizontale Navigation
      if (e.key === 'ArrowLeft') {
        newIndex = index > 0 ? index - 1 : tabs.length - 1
      } else if (e.key === 'ArrowRight') {
        newIndex = index < tabs.length - 1 ? index + 1 : 0
      }
    } else {
      // Vertikale Navigation
      if (e.key === 'ArrowUp') {
        newIndex = index > 0 ? index - 1 : tabs.length - 1
      } else if (e.key === 'ArrowDown') {
        newIndex = index < tabs.length - 1 ? index + 1 : 0
      }
    }
    
    if (e.key === 'Home') {
      newIndex = 0
    } else if (e.key === 'End') {
      newIndex = tabs.length - 1
    }
    
    if (newIndex !== -1) {
      e.preventDefault()
      const newTabId = tabs[newIndex].id
      handleTabChange(newTabId)
      document.getElementById(`tab-${newTabId}`)?.focus()
    }
  }
  
  // Bestimme die Klassen basierend auf der Orientierung
  const containerClasses = cn(
    orientation === 'horizontal' ? 'flex flex-col' : 'flex flex-row',
    className
  )
  
  const tabListClasses = cn(
    'flex',
    orientation === 'horizontal' ? 'flex-row border-b border-gray-200 dark:border-gray-700' : 'flex-col border-r border-gray-200 dark:border-gray-700',
    tabListClassName
  )
  
  return (
    <div className={containerClasses}>
      {/* Tab-Liste */}
      <div
        className={tabListClasses}
        role="tablist"
        aria-label={ariaLabel}
        aria-orientation={orientation}
      >
        {tabs.map((tab, index) => (
          <button
            key={tab.id}
            id={`tab-${tab.id}`}
            role="tab"
            aria-selected={activeTabId === tab.id}
            aria-controls={`panel-${tab.id}`}
            tabIndex={activeTabId === tab.id ? 0 : -1}
            onClick={() => handleTabChange(tab.id)}
            onKeyDown={(e) => handleKeyDown(e, tab.id, index)}
            className={cn(
              'px-4 py-2 focus:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2',
              orientation === 'horizontal'
                ? 'border-b-2 -mb-px'
                : 'border-r-2 -mr-px',
              activeTabId === tab.id
                ? cn(
                    orientation === 'horizontal'
                      ? 'border-primary'
                      : 'border-primary',
                    'text-primary',
                    activeTabClassName
                  )
                : cn(
                    orientation === 'horizontal'
                      ? 'border-transparent'
                      : 'border-transparent',
                    'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'
                  ),
              settings.highContrast && activeTabId === tab.id && 'border-yellow-400 text-white',
              tabClassName
            )}
          >
            <div className="flex items-center">
              {tab.icon && <span className="mr-2">{tab.icon}</span>}
              {tab.label}
            </div>
          </button>
        ))}
      </div>
      
      {/* Tab-Panels */}
      <div className="flex-1">
        {tabs.map((tab) => (
          <div
            key={tab.id}
            id={`panel-${tab.id}`}
            role="tabpanel"
            aria-labelledby={`tab-${tab.id}`}
            tabIndex={0}
            hidden={activeTabId !== tab.id}
            className={cn(
              'p-4 focus:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2',
              tabPanelClassName
            )}
          >
            {tab.content}
          </div>
        ))}
      </div>
    </div>
  )
}
