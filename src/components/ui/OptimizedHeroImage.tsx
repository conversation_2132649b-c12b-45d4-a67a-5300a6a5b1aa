"use client";

import { useState } from "react";
import Image from "next/image";

interface OptimizedHeroImageProps {
  src: string;
  alt: string;
  priority?: boolean;
  className?: string;
  sizes?: string;
  onLoad?: () => void;
}

/**
 * Optimized Hero Image Component
 * Uses modern image formats (AVIF/WebP) with fallbacks for LCP optimization
 */
export function OptimizedHeroImage({
  src,
  alt,
  priority = false,
  className = "",
  sizes = "(max-width: 640px) 320px, (max-width: 1024px) 384px, 448px",
  onLoad,
}: OptimizedHeroImageProps) {
  const [imageLoaded, setImageLoaded] = useState(false);

  // Extract filename without extension and path
  const getOptimizedImageSources = (originalSrc: string) => {
    const pathParts = originalSrc.split("/");
    const filename = pathParts[pathParts.length - 1];
    const nameWithoutExt = filename.split(".")[0];
    const directory = pathParts[pathParts.length - 2]; // hero, companies, etc.

    return {
      avif: `/images/optimized/${directory}/${nameWithoutExt}.avif`,
      webp: `/images/optimized/${directory}/${nameWithoutExt}.webp`,
      jpeg: `/images/optimized/${directory}/${nameWithoutExt}.jpg`,
      original: originalSrc,
    };
  };

  const handleLoad = () => {
    setImageLoaded(true);
    onLoad?.();
  };

  const sources = getOptimizedImageSources(src);

  return (
    <div className={`relative ${className}`}>
      {/* Modern browsers with AVIF/WebP support */}
      <picture>
        <source srcSet={sources.avif} type="image/avif" />
        <source srcSet={sources.webp} type="image/webp" />
        <Image
          src={sources.jpeg}
          alt={alt}
          fill
          className={`object-cover object-top transition-opacity duration-300 ${
            imageLoaded ? "opacity-100" : "opacity-0"
          }`}
          sizes={sizes}
          priority={priority}
          onLoad={handleLoad}
          quality={75}
          // Preload hint for critical LCP images
          {...(priority && {
            rel: "preload",
            as: "image",
          })}
        />
      </picture>

      {/* Loading skeleton for non-loaded images */}
      {!imageLoaded && (
        <div className="absolute inset-0 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 animate-pulse rounded-lg" />
      )}
    </div>
  );
}
