"use client";

import { ErrorBoundary } from "@/components/ui/ErrorBoundary";
import { FloatingContact } from "@/components/ui/FloatingContact";
import AIContentOptimizer from "@/components/seo/AIContentOptimizer";
import { type Dictionary } from "@/lib/dictionary";
import { useEffect, useState } from "react";

interface DynamicComponentWrapperProps {
  dictionary: Dictionary["contact"];
  locale: string;
}

export function DynamicComponentWrapper({
  dictionary,
  locale,
}: DynamicComponentWrapperProps) {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Don't render anything on the server
  if (!isMounted) {
    return null;
  }

  return (
    <ErrorBoundary
      fallback={({ error, retry }) => (
        <div className="p-4 text-center">
          <p className="text-sm text-gray-500">
            Optional components failed to load
          </p>
          {process.env.NODE_ENV === "development" && (
            <details className="mt-2 text-xs text-gray-400">
              <summary>Error details (dev only)</summary>
              <pre className="mt-1 whitespace-pre-wrap">
                {error?.message || "Unknown error"}
              </pre>
            </details>
          )}
        </div>
      )}
      onError={(error, errorInfo) => {
        if (process.env.NODE_ENV === "development") {
          console.warn("Dynamic component error:", error, errorInfo);
        }
      }}
    >
      <FloatingContact dictionary={dictionary} />
      <AIContentOptimizer
        locale={locale}
        pageType="home"
        primaryKeywords={[
          "Web Development",
          "Mobile App Development",
          "Digital Solutions",
          "Next.js Development",
          "Flutter Development",
        ]}
        secondaryKeywords={[
          "AI Integration",
          "Digital Transformation",
          "UX/UI Design",
          "Custom Software",
          "Business Solutions",
        ]}
        contentCategory="Digital Solutions"
        entityType="Organization"
      />
    </ErrorBoundary>
  );
}
