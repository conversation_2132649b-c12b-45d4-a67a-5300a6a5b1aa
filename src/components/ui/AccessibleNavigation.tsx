'use client'

import React, { useState, useEffect, useRef } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import { useAccessibility } from '@/providers/AccessibilityProvider'
import { ChevronDown } from 'lucide-react'

interface NavigationItem {
  label: string
  href: string
  children?: NavigationItem[]
  icon?: React.ReactNode
}

interface AccessibleNavigationProps {
  items: NavigationItem[]
  className?: string
  itemClassName?: string
  activeItemClassName?: string
  orientation?: 'horizontal' | 'vertical'
  'aria-label'?: string
}

export function AccessibleNavigation({
  items,
  className,
  itemClassName,
  activeItemClassName,
  orientation = 'horizontal',
  'aria-label': ariaLabel = 'Navigation',
}: AccessibleNavigationProps) {
  const pathname = usePathname()
  const { settings } = useAccessibility()
  
  // Bestimme die Klassen basierend auf der Orientierung
  const navClasses = cn(
    'flex',
    orientation === 'horizontal' ? 'flex-row items-center' : 'flex-col',
    className
  )
  
  // Bestimme die Klassen für die Navigationselemente
  const baseItemClasses = cn(
    'relative px-3 py-2 transition-colors rounded-md focus:outline-none focus-visible:ring-2 focus-visible:ring-primary',
    itemClassName
  )
  
  return (
    <nav className={navClasses} aria-label={ariaLabel}>
      <ul className={cn('flex', orientation === 'horizontal' ? 'flex-row' : 'flex-col', 'w-full')}>
        {items.map((item, index) => (
          <NavigationItem
            key={index}
            item={item}
            isActive={pathname === item.href}
            baseItemClasses={baseItemClasses}
            activeItemClassName={activeItemClassName}
            orientation={orientation}
          />
        ))}
      </ul>
    </nav>
  )
}

interface NavigationItemProps {
  item: NavigationItem
  isActive: boolean
  baseItemClasses: string
  activeItemClassName?: string
  orientation: 'horizontal' | 'vertical'
}

function NavigationItem({
  item,
  isActive,
  baseItemClasses,
  activeItemClassName,
  orientation,
}: NavigationItemProps) {
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)
  
  // Schließe das Dropdown, wenn außerhalb geklickt wird
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }
    
    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])
  
  // Tastatursteuerung
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (item.children) {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault()
        setIsOpen(!isOpen)
      } else if (e.key === 'Escape' && isOpen) {
        setIsOpen(false)
      }
    }
  }
  
  // Bestimme die Klassen für das aktive Element
  const itemClasses = cn(
    baseItemClasses,
    isActive && (activeItemClassName || 'bg-primary/10 text-primary'),
    item.children && 'cursor-pointer'
  )
  
  return (
    <li className={cn('relative', orientation === 'horizontal' ? 'mx-1' : 'my-1')} ref={dropdownRef}>
      {item.children ? (
        // Element mit Dropdown
        <div>
          <button
            className={cn(itemClasses, 'flex items-center w-full text-left')}
            onClick={() => setIsOpen(!isOpen)}
            onKeyDown={handleKeyDown}
            aria-expanded={isOpen}
            aria-haspopup="true"
          >
            {item.icon && <span className="mr-2">{item.icon}</span>}
            <span>{item.label}</span>
            <ChevronDown
              className={cn(
                'ml-1 h-4 w-4 transition-transform',
                isOpen && 'transform rotate-180'
              )}
            />
          </button>
          
          {/* Dropdown-Menü */}
          {isOpen && (
            <div className="absolute z-10 mt-1 py-1 bg-white dark:bg-gray-800 rounded-md shadow-lg border border-gray-200 dark:border-gray-700 min-w-[200px]">
              <ul className="py-1">
                {item.children.map((child, childIndex) => (
                  <li key={childIndex}>
                    <Link
                      href={child.href}
                      className={cn(
                        'block px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus-visible:bg-gray-100 dark:focus-visible:bg-gray-700',
                        pathname === child.href && 'bg-primary/10 text-primary'
                      )}
                    >
                      {child.icon && <span className="mr-2">{child.icon}</span>}
                      {child.label}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      ) : (
        // Einfaches Element ohne Dropdown
        <Link
          href={item.href}
          className={itemClasses}
          aria-current={isActive ? 'page' : undefined}
        >
          {item.icon && <span className="mr-2">{item.icon}</span>}
          {item.label}
        </Link>
      )}
    </li>
  )
}
