'use client'

import React, { useState } from 'react'
import { cn } from '@/lib/utils'
import { useAccessibility } from '@/providers/AccessibilityProvider'
import { ChevronDown } from 'lucide-react'

interface AccordionItem {
  id: string
  title: React.ReactNode
  content: React.ReactNode
  icon?: React.ReactNode
}

interface AccessibleAccordionProps {
  items: AccordionItem[]
  defaultExpandedIds?: string[]
  allowMultiple?: boolean
  className?: string
  itemClassName?: string
  headerClassName?: string
  contentClassName?: string
  iconClassName?: string
  expandedIconClassName?: string
  'aria-label'?: string
}

export function AccessibleAccordion({
  items,
  defaultExpandedIds = [],
  allowMultiple = false,
  className,
  itemClassName,
  headerClassName,
  contentClassName,
  iconClassName,
  expandedIconClassName,
  'aria-label': ariaLabel,
}: AccessibleAccordionProps) {
  const [expandedIds, setExpandedIds] = useState<string[]>(defaultExpandedIds)
  const { settings } = useAccessibility()
  
  // Behandle das Umschalten eines Akkordeon-Elements
  const toggleItem = (id: string) => {
    if (expandedIds.includes(id)) {
      // Schließe das Element
      setExpandedIds(expandedIds.filter(itemId => itemId !== id))
    } else {
      // Öffne das Element
      if (allowMultiple) {
        setExpandedIds([...expandedIds, id])
      } else {
        setExpandedIds([id])
      }
    }
  }
  
  // Tastatursteuerung
  const handleKeyDown = (e: React.KeyboardEvent, id: string, index: number) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault()
      toggleItem(id)
    } else if (e.key === 'ArrowDown') {
      e.preventDefault()
      const nextIndex = (index + 1) % items.length
      document.getElementById(`accordion-header-${items[nextIndex].id}`)?.focus()
    } else if (e.key === 'ArrowUp') {
      e.preventDefault()
      const prevIndex = (index - 1 + items.length) % items.length
      document.getElementById(`accordion-header-${items[prevIndex].id}`)?.focus()
    } else if (e.key === 'Home') {
      e.preventDefault()
      document.getElementById(`accordion-header-${items[0].id}`)?.focus()
    } else if (e.key === 'End') {
      e.preventDefault()
      document.getElementById(`accordion-header-${items[items.length - 1].id}`)?.focus()
    }
  }
  
  return (
    <div
      className={cn('divide-y divide-gray-200 dark:divide-gray-700', className)}
      role="region"
      aria-label={ariaLabel}
    >
      {items.map((item, index) => {
        const isExpanded = expandedIds.includes(item.id)
        
        return (
          <div
            key={item.id}
            className={cn('border-b border-gray-200 dark:border-gray-700 last:border-b-0', itemClassName)}
          >
            <h3>
              <button
                id={`accordion-header-${item.id}`}
                className={cn(
                  'flex items-center justify-between w-full py-4 px-4 text-left focus:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2',
                  settings.highContrast && 'focus-visible:ring-yellow-400',
                  headerClassName
                )}
                onClick={() => toggleItem(item.id)}
                onKeyDown={(e) => handleKeyDown(e, item.id, index)}
                aria-expanded={isExpanded}
                aria-controls={`accordion-panel-${item.id}`}
              >
                <div className="flex items-center">
                  {item.icon && <span className="mr-3">{item.icon}</span>}
                  <span className="font-medium">{item.title}</span>
                </div>
                <ChevronDown
                  className={cn(
                    'h-5 w-5 transition-transform duration-200',
                    isExpanded ? 'transform rotate-180' : '',
                    isExpanded ? expandedIconClassName : iconClassName
                  )}
                />
              </button>
            </h3>
            
            <div
              id={`accordion-panel-${item.id}`}
              role="region"
              aria-labelledby={`accordion-header-${item.id}`}
              className={cn(
                'overflow-hidden transition-all duration-200',
                isExpanded ? 'max-h-96' : 'max-h-0',
                contentClassName
              )}
              hidden={!isExpanded}
            >
              <div className="p-4 pt-0">
                {item.content}
              </div>
            </div>
          </div>
        )
      })}
    </div>
  )
}
