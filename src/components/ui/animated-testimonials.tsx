"use client";

import { IconArrowLeft, IconArrowRight } from "@tabler/icons-react";
import { motion, AnimatePresence } from "framer-motion";
import Image from "next/image";
import { useEffect, useState } from "react";
import { cn } from "@/lib/utils";

type Testimonial = {
  quote: string;
  name: string;
  designation: string;
  src: string;
};

export const AnimatedTestimonials = ({
  testimonials,
  autoplay = false,
  className,
}: {
  testimonials: Testimonial[];
  autoplay?: boolean;
  className?: string;
}) => {
  const [active, setActive] = useState(0);
  const [isHovered, setIsHovered] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);

  const handleNext = () => {
    setActive((prev) => (prev + 1) % testimonials.length);
  };

  const handlePrev = () => {
    setActive((prev) => (prev - 1 + testimonials.length) % testimonials.length);
  };

  const isActive = (index: number) => {
    return index === active;
  };

  useEffect(() => {
    if (autoplay && !isHovered) {
      const interval = setInterval(handleNext, 8000);
      return () => clearInterval(interval);
    }
  }, [autoplay, isHovered]);

  const randomRotateY = (index: number) => {
    // Use a deterministic value based on index to avoid hydration mismatches
    const rotations = [-8, 6, -4, 7, -10, 5, -6, 8, -3, 9];
    return rotations[index % rotations.length];
  };

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  const shouldShowReadMore = (text: string) => text.length > 150;
  const displayText = (text: string) => {
    if (!shouldShowReadMore(text) || isExpanded) return text;
    return text.slice(0, 150) + "...";
  };

  return (
    <div
      className={cn(
        "max-w-sm md:max-w-5xl mx-auto px-4 md:px-8 lg:px-12 py-20",
        className
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="relative grid grid-cols-1 md:grid-cols-2 gap-20">
        <div>
          <div className="relative h-96 md:h-[500px] w-full z-2">
            <AnimatePresence>
              {testimonials.map((testimonial, index) => (
                <motion.div
                  key={testimonial.src}
                  initial={{
                    opacity: 0,
                    scale: 0.9,
                    z: -100,
                    rotate: randomRotateY(index),
                  }}
                  animate={{
                    opacity: isActive(index) ? 1 : 0.7,
                    scale: isActive(index) ? 1 : 0.95,
                    z: isActive(index) ? 0 : -100,
                    rotate: isActive(index) ? 0 : randomRotateY(index),
                    zIndex: isActive(index)
                      ? 999
                      : testimonials.length + 2 - index,
                    y: isActive(index) ? [0, -80, 0] : 0,
                  }}
                  exit={{
                    opacity: 0,
                    scale: 0.9,
                    z: 100,
                    rotate: randomRotateY(index),
                  }}
                  transition={{
                    duration: 0.4,
                    ease: "easeInOut",
                  }}
                  className="absolute inset-0 origin-bottom"
                >
                  <Image
                    src={testimonial.src}
                    alt={testimonial.name}
                    width={800}
                    height={800}
                    draggable={false}
                    className="h-full w-full rounded-3xl object-cover object-center shadow-xl"
                  />
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        </div>
        <div className="flex justify-between flex-col py-4">
          <motion.div
            key={active}
            initial={{
              y: 20,
              opacity: 0,
            }}
            animate={{
              y: 0,
              opacity: 1,
            }}
            exit={{
              y: -20,
              opacity: 0,
            }}
            transition={{
              duration: 0.2,
              ease: "easeInOut",
            }}
          >
            <h3 className="text-2xl font-bold text-foreground dark:text-white">
              {testimonials[active].name}
            </h3>
            <p className="text-sm text-muted-foreground dark:text-gray-400">
              {testimonials[active].designation}
            </p>
            <div className="mt-6 space-y-4">
              <motion.p className="text-lg text-muted-foreground dark:text-gray-300">
                {displayText(testimonials[active].quote)
                  .split(" ")
                  .map((word, index) => (
                    <motion.span
                      key={index}
                      initial={{
                        filter: "blur(10px)",
                        opacity: 0,
                        y: 5,
                      }}
                      animate={{
                        filter: "blur(0px)",
                        opacity: 1,
                        y: 0,
                      }}
                      transition={{
                        duration: 0.2,
                        ease: "easeInOut",
                        delay: 0.02 * index,
                      }}
                      className="inline-block"
                    >
                      {word}&nbsp;
                    </motion.span>
                  ))}
              </motion.p>
              {shouldShowReadMore(testimonials[active].quote) && (
                <button
                  onClick={toggleExpand}
                  className="text-primary dark:text-blue-400 hover:text-primary/80 dark:hover:text-blue-300 font-medium text-sm transition-colors focus:outline-none focus:ring-2 focus:ring-blue-400 dark:focus:ring-blue-300"
                >
                  {isExpanded ? "Read less" : "Read more"}
                </button>
              )}
            </div>
          </motion.div>
          <div className="flex gap-4 pt-24 md:pt-12">
            <button
              onClick={handlePrev}
              className="h-10 w-10 rounded-full bg-secondary dark:bg-blue-700 border border-gray-200 dark:border-blue-500 flex items-center justify-center group/button hover:bg-secondary/80 dark:hover:bg-blue-600 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-400 dark:focus:ring-blue-300"
            >
              <IconArrowLeft className="h-6 w-6 text-foreground dark:text-white group-hover/button:rotate-12 transition-transform duration-300" />
            </button>
            <button
              onClick={handleNext}
              className="h-10 w-10 rounded-full bg-secondary dark:bg-blue-700 border border-gray-200 dark:border-blue-500 flex items-center justify-center group/button hover:bg-secondary/80 dark:hover:bg-blue-600 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-400 dark:focus:ring-blue-300"
            >
              <IconArrowRight className="h-6 w-6 text-foreground dark:text-white group-hover/button:-rotate-12 transition-transform duration-300" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
 