'use client'

import React from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import { useAccessibility } from '@/providers/AccessibilityProvider'
import { ChevronRight, Home } from 'lucide-react'

interface BreadcrumbItem {
  label: string
  href: string
  icon?: React.ReactNode
}

interface AccessibleBreadcrumbsProps {
  items: BreadcrumbItem[]
  homeHref?: string
  className?: string
  itemClassName?: string
  activeItemClassName?: string
  separator?: React.ReactNode
  showHomeIcon?: boolean
  'aria-label'?: string
}

export function AccessibleBreadcrumbs({
  items,
  homeHref = '/',
  className,
  itemClassName,
  activeItemClassName,
  separator = <ChevronRight className="h-4 w-4 mx-2 text-gray-400" />,
  showHomeIcon = true,
  'aria-label': ariaLabel = 'Breadcrumbs',
}: AccessibleBreadcrumbsProps) {
  const pathname = usePathname()
  const { settings } = useAccessibility()
  
  // Füge das Home-Element hinzu, wenn showHomeIcon true ist
  const allItems = showHomeIcon
    ? [{ label: 'Home', href: homeHref, icon: <Home className="h-4 w-4" /> }, ...items]
    : items
  
  return (
    <nav aria-label={ariaLabel} className={cn('flex', className)}>
      <ol className="flex items-center flex-wrap">
        {allItems.map((item, index) => {
          const isLast = index === allItems.length - 1
          const isActive = pathname === item.href || (isLast && !pathname.startsWith(item.href))
          
          return (
            <li
              key={index}
              className={cn(
                'flex items-center',
                itemClassName
              )}
            >
              {index > 0 && (
                <span className="flex-shrink-0" aria-hidden="true">
                  {separator}
                </span>
              )}
              
              {isLast ? (
                <span
                  className={cn(
                    'font-medium',
                    isActive && (activeItemClassName || 'text-primary'),
                    settings.highContrast && 'text-white'
                  )}
                  aria-current="page"
                >
                  {item.icon && (
                    <span className="mr-1">{item.icon}</span>
                  )}
                  {item.label}
                </span>
              ) : (
                <Link
                  href={item.href}
                  className={cn(
                    'hover:text-primary focus:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 rounded',
                    isActive && (activeItemClassName || 'text-primary'),
                    settings.highContrast && 'text-white hover:text-yellow-400 focus-visible:ring-yellow-400'
                  )}
                >
                  <span className="flex items-center">
                    {item.icon && (
                      <span className="mr-1">{item.icon}</span>
                    )}
                    {item.label}
                  </span>
                </Link>
              )}
            </li>
          )
        })}
      </ol>
    </nav>
  )
}
