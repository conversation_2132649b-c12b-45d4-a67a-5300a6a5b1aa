'use client'

import { Fragment } from 'react'
import { Menu, Transition } from '@headlessui/react'
import { GlobeAltIcon } from '@heroicons/react/24/outline'
import { cn } from '@/lib/utils'

interface LanguageSelectorProps {
  currentLocale: string
  locales: string[]
  onChange: (locale: string) => void
  className?: string
}

export function LanguageSelector({ currentLocale, locales, onChange, className }: LanguageSelectorProps) {
  return (
    <Menu as="div" className="relative">
      <Menu.Button className={cn(
        "flex items-center gap-2 rounded-md p-2 hover:bg-white/10",
        className
      )}>
        <GlobeAltIcon className="h-5 w-5" />
      </Menu.Button>
      <Transition
        as={Fragment}
        enter="transition ease-out duration-100"
        enterFrom="transform opacity-0 scale-95"
        enterTo="transform opacity-100 scale-100"
        leave="transition ease-in duration-75"
        leaveFrom="transform opacity-100 scale-100"
        leaveTo="transform opacity-0 scale-95"
      >
        <Menu.Items className="absolute right-0 bottom-full mb-2 md:bottom-auto md:top-full md:mt-2 w-32 origin-bottom-right md:origin-top-right rounded-md bg-gray-900/90 py-1 shadow-lg ring-1 ring-white/10 focus:outline-none backdrop-blur-sm z-50">
          {locales.map((locale) => (
            <Menu.Item key={locale}>
              {({ active }) => (
                <button
                  onClick={() => onChange(locale)}
                  className={cn(
                    active ? 'bg-white/10' : '',
                    locale === currentLocale ? 'text-white' : 'text-white/70',
                    'block w-full px-4 py-2 text-left text-sm'
                  )}
                >
                  {locale.toUpperCase()}
                </button>
              )}
            </Menu.Item>
          ))}
        </Menu.Items>
      </Transition>
    </Menu>
  )
}