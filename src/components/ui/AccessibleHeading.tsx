'use client'

import React from 'react'
import { cn } from '@/lib/utils'

interface AccessibleHeadingProps {
  level: 1 | 2 | 3 | 4 | 5 | 6
  children: React.ReactNode
  className?: string
  id?: string
  visualLevel?: 1 | 2 | 3 | 4 | 5 | 6 // Visuelles Level (kann vom semantischen Level abweichen)
  'aria-label'?: string
}

export function AccessibleHeading({
  level,
  children,
  className,
  id,
  visualLevel,
  'aria-label': ariaLabel,
  ...props
}: AccessibleHeadingProps) {
  // Bestimme das visuelle Level (falls angegeben, sonst das semantische Level)
  const visualHeadingLevel = visualLevel || level
  
  // Klassen basierend auf dem visuellen Level
  const headingClasses = {
    1: 'text-4xl sm:text-5xl font-bold',
    2: 'text-3xl sm:text-4xl font-bold',
    3: 'text-2xl sm:text-3xl font-semibold',
    4: 'text-xl sm:text-2xl font-semibold',
    5: 'text-lg sm:text-xl font-medium',
    6: 'text-base sm:text-lg font-medium',
  }
  
  // Kombinierte Klassen
  const combinedClassName = cn(headingClasses[visualHeadingLevel], className)
  
  // Rendere das entsprechende Heading-Element basierend auf dem semantischen Level
  const HeadingTag = `h${level}` as keyof JSX.IntrinsicElements
  
  return (
    <HeadingTag
      id={id}
      className={combinedClassName}
      aria-label={ariaLabel}
      {...props}
    >
      {children}
    </HeadingTag>
  )
}
