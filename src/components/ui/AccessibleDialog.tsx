'use client'

import React, { useEffect, useRef } from 'react'
import { createPortal } from 'react-dom'
import { cn } from '@/lib/utils'
import { useAccessibility } from '@/providers/AccessibilityProvider'
import { X } from 'lucide-react'

interface AccessibleDialogProps {
  isOpen: boolean
  onClose: () => void
  title: string
  children: React.ReactNode
  className?: string
  contentClassName?: string
  titleClassName?: string
  closeButtonClassName?: string
  closeOnEsc?: boolean
  closeOnOutsideClick?: boolean
  initialFocusRef?: React.RefObject<HTMLElement>
  'aria-describedby'?: string
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
}

export function AccessibleDialog({
  isOpen,
  onClose,
  title,
  children,
  className,
  contentClassName,
  titleClassName,
  closeButtonClassName,
  closeOnEsc = true,
  closeOnOutsideClick = true,
  initialFocusRef,
  'aria-describedby': ariaDescribedby,
  size = 'md',
}: AccessibleDialogProps) {
  const { settings } = useAccessibility()
  const dialogRef = useRef<HTMLDivElement>(null)
  const previousFocusRef = useRef<HTMLElement | null>(null)
  
  // Speichere das vorherige fokussierte Element, wenn der Dialog geöffnet wird
  useEffect(() => {
    if (isOpen) {
      previousFocusRef.current = document.activeElement as HTMLElement
    }
  }, [isOpen])
  
  // Setze den Fokus auf das angegebene Element oder den Dialog selbst
  useEffect(() => {
    if (isOpen) {
      const focusElement = initialFocusRef?.current || dialogRef.current
      setTimeout(() => {
        focusElement?.focus()
      }, 100)
    } else if (previousFocusRef.current) {
      // Setze den Fokus zurück, wenn der Dialog geschlossen wird
      previousFocusRef.current.focus()
    }
  }, [isOpen, initialFocusRef])
  
  // Behandle Tastaturereignisse (Escape zum Schließen)
  useEffect(() => {
    if (!isOpen || !closeOnEsc) return
    
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose()
      }
    }
    
    document.addEventListener('keydown', handleKeyDown)
    return () => {
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [isOpen, onClose, closeOnEsc])
  
  // Verhindere das Scrollen des Hintergrunds, wenn der Dialog geöffnet ist
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = ''
    }
    
    return () => {
      document.body.style.overflow = ''
    }
  }, [isOpen])
  
  // Behandle Klicks außerhalb des Dialogs
  const handleOutsideClick = (e: React.MouseEvent) => {
    if (closeOnOutsideClick && dialogRef.current && !dialogRef.current.contains(e.target as Node)) {
      onClose()
    }
  }
  
  // Bestimme die Größenklassen
  const sizeClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    full: 'max-w-full mx-4',
  }
  
  // Wenn der Dialog nicht geöffnet ist, rendere nichts
  if (!isOpen) return null
  
  // Rendere den Dialog im Portal
  return createPortal(
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4"
      onClick={handleOutsideClick}
      role="presentation"
    >
      <div
        ref={dialogRef}
        role="dialog"
        aria-modal="true"
        aria-labelledby="dialog-title"
        aria-describedby={ariaDescribedby}
        className={cn(
          'bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full',
          sizeClasses[size],
          settings.highContrast && 'bg-black border-2 border-white',
          className
        )}
        tabIndex={-1}
      >
        {/* Dialog-Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <h2
            id="dialog-title"
            className={cn(
              'text-lg font-semibold',
              settings.highContrast && 'text-white',
              titleClassName
            )}
          >
            {title}
          </h2>
          
          <button
            type="button"
            onClick={onClose}
            aria-label="Schließen"
            className={cn(
              'p-1 rounded-full text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-primary',
              settings.highContrast && 'text-white hover:text-yellow-400',
              closeButtonClassName
            )}
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        
        {/* Dialog-Inhalt */}
        <div className={cn('p-4', contentClassName)}>
          {children}
        </div>
      </div>
    </div>,
    document.body
  )
}
