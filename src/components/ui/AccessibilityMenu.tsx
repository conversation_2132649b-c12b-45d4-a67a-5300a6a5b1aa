'use client'

import React, { useState } from 'react'
import { useAccessibility } from '@/providers/AccessibilityProvider'
import { Button } from '@/components/ui/Button'
import { Slider } from '@/components/ui/Slider'
import { Switch } from '@/components/ui/Switch'
import { useI18n } from '@/providers/I18nProvider'
import { 
  ChevronUp, 
  ChevronDown, 
  ZoomIn, 
  ZoomOut, 
  Eye, 
  Volume2, 
  VolumeX,
  RefreshCw,
  X,
  Settings
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface AccessibilityMenuProps {
  className?: string
}

export function AccessibilityMenu({ className }: AccessibilityMenuProps) {
  const { 
    settings, 
    updateFontSize, 
    toggleHighContrast, 
    toggleReducedMotion, 
    toggleTextToSpeech,
    resetSettings,
    speakText,
    stopSpeaking
  } = useAccessibility()
  
  const { t } = useI18n()
  const [isOpen, setIsOpen] = useState(false)

  // Funktion zum E<PERSON>h<PERSON><PERSON> der Schriftgröße
  const increaseFontSize = () => {
    updateFontSize(settings.fontSize + 10)
  }

  // Funktion zum Verringern der Schriftgröße
  const decreaseFontSize = () => {
    updateFontSize(settings.fontSize - 10)
  }

  // Funktion zum Vorlesen der aktuellen Seite
  const readPageContent = () => {
    // Hauptinhalt der Seite finden
    const mainContent = document.querySelector('main')
    if (mainContent) {
      // Text aus dem Hauptinhalt extrahieren
      const textContent = mainContent.textContent || ''
      // Text vorlesen
      speakText(textContent)
    }
  }

  // Funktion zum Öffnen/Schließen des Menüs
  const toggleMenu = () => {
    setIsOpen(!isOpen)
  }

  return (
    <div className={cn("fixed bottom-4 left-4 z-50", className)}>
      {/* Schwebender Button zum Öffnen des Menüs */}
      <Button
        variant="default"
        size="icon"
        onClick={toggleMenu}
        aria-label={t("Barrierefreiheits-Einstellungen öffnen")}
        aria-expanded={isOpen}
        className="rounded-full shadow-lg"
      >
        <Settings className="h-5 w-5" />
      </Button>

      {/* Ausklappbares Menü */}
      {isOpen && (
        <div className="accessibility-menu w-72 absolute bottom-0 left-14 animate-in fade-in slide-in-from-left-5">
          <div className="flex justify-between items-center mb-4">
            <h3 className="font-semibold text-lg">{t("Barrierefreiheit")}</h3>
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleMenu}
              aria-label={t("Barrierefreiheits-Menü schließen")}
              className="h-8 w-8"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Schriftgröße */}
          <div className="mb-4">
            <div className="flex justify-between items-center mb-2">
              <label className="text-sm font-medium">
                {t("Schriftgröße")}: {settings.fontSize}%
              </label>
              <div className="flex space-x-1">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={decreaseFontSize}
                  disabled={settings.fontSize <= 80}
                  aria-label={t("Schriftgröße verringern")}
                  className="h-7 w-7"
                >
                  <ZoomOut className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={increaseFontSize}
                  disabled={settings.fontSize >= 150}
                  aria-label={t("Schriftgröße erhöhen")}
                  className="h-7 w-7"
                >
                  <ZoomIn className="h-4 w-4" />
                </Button>
              </div>
            </div>
            <Slider
              value={[settings.fontSize]}
              min={80}
              max={150}
              step={10}
              onValueChange={(value) => updateFontSize(value[0])}
              aria-label={t("Schriftgröße anpassen")}
            />
          </div>

          {/* Hochkontrast-Modus */}
          <div className="flex items-center justify-between mb-4">
            <label htmlFor="high-contrast" className="text-sm font-medium">
              {t("Hochkontrast-Modus")}
            </label>
            <Switch
              id="high-contrast"
              checked={settings.highContrast}
              onCheckedChange={toggleHighContrast}
              aria-label={t("Hochkontrast-Modus umschalten")}
            />
          </div>

          {/* Reduzierte Bewegung */}
          <div className="flex items-center justify-between mb-4">
            <label htmlFor="reduced-motion" className="text-sm font-medium">
              {t("Reduzierte Bewegung")}
            </label>
            <Switch
              id="reduced-motion"
              checked={settings.reducedMotion}
              onCheckedChange={toggleReducedMotion}
              aria-label={t("Reduzierte Bewegung umschalten")}
            />
          </div>

          {/* Text-to-Speech */}
          <div className="flex items-center justify-between mb-4">
            <label htmlFor="text-to-speech" className="text-sm font-medium">
              {t("Vorlesen aktivieren")}
            </label>
            <Switch
              id="text-to-speech"
              checked={settings.textToSpeechEnabled}
              onCheckedChange={toggleTextToSpeech}
              aria-label={t("Vorlesen umschalten")}
            />
          </div>

          {/* Text-to-Speech Steuerung */}
          {settings.textToSpeechEnabled && (
            <div className="flex space-x-2 mb-4">
              <Button
                variant="outline"
                onClick={readPageContent}
                aria-label={t("Seite vorlesen")}
                className="flex-1"
              >
                <Volume2 className="h-4 w-4 mr-2" />
                {t("Vorlesen")}
              </Button>
              <Button
                variant="outline"
                onClick={stopSpeaking}
                aria-label={t("Vorlesen stoppen")}
                className="flex-1"
              >
                <VolumeX className="h-4 w-4 mr-2" />
                {t("Stoppen")}
              </Button>
            </div>
          )}

          {/* Zurücksetzen */}
          <Button
            variant="outline"
            onClick={resetSettings}
            aria-label={t("Einstellungen zurücksetzen")}
            className="w-full mt-2"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            {t("Zurücksetzen")}
          </Button>
        </div>
      )}
    </div>
  );
}
