'use client'

import React, { useState, useEffect } from 'react'
import { createPortal } from 'react-dom'
import { cn } from '@/lib/utils'
import { useAccessibility } from '@/providers/AccessibilityProvider'
import { X, AlertCircle, CheckCircle, Info, AlertTriangle } from 'lucide-react'

type AlertType = 'info' | 'success' | 'warning' | 'error'

interface AccessibleAlertProps {
  type?: AlertType
  title?: string
  message: string
  isOpen: boolean
  onClose?: () => void
  autoClose?: boolean
  autoCloseDelay?: number
  className?: string
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center'
  showIcon?: boolean
  showCloseButton?: boolean
  'aria-live'?: 'polite' | 'assertive' | 'off'
}

export function AccessibleAlert({
  type = 'info',
  title,
  message,
  isOpen,
  onClose,
  autoClose = false,
  autoCloseDelay = 5000,
  className,
  position = 'top-right',
  showIcon = true,
  showCloseButton = true,
  'aria-live': ariaLive = 'polite',
}: AccessibleAlertProps) {
  const [isVisible, setIsVisible] = useState(isOpen)
  const { settings, speakText } = useAccessibility()
  
  // Aktualisiere die Sichtbarkeit, wenn sich isOpen ändert
  useEffect(() => {
    setIsVisible(isOpen)
  }, [isOpen])
  
  // Automatisches Schließen
  useEffect(() => {
    if (isVisible && autoClose) {
      const timer = setTimeout(() => {
        setIsVisible(false)
        if (onClose) {
          onClose()
        }
      }, autoCloseDelay)
      
      return () => {
        clearTimeout(timer)
      }
    }
  }, [isVisible, autoClose, autoCloseDelay, onClose])
  
  // Vorlesen der Benachrichtigung für Screenreader
  useEffect(() => {
    if (isVisible && settings.textToSpeechEnabled) {
      const textToRead = title ? `${title}: ${message}` : message
      speakText(textToRead)
    }
  }, [isVisible, title, message, settings.textToSpeechEnabled, speakText])
  
  // Schließen der Benachrichtigung
  const handleClose = () => {
    setIsVisible(false)
    if (onClose) {
      onClose()
    }
  }
  
  // Bestimme das Icon basierend auf dem Typ
  const getIcon = () => {
    switch (type) {
      case 'info':
        return <Info className="h-5 w-5 text-blue-500" />
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />
      case 'error':
        return <AlertCircle className="h-5 w-5 text-red-500" />
      default:
        return <Info className="h-5 w-5 text-blue-500" />
    }
  }
  
  // Bestimme die Klassen basierend auf dem Typ
  const getTypeClasses = () => {
    switch (type) {
      case 'info':
        return 'bg-blue-50 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200 border-blue-200 dark:border-blue-800'
      case 'success':
        return 'bg-green-50 dark:bg-green-900/20 text-green-800 dark:text-green-200 border-green-200 dark:border-green-800'
      case 'warning':
        return 'bg-yellow-50 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-200 border-yellow-200 dark:border-yellow-800'
      case 'error':
        return 'bg-red-50 dark:bg-red-900/20 text-red-800 dark:text-red-200 border-red-200 dark:border-red-800'
      default:
        return 'bg-blue-50 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200 border-blue-200 dark:border-blue-800'
    }
  }
  
  // Bestimme die Positionsklassen
  const getPositionClasses = () => {
    switch (position) {
      case 'top-right':
        return 'top-4 right-4'
      case 'top-left':
        return 'top-4 left-4'
      case 'bottom-right':
        return 'bottom-4 right-4'
      case 'bottom-left':
        return 'bottom-4 left-4'
      case 'top-center':
        return 'top-4 left-1/2 transform -translate-x-1/2'
      case 'bottom-center':
        return 'bottom-4 left-1/2 transform -translate-x-1/2'
      default:
        return 'top-4 right-4'
    }
  }
  
  // Wenn die Benachrichtigung nicht sichtbar ist, rendere nichts
  if (!isVisible) return null
  
  // Rendere die Benachrichtigung im Portal
  return createPortal(
    <div
      role="alert"
      aria-live={ariaLive}
      className={cn(
        'fixed z-50 max-w-md w-full p-4 rounded-lg shadow-md border animate-in fade-in slide-in-from-top-5 duration-300',
        getTypeClasses(),
        getPositionClasses(),
        settings.highContrast && 'bg-black text-white border-white',
        className
      )}
    >
      <div className="flex items-start">
        {showIcon && (
          <div className="flex-shrink-0 mr-3">
            {getIcon()}
          </div>
        )}
        
        <div className="flex-1">
          {title && (
            <h3 className="text-sm font-medium mb-1">{title}</h3>
          )}
          <p className="text-sm">{message}</p>
        </div>
        
        {showCloseButton && onClose && (
          <button
            type="button"
            onClick={handleClose}
            className="flex-shrink-0 ml-3 p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 focus:outline-none focus-visible:ring-2 focus-visible:ring-primary"
            aria-label="Benachrichtigung schließen"
          >
            <X className="h-4 w-4" />
          </button>
        )}
      </div>
    </div>,
    document.body
  )
}
