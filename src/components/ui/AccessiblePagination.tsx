'use client'

import React from 'react'
import { cn } from '@/lib/utils'
import { useAccessibility } from '@/providers/AccessibilityProvider'
import { ChevronLeft, ChevronRight, MoreHorizontal } from 'lucide-react'

interface AccessiblePaginationProps {
  currentPage: number
  totalPages: number
  onPageChange: (page: number) => void
  className?: string
  buttonClassName?: string
  activeButtonClassName?: string
  showFirstLastButtons?: boolean
  showPrevNextButtons?: boolean
  siblingCount?: number
  boundaryCount?: number
  'aria-label'?: string
}

export function AccessiblePagination({
  currentPage,
  totalPages,
  onPageChange,
  className,
  buttonClassName,
  activeButtonClassName,
  showFirstLastButtons = true,
  showPrevNextButtons = true,
  siblingCount = 1,
  boundaryCount = 1,
  'aria-label': ariaLabel = 'Pagination',
}: AccessiblePaginationProps) {
  const { settings } = useAccessibility()
  
  // Hilfsfunktion zum Erstellen eines Bereichs von Zahlen
  const range = (start: number, end: number) => {
    const length = end - start + 1
    return Array.from({ length }, (_, i) => start + i)
  }
  
  // Berechne die anzuzeigenden Seitenzahlen
  const getPageNumbers = () => {
    const startPages = range(1, Math.min(boundaryCount, totalPages))
    const endPages = range(
      Math.max(totalPages - boundaryCount + 1, boundaryCount + 1),
      totalPages
    )
    
    const siblingsStart = Math.max(
      Math.min(
        currentPage - siblingCount,
        totalPages - boundaryCount - siblingCount * 2 - 1
      ),
      boundaryCount + 2
    )
    
    const siblingsEnd = Math.min(
      Math.max(
        currentPage + siblingCount,
        boundaryCount + siblingCount * 2 + 2
      ),
      endPages.length > 0 ? endPages[0] - 2 : totalPages - 1
    )
    
    const itemList: (number | 'ellipsis')[] = []
    
    // Startseiten
    itemList.push(...startPages)
    
    // Ellipsis zwischen Startseiten und Geschwisterseiten
    if (siblingsStart > boundaryCount + 2) {
      itemList.push('ellipsis')
    } else if (boundaryCount + 1 < totalPages - boundaryCount) {
      itemList.push(boundaryCount + 1)
    }
    
    // Geschwisterseiten
    itemList.push(...range(siblingsStart, siblingsEnd))
    
    // Ellipsis zwischen Geschwisterseiten und Endseiten
    if (siblingsEnd < totalPages - boundaryCount - 1) {
      itemList.push('ellipsis')
    } else if (totalPages - boundaryCount > boundaryCount) {
      itemList.push(totalPages - boundaryCount)
    }
    
    // Endseiten
    itemList.push(...endPages)
    
    return itemList
  }
  
  // Behandle Seitenwechsel
  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages && page !== currentPage) {
      onPageChange(page)
    }
  }
  
  // Tastatursteuerung
  const handleKeyDown = (e: React.KeyboardEvent, page: number) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault()
      handlePageChange(page)
    }
  }
  
  // Generiere die Seitenzahlen
  const pageNumbers = getPageNumbers()
  
  return (
    <nav
      aria-label={ariaLabel}
      className={cn('flex justify-center', className)}
    >
      <ul className="flex items-center">
        {/* Erste Seite */}
        {showFirstLastButtons && (
          <li>
            <button
              type="button"
              onClick={() => handlePageChange(1)}
              disabled={currentPage === 1}
              aria-label="Erste Seite"
              className={cn(
                'flex items-center justify-center h-8 w-8 rounded-md focus:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2',
                'disabled:opacity-50 disabled:cursor-not-allowed',
                buttonClassName,
                settings.highContrast && 'focus-visible:ring-yellow-400'
              )}
            >
              <span aria-hidden="true">«</span>
            </button>
          </li>
        )}
        
        {/* Vorherige Seite */}
        {showPrevNextButtons && (
          <li>
            <button
              type="button"
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
              aria-label="Vorherige Seite"
              className={cn(
                'flex items-center justify-center h-8 w-8 rounded-md focus:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2',
                'disabled:opacity-50 disabled:cursor-not-allowed',
                buttonClassName,
                settings.highContrast && 'focus-visible:ring-yellow-400'
              )}
            >
              <ChevronLeft className="h-4 w-4" />
            </button>
          </li>
        )}
        
        {/* Seitenzahlen */}
        {pageNumbers.map((page, index) => (
          <li key={index}>
            {page === 'ellipsis' ? (
              <span
                className="flex items-center justify-center h-8 w-8"
                aria-hidden="true"
              >
                <MoreHorizontal className="h-4 w-4" />
              </span>
            ) : (
              <button
                type="button"
                onClick={() => handlePageChange(page)}
                onKeyDown={(e) => handleKeyDown(e, page)}
                aria-label={`Seite ${page}`}
                aria-current={page === currentPage ? 'page' : undefined}
                className={cn(
                  'flex items-center justify-center h-8 w-8 rounded-md focus:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2',
                  page === currentPage
                    ? cn('bg-primary text-white', activeButtonClassName)
                    : 'hover:bg-gray-100 dark:hover:bg-gray-800',
                  buttonClassName,
                  settings.highContrast && 'focus-visible:ring-yellow-400',
                  settings.highContrast && page === currentPage && 'bg-yellow-400 text-black'
                )}
              >
                {page}
              </button>
            )}
          </li>
        ))}
        
        {/* Nächste Seite */}
        {showPrevNextButtons && (
          <li>
            <button
              type="button"
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
              aria-label="Nächste Seite"
              className={cn(
                'flex items-center justify-center h-8 w-8 rounded-md focus:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2',
                'disabled:opacity-50 disabled:cursor-not-allowed',
                buttonClassName,
                settings.highContrast && 'focus-visible:ring-yellow-400'
              )}
            >
              <ChevronRight className="h-4 w-4" />
            </button>
          </li>
        )}
        
        {/* Letzte Seite */}
        {showFirstLastButtons && (
          <li>
            <button
              type="button"
              onClick={() => handlePageChange(totalPages)}
              disabled={currentPage === totalPages}
              aria-label="Letzte Seite"
              className={cn(
                'flex items-center justify-center h-8 w-8 rounded-md focus:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2',
                'disabled:opacity-50 disabled:cursor-not-allowed',
                buttonClassName,
                settings.highContrast && 'focus-visible:ring-yellow-400'
              )}
            >
              <span aria-hidden="true">»</span>
            </button>
          </li>
        )}
      </ul>
    </nav>
  )
}
