import React from 'react'
import { cn } from '@/lib/utils'

interface SectionProps {
  id?: string;
  title?: React.ReactNode;
  subtitle?: string;
  description?: string;
  children?: React.ReactNode;
  className?: string;
  containerClassName?: string;
  titleClassName?: string;
  subtitleClassName?: string;
  descriptionClassName?: string;
  // ARIA Attribute für Barrierefreiheit
  role?: string;
  'aria-label'?: string;
  'aria-labelledby'?: string;
  'aria-describedby'?: string;
}

export const Section = ({
  id,
  title,
  subtitle,
  description,
  children,
  className = "",
  containerClassName = "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",
  titleClassName = "",
  subtitleClassName = "",
  descriptionClassName = "",
  role,
  'aria-label': ariaLabel,
  'aria-labelledby': ariaLabelledby,
  'aria-describedby': ariaDescribedby,
}: SectionProps) => {
  return (
    <section
      id={id}
      className={cn("py-20 w-full relative overflow-hidden", className)}
      role={role}
      aria-label={ariaLabel}
      aria-labelledby={ariaLabelledby}
      aria-describedby={ariaDescribedby}
    >
      <div className={cn(containerClassName, "w-full")}>
        {/* Section header */}
        {(title || subtitle) && (
          <div className="text-center mb-12">
            {title && (
              <h2
                className={cn(
                  "text-3xl sm:text-4xl font-bold mb-2",
                  titleClassName
                )}
              >
                {title}
              </h2>
            )}
            {subtitle && (
              <h3 className={cn("text-lg font-medium", subtitleClassName)}>
                {subtitle}
              </h3>
            )}
          </div>
        )}
        {children}
      </div>
    </section>
  );
}; 