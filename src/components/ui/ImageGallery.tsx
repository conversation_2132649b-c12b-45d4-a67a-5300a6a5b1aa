"use client";

import { useState } from "react";
import Image from "next/image";
import { Dialog, DialogContent } from "@/components/ui/Dialog";
import { ChevronLeft, ChevronRight, X } from "lucide-react";
import { Button } from "./Button";
import { motion, AnimatePresence } from "framer-motion";

interface ImageGalleryProps {
  images: {
    src: string;
    alt: string;
  }[];
  className?: string;
}

export function ImageGallery({ images, className = "" }: ImageGalleryProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  const showNext = () => {
    setCurrentImageIndex((current) => (current + 1) % images.length);
  };

  const showPrevious = () => {
    setCurrentImageIndex(
      (current) => (current - 1 + images.length) % images.length
    );
  };

  return (
    <>
      <div
        className={`relative group cursor-pointer ${className}`}
        onClick={() => setIsOpen(true)}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            setIsOpen(true);
          }
        }}
        role="button"
        tabIndex={0}
        aria-label="Bildergalerie öffnen"
        aria-haspopup="dialog"
      >
        <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 focus-within:opacity-100 transition-opacity duration-300 rounded-xl flex items-center justify-center">
          <span className="text-white text-sm font-medium">
            Galerie ansehen ({images.length} Bilder)
          </span>
        </div>
        <div className="relative h-48 w-full rounded-xl overflow-hidden">
          <Image
            src={images[0].src}
            alt={images[0].alt}
            fill
            className="object-cover transform group-hover:scale-105 transition-transform duration-300"
          />
        </div>
      </div>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent 
          className="max-w-5xl bg-black/95 border-0"
          aria-label="Bildergalerie"
          role="dialog"
        >
          <div className="relative h-[80vh] w-full">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentImageIndex}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.2 }}
                className="relative h-full w-full"
              >
                <Image
                  src={images[currentImageIndex].src}
                  alt={images[currentImageIndex].alt || `Bild ${currentImageIndex + 1} von ${images.length}`}
                  fill
                  className="object-contain"
                  quality={95}
                  role="img"
                  aria-describedby={`gallery-image-description-${currentImageIndex}`}
                />
                <div id={`gallery-image-description-${currentImageIndex}`} className="sr-only">
                  {images[currentImageIndex].alt || `Bild ${currentImageIndex + 1} von ${images.length}`}
                </div>
              </motion.div>
            </AnimatePresence>

            <Button
              variant="ghost"
              size="icon"
              className="absolute top-2 right-2 text-white hover:bg-white/20"
              onClick={() => setIsOpen(false)}
              aria-label="Galerie schließen"
            >
              <X className="h-5 w-5" aria-hidden="true" />
            </Button>

            {images.length > 1 && (
              <>
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute left-2 top-1/2 -translate-y-1/2 text-white hover:bg-white/20"
                  onClick={showPrevious}
                  aria-label="Vorheriges Bild"
                >
                  <ChevronLeft className="h-8 w-8" aria-hidden="true" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute right-2 top-1/2 -translate-y-1/2 text-white hover:bg-white/20"
                  onClick={showNext}
                  aria-label="Nächstes Bild"
                >
                  <ChevronRight className="h-8 w-8" aria-hidden="true" />
                </Button>
              </>
            )}

            <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex gap-2">
              {images.map((_, index) => (
                <button
                  key={index}
                  className={`w-2 h-2 rounded-full transition-all ${
                    index === currentImageIndex
                      ? "bg-white scale-125"
                      : "bg-white/50"
                  }`}
                  onClick={() => setCurrentImageIndex(index)}
                  aria-label={`Bild ${index + 1} von ${images.length} anzeigen`}
                  aria-current={index === currentImageIndex ? "true" : "false"}
                  tabIndex={0}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault();
                      setCurrentImageIndex(index);
                    }
                  }}
                />
              ))}
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
