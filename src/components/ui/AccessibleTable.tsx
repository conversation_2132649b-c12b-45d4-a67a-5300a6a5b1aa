'use client'

import React, { useState } from 'react'
import { cn } from '@/lib/utils'
import { useAccessibility } from '@/providers/AccessibilityProvider'
import { ChevronDown, ChevronUp } from 'lucide-react'

interface Column<T> {
  id: string
  header: React.ReactNode
  accessor: (row: T) => React.ReactNode
  sortable?: boolean
  width?: string
}

interface AccessibleTableProps<T> {
  columns: Column<T>[]
  data: T[]
  keyExtractor: (row: T) => string
  className?: string
  headerClassName?: string
  rowClassName?: string
  cellClassName?: string
  striped?: boolean
  hoverable?: boolean
  bordered?: boolean
  sortable?: boolean
  defaultSortColumn?: string
  defaultSortDirection?: 'asc' | 'desc'
  onRowClick?: (row: T) => void
  emptyMessage?: React.ReactNode
  caption?: React.ReactNode
  'aria-label'?: string
  'aria-describedby'?: string
}

export function AccessibleTable<T>({
  columns,
  data,
  keyExtractor,
  className,
  headerClassName,
  rowClassName,
  cellClassName,
  striped = true,
  hoverable = true,
  bordered = true,
  sortable = false,
  defaultSortColumn,
  defaultSortDirection = 'asc',
  onRowClick,
  emptyMessage = 'Keine Daten verfügbar',
  caption,
  'aria-label': ariaLabel,
  'aria-describedby': ariaDescribedby,
}: AccessibleTableProps<T>) {
  const { settings } = useAccessibility()
  const [sortColumn, setSortColumn] = useState<string | undefined>(defaultSortColumn)
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>(defaultSortDirection)
  
  // Sortiere die Daten, wenn sortable true ist
  const sortedData = React.useMemo(() => {
    if (!sortable || !sortColumn) return data
    
    const column = columns.find(col => col.id === sortColumn)
    if (!column || !column.sortable) return data
    
    return [...data].sort((a, b) => {
      const aValue = column.accessor(a)
      const bValue = column.accessor(b)
      
      // Vergleiche die Werte
      const comparison = compareValues(aValue, bValue)
      
      // Kehre die Sortierrichtung um, wenn desc
      return sortDirection === 'asc' ? comparison : -comparison
    })
  }, [data, sortable, sortColumn, sortDirection, columns])
  
  // Behandle Klick auf Spaltenüberschrift
  const handleHeaderClick = (columnId: string) => {
    if (!sortable) return
    
    const column = columns.find(col => col.id === columnId)
    if (!column || !column.sortable) return
    
    if (sortColumn === columnId) {
      // Wenn bereits nach dieser Spalte sortiert wird, ändere die Richtung
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      // Sonst setze die neue Spalte und Richtung
      setSortColumn(columnId)
      setSortDirection('asc')
    }
  }
  
  // Bestimme die Klassen für die Tabelle
  const tableClasses = cn(
    'w-full table-auto',
    bordered && 'border-collapse',
    className
  )
  
  // Bestimme die Klassen für die Tabellenüberschriften
  const thClasses = cn(
    'px-4 py-2 text-left font-medium',
    bordered && 'border dark:border-gray-700',
    sortable && 'cursor-pointer select-none',
    headerClassName
  )
  
  // Bestimme die Klassen für die Tabellenzeilen
  const trClasses = (index: number) => cn(
    striped && index % 2 === 1 && 'bg-gray-50 dark:bg-gray-800/50',
    hoverable && 'hover:bg-gray-100 dark:hover:bg-gray-800',
    onRowClick && 'cursor-pointer',
    rowClassName
  )
  
  // Bestimme die Klassen für die Tabellenzellen
  const tdClasses = cn(
    'px-4 py-2',
    bordered && 'border dark:border-gray-700',
    cellClassName
  )
  
  return (
    <div className="overflow-x-auto">
      <table
        className={tableClasses}
        aria-label={ariaLabel}
        aria-describedby={ariaDescribedby}
      >
        {caption && <caption>{caption}</caption>}
        
        <thead className={settings.highContrast ? 'bg-black text-white' : ''}>
          <tr>
            {columns.map(column => (
              <th
                key={column.id}
                className={thClasses}
                onClick={() => column.sortable && handleHeaderClick(column.id)}
                style={{ width: column.width }}
                aria-sort={
                  sortable && sortColumn === column.id
                    ? sortDirection === 'asc'
                      ? 'ascending'
                      : 'descending'
                    : undefined
                }
              >
                <div className="flex items-center space-x-1">
                  <span>{column.header}</span>
                  {sortable && column.sortable && sortColumn === column.id && (
                    <span className="inline-flex">
                      {sortDirection === 'asc' ? (
                        <ChevronUp className="h-4 w-4" />
                      ) : (
                        <ChevronDown className="h-4 w-4" />
                      )}
                    </span>
                  )}
                </div>
              </th>
            ))}
          </tr>
        </thead>
        
        <tbody>
          {sortedData.length > 0 ? (
            sortedData.map((row, rowIndex) => (
              <tr
                key={keyExtractor(row)}
                className={trClasses(rowIndex)}
                onClick={() => onRowClick && onRowClick(row)}
              >
                {columns.map(column => (
                  <td key={column.id} className={tdClasses}>
                    {column.accessor(row)}
                  </td>
                ))}
              </tr>
            ))
          ) : (
            <tr>
              <td
                colSpan={columns.length}
                className="px-4 py-4 text-center text-gray-500 dark:text-gray-400"
              >
                {emptyMessage}
              </td>
            </tr>
          )}
        </tbody>
      </table>
    </div>
  )
}

// Hilfsfunktion zum Vergleichen von Werten für die Sortierung
function compareValues(a: React.ReactNode, b: React.ReactNode): number {
  // Konvertiere ReactNode zu vergleichbaren Werten
  const aValue = getComparableValue(a)
  const bValue = getComparableValue(b)
  
  // Vergleiche die Werte
  if (aValue === bValue) return 0
  if (aValue === null || aValue === undefined) return -1
  if (bValue === null || bValue === undefined) return 1
  
  return aValue < bValue ? -1 : 1
}

// Hilfsfunktion zum Extrahieren eines vergleichbaren Werts aus einem ReactNode
function getComparableValue(value: React.ReactNode): any {
  if (value === null || value === undefined) return null
  
  // Wenn es ein primitiver Wert ist, verwende ihn direkt
  if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {
    return value
  }
  
  // Wenn es ein Objekt mit toString ist, verwende toString
  if (value && typeof value === 'object' && 'toString' in value) {
    return value.toString()
  }
  
  // Wenn es ein React-Element ist, versuche, den Text zu extrahieren
  if (React.isValidElement(value)) {
    const props = value.props as any
    
    // Versuche, den Text aus children zu extrahieren
    if (props.children) {
      if (typeof props.children === 'string' || typeof props.children === 'number') {
        return props.children
      }
      
      // Wenn children ein Array ist, verbinde die Textwerte
      if (Array.isArray(props.children)) {
        return props.children
          .map((child: any) => getComparableValue(child))
          .filter(Boolean)
          .join(' ')
      }
    }
    
    // Versuche, den Text aus anderen Eigenschaften zu extrahieren
    for (const prop of ['value', 'label', 'title', 'alt', 'name']) {
      if (prop in props && (typeof props[prop] === 'string' || typeof props[prop] === 'number')) {
        return props[prop]
      }
    }
  }
  
  // Fallback: Konvertiere zu String
  return String(value)
}
