"use client";
import React, { useEffect, useRef, useState } from "react";
import {
  animate,
  useMotionValue,
  useTransform,
  useSpring,
  motion,
  useScroll,
  useReducedMotion,
  AnimatePresence,
} from "framer-motion";
import Image from "next/image";
import { TypeAnimation } from "react-type-animation";
import { useTheme } from "next-themes";
import { useRouter } from "next/navigation";
import { X, TrendingUp, Clock, Eye } from "lucide-react";
import { blogService } from "@/lib/blog-service";
import { useI18n } from "@/providers/I18nProvider";

// New component for animated number
const AnimatedMetricValue = React.memo(
  ({
    value,
    delay,
    startValueOffset = 20,
    className = "",
  }: {
    value: number;
    delay: number;
    startValueOffset?: number;
    className?: string;
  }) => {
    const startValue = value > startValueOffset ? value - startValueOffset : 0;
    const count = useMotionValue(startValue);
    const rounded = useTransform(count, (latest: number) => Math.round(latest));

    useEffect(() => {
      const controls = animate(count, value, {
        duration: 2.5,
        delay: delay,
        ease: "easeOut",
      });
      return controls.stop;
    }, [value, delay, count, startValue]);

    return (
      <div className={`relative font-bold tabular-nums ${className}`}>
        {/* Simplified animated number without clipPath */}
        <motion.span
          className="relative block text-center bg-gradient-to-r from-blue-400 via-violet-400 to-blue-400 text-transparent bg-clip-text"
          initial={{ opacity: 0, scale: 0.5 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: delay }}
        >
          <motion.span>{rounded}</motion.span>%
        </motion.span>
      </div>
    );
  }
);

AnimatedMetricValue.displayName = "AnimatedMetricValue";

interface HeroParallaxProps {
  dictionary: any; // We'll properly type this later
}

// Simplified Spark particles component with minimal elegant animations
const SparkParticles = React.memo(() => {
  const shouldReduceMotion = useReducedMotion();

  if (shouldReduceMotion) {
    return null;
  }

  // Generate random sparks with different properties - simplified for elegance
  const generateSparks = (count: number, type: "small" | "medium") => {
    return Array.from({ length: count }).map((_, i) => {
      // Base size adjusted by type for more controlled variation
      let baseSize;
      if (type === "small") baseSize = 1 + Math.random() * 1.5;
      else baseSize = 2 + Math.random() * 2;

      // More controlled positioning for better distribution
      const initialX = Math.random() * 100;
      const initialY = Math.random() * 100;

      // Slower, more elegant movement
      const speed = Math.random() * 8 + 3;
      const delay = Math.random() * 3;
      const duration = Math.random() * 15 + 15; // Longer durations for smoother movement

      // Very subtle opacity
      const opacity = Math.random() * 0.2 + 0.05;

      return {
        id: i,
        size: baseSize,
        initialX,
        initialY,
        speed,
        delay,
        duration,
        opacity,
      };
    });
  };

  // Generate fewer sparks for a cleaner look
  const smallSparks = generateSparks(8, "small");
  const mediumSparks = generateSparks(4, "medium");

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {/* Small sparks - minimal, subtle movement */}
      {smallSparks.map((spark) => (
        <motion.div
          key={`small-${spark.id}`}
          className="absolute rounded-full bg-white/20"
          style={{
            width: spark.size,
            height: spark.size,
            left: `${spark.initialX}%`,
            top: `${spark.initialY}%`,
            opacity: spark.opacity * 0.6,
          }}
          animate={{
            y: [0, -spark.speed * 6],
            x: [(Math.random() - 0.5) * 8, (Math.random() - 0.5) * 20],
            opacity: [0, spark.opacity, 0],
            scale: [0, 1, 0.5, 0],
          }}
          transition={{
            duration: spark.duration,
            delay: spark.delay,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
      ))}

      {/* Medium sparks with subtle glow */}
      {mediumSparks.map((spark) => (
        <motion.div
          key={`medium-${spark.id}`}
          className="absolute rounded-full bg-gradient-to-r from-blue-300/20 to-violet-300/20"
          style={{
            width: spark.size * 2,
            height: spark.size * 2,
            left: `${spark.initialX}%`,
            top: `${spark.initialY}%`,
            opacity: spark.opacity * 0.5,
          }}
          animate={{
            y: [0, -spark.speed * 10],
            x: [(Math.random() - 0.5) * 15, (Math.random() - 0.5) * 25],
            opacity: [0, spark.opacity, 0],
            scale: [0, 1, 0.6, 0],
          }}
          transition={{
            duration: spark.duration * 1.3,
            delay: spark.delay + 1,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
      ))}
    </div>
  );
});

SparkParticles.displayName = "SparkParticles";

export const HeroParallax = ({ dictionary }: HeroParallaxProps) => {
  const { resolvedTheme } = useTheme();
  const isDarkMode = resolvedTheme === "dark";

  return (
    <div className="min-h-screen w-full md:pb-0 antialiased relative flex flex-col self-auto overflow-hidden">
      {/* Solid background with subtle gradient */}
      <div className="absolute inset-0 bg-gradient-to-b from-[#162760] to-[#162760] dark:from-[#11142b] dark:to-[#11142b] pointer-events-none" />

      {/* Floating Tech Elements */}
      <FloatingTechElements />

      {/* Very subtle vignette effect for depth */}
      <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-black/5 pointer-events-none z-[1]" />

      {/* Simplified floating sparks with parallax effect - fewer elements */}
      <SparkParticles />

      <Header dictionary={dictionary} />
    </div>
  );
};

// Floating Tech Elements Component - optimized
const FloatingTechElements = React.memo(() => {
  const shouldReduceMotion = useReducedMotion();

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {/* Mobile Phone Frame - More Prominent */}
      <motion.div
        className="absolute w-36 h-36 md:w-48 md:h-48 opacity-15 dark:opacity-12 text-blue-200"
        style={{ top: "8%", right: "8%" }}
        animate={
          shouldReduceMotion
            ? {}
            : {
                y: [0, -10, 0],
                scale: [1, 1.05, 1],
              }
        }
        transition={{
          duration: 18,
          repeat: Infinity,
          repeatType: "reverse",
          ease: "linear",
        }}
      >
        <svg
          viewBox="0 0 100 100"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <rect
            x="25"
            y="10"
            width="50"
            height="80"
            rx="8"
            stroke="currentColor"
            strokeWidth="3"
            fill="currentColor"
            fillOpacity="0.1"
          />
          <rect
            x="28"
            y="20"
            width="44"
            height="60"
            rx="2"
            stroke="currentColor"
            strokeWidth="1"
            fill="currentColor"
            fillOpacity="0.05"
          />
          <circle cx="50" cy="85" r="3" fill="currentColor" fillOpacity="0.6" />
          <line
            x1="42"
            y1="15"
            x2="58"
            y2="15"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
          />
          {/* App icons simulation */}
          <circle cx="35" cy="30" r="3" fill="currentColor" fillOpacity="0.4" />
          <circle cx="50" cy="30" r="3" fill="currentColor" fillOpacity="0.4" />
          <circle cx="65" cy="30" r="3" fill="currentColor" fillOpacity="0.4" />
          <circle cx="35" cy="45" r="3" fill="currentColor" fillOpacity="0.4" />
          <circle cx="50" cy="45" r="3" fill="currentColor" fillOpacity="0.4" />
          <circle cx="65" cy="45" r="3" fill="currentColor" fillOpacity="0.4" />
        </svg>
      </motion.div>

      {/* Flutter Logo - Larger and More Prominent */}
      <motion.div
        className="absolute w-40 h-40 md:w-52 md:h-52 opacity-12 dark:opacity-10 text-blue-300"
        style={{ top: "10%", left: "5%" }}
        animate={
          shouldReduceMotion
            ? {}
            : {
                y: [0, -15, 0],
                rotate: [0, 5, 0],
              }
        }
        transition={{
          duration: 20,
          repeat: Infinity,
          repeatType: "reverse",
          ease: "linear",
        }}
      >
        <svg
          viewBox="0 0 100 100"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M21.25 70.31L39.06 88.13L82.81 44.38H47.19L21.25 70.31Z"
            fill="currentColor"
            fillOpacity="0.3"
          />
          <path
            d="M39.06 11.88L21.25 29.69L47.19 55.63H82.81L39.06 11.88Z"
            fill="currentColor"
            fillOpacity="0.8"
          />
          <path
            d="M21.25 70.31L39.06 88.13L53.13 74.06L39.06 60L21.25 70.31Z"
            fill="currentColor"
            fillOpacity="0.5"
          />
        </svg>
      </motion.div>

      {/* iOS Apple Logo - More Prominent */}
      <motion.div
        className="absolute w-32 h-32 md:w-44 md:h-44 opacity-12 dark:opacity-10 text-gray-300"
        style={{ top: "55%", right: "15%" }}
        animate={
          shouldReduceMotion
            ? {}
            : {
                y: [0, -10, 0],
                rotate: [0, -3, 0],
              }
        }
        transition={{
          duration: 15,
          repeat: Infinity,
          repeatType: "reverse",
          ease: "linear",
        }}
      >
        <svg
          viewBox="0 0 100 100"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M65.315 36.3078C61.312 36.3078 58.542 38.3672 56.361 38.3672C54.053 38.3672 51.154 36.437 47.535 36.437C42.579 36.437 37.232 39.8057 34.074 45.4557C29.761 53.7995 33.046 66.8682 37.232 74.4995C39.28 78.189 41.848 82.2682 45.209 82.1391C48.44 82.0099 49.747 80.0797 53.66 80.0797C57.573 80.0797 58.739 82.1391 62.098 82.0797C65.586 82.0099 67.83 78.3182 69.879 74.6286C72.256 70.2891 73.284 66.0807 73.368 65.8224C73.284 65.6932 66.227 62.9036 66.143 54.4578C66.059 47.3859 71.713 43.8182 72.084 43.5599C68.728 38.6849 63.646 38.1557 61.571 38.0266C58.985 37.7682 57.235 38.0266 55.208 38.9557C53.516 39.7432 51.971 40.6724 50.5 41.6016C49.029 40.6724 47.484 39.6141 45.792 38.8266C43.765 37.8974 42.015 37.5099 39.429 37.8974C37.381 38.026 32.298 38.5557 28.943 43.4307"
            stroke="currentColor"
            strokeWidth="2.5"
            strokeLinecap="round"
            fill="currentColor"
            fillOpacity="0.1"
          />
          <path
            d="M53.145 28.4141C54.84 26.2256 55.981 23.2425 55.632 20C52.448 20.2583 48.733 22.3177 46.909 24.5062C45.214 26.4365 43.817 29.4797 44.293 32.5932C47.607 32.8516 51.32 30.7922 53.145 28.4745"
            stroke="currentColor"
            strokeWidth="2.5"
            strokeLinecap="round"
            fill="currentColor"
            fillOpacity="0.1"
          />
        </svg>
      </motion.div>

      {/* Android Logo - More Prominent */}
      <motion.div
        className="absolute w-36 h-36 md:w-48 md:h-48 opacity-12 dark:opacity-10 text-green-400"
        style={{ bottom: "12%", left: "8%" }}
        animate={
          shouldReduceMotion
            ? {}
            : {
                y: [0, 20, 0],
                x: [0, 10, 0],
              }
        }
        transition={{
          duration: 25,
          repeat: Infinity,
          repeatType: "reverse",
          ease: "linear",
        }}
      >
        <svg
          viewBox="0 0 100 100"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M20 40H80V75C80 77.7614 77.7614 80 75 80H25C22.2386 80 20 77.7614 20 75V40Z"
            stroke="currentColor"
            strokeWidth="3"
            fill="currentColor"
            fillOpacity="0.1"
          />
          <path
            d="M30 40V30C30 22.6112 35.8203 16 43.3333 16H56.6667C64.1797 16 70 22.6112 70 30V40"
            stroke="currentColor"
            strokeWidth="3"
          />
          <circle cx="35" cy="25" r="3" fill="currentColor" fillOpacity="0.8" />
          <circle cx="65" cy="25" r="3" fill="currentColor" fillOpacity="0.8" />
          <path
            d="M50 70V60"
            stroke="currentColor"
            strokeWidth="3"
            strokeLinecap="round"
          />
          <path
            d="M45 65H55"
            stroke="currentColor"
            strokeWidth="3"
            strokeLinecap="round"
          />
          <path
            d="M15 60L20 60"
            stroke="currentColor"
            strokeWidth="3"
            strokeLinecap="round"
          />
          <path
            d="M80 60L85 60"
            stroke="currentColor"
            strokeWidth="3"
            strokeLinecap="round"
          />
        </svg>
      </motion.div>

      {/* Additional Mobile Device - Tablet */}
      <motion.div
        className="absolute w-28 h-28 md:w-40 md:h-40 opacity-10 dark:opacity-8 text-violet-300"
        style={{ bottom: "35%", right: "25%" }}
        animate={
          shouldReduceMotion
            ? {}
            : {
                y: [0, 12, 0],
                rotate: [0, 2, 0],
              }
        }
        transition={{
          duration: 22,
          repeat: Infinity,
          repeatType: "reverse",
          ease: "linear",
        }}
      >
        <svg
          viewBox="0 0 100 100"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <rect
            x="15"
            y="20"
            width="70"
            height="60"
            rx="6"
            stroke="currentColor"
            strokeWidth="2"
            fill="currentColor"
            fillOpacity="0.05"
          />
          <rect
            x="18"
            y="25"
            width="64"
            height="45"
            rx="2"
            stroke="currentColor"
            strokeWidth="1"
            fill="currentColor"
            fillOpacity="0.02"
          />
          <circle
            cx="50"
            cy="75"
            r="2.5"
            fill="currentColor"
            fillOpacity="0.6"
          />
        </svg>
      </motion.div>

      {/* Cross-Platform Indicator */}
      <motion.div
        className="absolute w-32 h-8 md:w-40 md:h-10 opacity-10 dark:opacity-8 text-yellow-300"
        style={{ top: "45%", left: "35%" }}
        animate={
          shouldReduceMotion
            ? {}
            : {
                scale: [1, 1.1, 1],
                opacity: [0.1, 0.15, 0.1],
              }
        }
        transition={{
          duration: 4,
          repeat: Infinity,
          repeatType: "reverse",
          ease: "linear",
        }}
      >
        <svg
          viewBox="0 0 100 20"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <text
            x="50"
            y="12"
            textAnchor="middle"
            fill="currentColor"
            fontSize="8"
            fontWeight="bold"
          >
            CROSS-PLATFORM
          </text>
          <path
            d="M10 10 L25 10 M75 10 L90 10"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
          />
        </svg>
      </motion.div>
    </div>
  );
});

FloatingTechElements.displayName = "FloatingTechElements";

export const Header = ({ dictionary }: { dictionary: any }) => {
  const router = useRouter();
  const { locale } = useI18n();
  const [isBlogOpen, setIsBlogOpen] = useState(false);
  const [currentPostIndex, setCurrentPostIndex] = useState(0);

  // Get recent posts from service
  const recentPosts = blogService.getRecentPosts(3, locale);

  // Auto-rotate through posts
  useEffect(() => {
    if (isBlogOpen && recentPosts.length > 1) {
      const interval = setInterval(() => {
        setCurrentPostIndex((prev) => (prev + 1) % recentPosts.length);
      }, 5000);
      return () => clearInterval(interval);
    }
  }, [isBlogOpen, recentPosts.length]);

  const currentPost = recentPosts[currentPostIndex];

  const handleBlogClick = () => {
    router.push(`/${locale}/blog`);
  };

  const handlePostClick = (slug: string) => {
    router.push(`/${locale}/blog/${slug}`);
    setIsBlogOpen(false);
  };

  // Enhanced Eye-Catching Stat Cards Component
  const StatCards = () => {
    // Data for the stat cards with detailed explanations
    const statsData = [
      {
        id: 1,
        label: dictionary.metrics.development.label || "Development Speed",
        value: dictionary.metrics.development.value || "40%",
        trend: "up",
        description:
          dictionary.metrics.development.description || "Faster development",
        howTitle: dictionary.metrics.development.howTitle || "How:",
        howDescription:
          dictionary.metrics.development.howExplanation ||
          "Using newest IDE with AI support, automated code generation, and intelligent code completion.",
        dataPoints: [10, 15, 25, 20, 30, 25, 40], // Data for the sparkline chart
        icon: (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.8}
            stroke="currentColor"
            className="w-5 h-5"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M15.59 14.37a6 6 0 0 1-5.84 7.38v-4.8m5.84-2.58a14.98 14.98 0 0 0 6.16-12.12A14.98 14.98 0 0 0 9.631 8.41m5.96 5.96a14.926 14.926 0 0 1-5.841 2.58m-.119-8.54a6 6 0 0 0-7.381 5.84h4.8m2.581-5.84a14.927 14.927 0 0 0-2.58 5.84m2.699 2.7c-.103.021-.207.041-.311.06a15.09 15.09 0 0 1-2.448-2.448 14.9 14.9 0 0 1 .06-.312m-2.24 2.39a4.493 4.493 0 0 0-1.757 4.306 4.493 4.493 0 0 0 4.306-1.758M16.5 9a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Z"
            />
          </svg>
        ),
        color: "blue",
        delay: 0.3,
      },
      {
        id: 2,
        label: dictionary.metrics.timeToMarket.label || "Time to Market",
        value: dictionary.metrics.timeToMarket.value || "50%",
        trend: "up",
        description:
          dictionary.metrics.timeToMarket.description || "Faster publishing",
        howTitle: dictionary.metrics.timeToMarket.howTitle || "How:",
        howDescription:
          dictionary.metrics.timeToMarket.howExplanation ||
          "Fast lane CI/CD pipeline, automated testing, AI-powered code review, and streamlined deployment process.",
        dataPoints: [15, 20, 25, 30, 35, 45, 50], // Data for the sparkline chart
        icon: (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.8}
            stroke="currentColor"
            className="w-5 h-5"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
            />
          </svg>
        ),
        color: "violet",
        delay: 0.5,
      },
      {
        id: 3,
        label: dictionary.metrics.costSaving.label || "Cost Efficiency",
        value: dictionary.metrics.costSaving.value || "30%",
        trend: "up",
        description:
          dictionary.metrics.costSaving.description || "Cost savings",
        howTitle: dictionary.metrics.costSaving.howTitle || "How:",
        howDescription:
          dictionary.metrics.costSaving.howExplanation ||
          "Using Flutter for cross-platform development, optimized cloud resources, and efficient development practices.",
        dataPoints: [5, 10, 15, 20, 25, 30, 30], // Data for the sparkline chart
        icon: (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.8}
            stroke="currentColor"
            className="w-5 h-5"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
            />
          </svg>
        ),
        color: "emerald",
        delay: 0.7,
      },
    ];

    // Function to get color classes based on the color prop
    const getColorClasses = (color: string) => {
      switch (color) {
        case "blue":
          return {
            bg: "bg-gradient-to-br from-blue-900/40 to-blue-800/20",
            border: "border-blue-500/30",
            text: "text-blue-400",
            trend: "text-blue-500",
            glow: "from-blue-500/20 to-blue-400/5",
            sparkline: "stroke-blue-500",
            sparklineFill: "fill-blue-500/20",
          };
        case "violet":
          return {
            bg: "bg-gradient-to-br from-violet-900/40 to-violet-800/20",
            border: "border-violet-500/30",
            text: "text-violet-400",
            trend: "text-violet-500",
            glow: "from-violet-500/20 to-violet-400/5",
            sparkline: "stroke-violet-500",
            sparklineFill: "fill-violet-500/20",
          };
        case "emerald":
          return {
            bg: "bg-gradient-to-br from-emerald-900/40 to-emerald-800/20",
            border: "border-emerald-500/30",
            text: "text-emerald-400",
            trend: "text-emerald-500",
            glow: "from-emerald-500/20 to-emerald-400/5",
            sparkline: "stroke-emerald-500",
            sparklineFill: "fill-emerald-500/20",
          };
        default:
          return {
            bg: "bg-gradient-to-br from-blue-900/40 to-blue-800/20",
            border: "border-blue-500/30",
            text: "text-blue-400",
            trend: "text-blue-500",
            glow: "from-blue-500/20 to-blue-400/5",
            sparkline: "stroke-blue-500",
            sparklineFill: "fill-blue-500/20",
          };
      }
    };

    // Function to generate sparkline path from data points
    const generateSparklinePath = (
      dataPoints: number[],
      height: number = 40,
      width: number = 100
    ) => {
      if (!dataPoints.length) return "";

      const max = Math.max(...dataPoints);
      const min = Math.min(...dataPoints);
      const range = max - min || 1;

      const xStep = width / (dataPoints.length - 1);

      // Generate path
      return dataPoints
        .map((point, i) => {
          const x = i * xStep;
          const y = height - ((point - min) / range) * height;
          return `${i === 0 ? "M" : "L"} ${x},${y}`;
        })
        .join(" ");
    };

    return (
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {statsData.map((stat) => {
          const colorClasses = getColorClasses(stat.color);
          const sparklinePath = generateSparklinePath(stat.dataPoints);

          return (
            <motion.div
              key={stat.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: stat.delay }}
              className={`relative rounded-xl ${colorClasses.bg} border ${colorClasses.border} p-6 shadow-lg backdrop-blur-sm overflow-hidden group hover:shadow-xl transition-all duration-300`}
            >
              {/* Glowing accent in corner */}
              <div
                className={`absolute -top-10 -right-10 w-20 h-20 rounded-full bg-gradient-to-br ${colorClasses.glow} blur-xl opacity-60 group-hover:opacity-80 transition-opacity duration-300`}
              />

              {/* Header with icon and label */}
              <div className="flex justify-between items-start mb-3">
                <div>
                  <div
                    className={`text-sm font-semibold ${colorClasses.text} mb-1`}
                  >
                    {stat.label}
                  </div>
                  <div className="flex items-baseline">
                    <div className="text-3xl font-bold text-white">
                      {stat.value}
                    </div>
                    <div className="ml-1 text-sm text-gray-300">
                      {stat.description}
                    </div>
                  </div>
                </div>
                <div
                  className={`p-2 rounded-lg bg-white/10 border border-white/5`}
                >
                  {React.cloneElement(stat.icon, {
                    className: `w-5 h-5 ${colorClasses.text}`,
                  })}
                </div>
              </div>

              {/* Sparkline chart */}
              <div className="relative h-10 mt-2 mb-3">
                <svg
                  width="100%"
                  height="100%"
                  viewBox="0 0 100 40"
                  preserveAspectRatio="none"
                >
                  {/* Area under the sparkline */}
                  <motion.path
                    d={`${sparklinePath} L 100,40 L 0,40 Z`}
                    className={`${colorClasses.sparklineFill}`}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 1, delay: stat.delay + 0.5 }}
                  />

                  {/* Sparkline */}
                  <motion.path
                    d={sparklinePath}
                    className={`${colorClasses.sparkline} fill-none stroke-2`}
                    initial={{ pathLength: 0 }}
                    animate={{ pathLength: 1 }}
                    transition={{
                      duration: 1.5,
                      delay: stat.delay + 0.3,
                      ease: "easeOut",
                    }}
                  />

                  {/* End point with pulse effect */}
                  <motion.circle
                    cx={100}
                    cy={
                      40 -
                      (stat.dataPoints[stat.dataPoints.length - 1] /
                        Math.max(...stat.dataPoints)) *
                        40
                    }
                    r={3}
                    className={`${colorClasses.text} fill-current`}
                    initial={{ scale: 0 }}
                    animate={{ scale: [0, 1.2, 1] }}
                    transition={{ duration: 0.5, delay: stat.delay + 1.8 }}
                  />
                </svg>

                {/* Trend indicator */}
                <motion.div
                  className={`absolute top-0 right-0 flex items-center ${colorClasses.trend} text-xs font-medium`}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5, delay: stat.delay + 1.5 }}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                    className="w-3 h-3 mr-1"
                  >
                    <path
                      fillRule="evenodd"
                      d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z"
                      clipRule="evenodd"
                    />
                  </svg>
                  {stat.value}
                </motion.div>
              </div>

              {/* How section */}
              <div className="pt-2">
                <div className="text-[10px] font-semibold text-blue-400 mb-1">
                  {dictionary.metrics.development.howTitle || "How:"}
                </div>
                <div className="text-[10px] text-gray-300 leading-tight">
                  {dictionary.metrics.development.howExplanation ||
                    "Using newest IDE with AI support"}
                </div>
              </div>
            </motion.div>
          );
        })}
      </div>
    );
  };

  return (
    <div className="relative w-full left-0 top-6 z-10 px-4 sm:px-6 lg:px-8 xl:px-0">
      {/* 100% Satisfaction Guarantee Seal - Top Right */}
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.8, delay: 0.3 }}
        className="fixed top-4 right-4 z-50 hidden md:block"
      >
        {/* Mobile version - smaller and positioned differently */}
      </motion.div>

      {/* Mobile Seal - Top Right but smaller */}
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.8, delay: 0.3 }}
        className="fixed top-16 right-4 z-50 md:hidden"
      >
        <div className="relative">
          {/* Star-based design for mobile */}
          <div className="w-16 h-16 rounded-full bg-gradient-to-br from-yellow-400/20 to-yellow-600/10 backdrop-blur-sm flex flex-col items-center justify-center shadow-lg border border-yellow-400/30">
            <div className="flex gap-0.5 mb-1">
              {[1, 2, 3, 4, 5].map((star) => (
                <motion.svg
                  key={star}
                  className="w-2 h-2 text-yellow-400 fill-current"
                  viewBox="0 0 20 20"
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ duration: 0.3, delay: 0.8 + star * 0.1 }}
                >
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.286 3.954a1 1 0 00.95.69h4.162c.969 0 1.371 1.24.588 1.81l-3.37 2.448a1 1 0 00-.364 1.118l1.286 3.954c.3.921-.755 1.688-1.54 1.118l-3.37-2.448a1 1 0 00-1.175 0l-3.37 2.448c-.784.57-1.838-.197-1.539-1.118l1.286-3.954a1 1 0 00-.364-1.118L2.49 9.381c-.783-.57-.38-1.81.588-1.81h4.162a1 1 0 00.95-.69l1.286-3.954z" />
                </motion.svg>
              ))}
            </div>
            <div className="text-center">
              <div className="text-yellow-400 text-xs font-bold leading-tight">
                100%
              </div>
              <div className="text-yellow-300 text-[8px] font-medium leading-tight">
                GUARANTEE
              </div>
            </div>
          </div>
          {/* Subtle glow effect */}
          <div className="absolute inset-0 rounded-full bg-yellow-400/20 blur-md -z-10" />
        </div>
      </motion.div>

      {/* Desktop Seal */}
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.8, delay: 0.3 }}
        className="fixed top-20 right-8 z-50 hidden md:block"
      >
        <div className="relative">
          {/* Star-based design for desktop */}
          <div className="w-20 h-20 rounded-full bg-gradient-to-br from-yellow-400/20 to-yellow-600/10 backdrop-blur-sm flex flex-col items-center justify-center shadow-xl border border-yellow-400/30">
            <div className="flex gap-0.5 mb-1">
              {[1, 2, 3, 4, 5].map((star) => (
                <motion.svg
                  key={star}
                  className="w-2.5 h-2.5 text-yellow-400 fill-current"
                  viewBox="0 0 20 20"
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ duration: 0.3, delay: 0.8 + star * 0.1 }}
                >
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.286 3.954a1 1 0 00.95.69h4.162c.969 0 1.371 1.24.588 1.81l-3.37 2.448a1 1 0 00-.364 1.118l1.286 3.954c.3.921-.755 1.688-1.54 1.118l-3.37-2.448a1 1 0 00-1.175 0l-3.37 2.448c-.784.57-1.838-.197-1.539-1.118l1.286-3.954a1 1 0 00-.364-1.118L2.49 9.381c-.783-.57-.38-1.81.588-1.81h4.162a1 1 0 00.95-.69l1.286-3.954z" />
                </motion.svg>
              ))}
            </div>
            <div className="text-center">
              <div className="text-yellow-400 text-sm font-bold leading-tight">
                100%
              </div>
              <div className="text-yellow-300 text-[10px] font-medium leading-tight">
                GUARANTEE
              </div>
            </div>
          </div>
          {/* Subtle glow effect */}
          <div className="absolute inset-0 rounded-full bg-yellow-400/20 blur-lg -z-10" />
        </div>
      </motion.div>

      {/* Main Container - Two Column Layout */}
      <div className="max-w-7xl mx-auto w-full pt-16 md:pt-16 relative">
        {/* Ambient background glow */}
        <div className="absolute inset-0 bg-gradient-radial from-blue-900/10 via-transparent to-transparent pointer-events-none"></div>

        {/* Centered Single Column Layout */}
        <div className="flex flex-col items-center text-center max-w-5xl mx-auto relative z-10">
          {/* Main Content */}
          <div className="flex flex-col items-center text-center">
            {/* Enhanced Intro Text */}
            <p className="text-lg md:text-xl xl:text-2xl text-blue-100/90 mb-6 font-medium tracking-wide max-w-4xl">
              📱 Von der Idee bis zum App Store - Ihr Partner für erfolgreiche
              Apps
            </p>

            {/* Main Title - Reduced font size and weight */}
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7, delay: 0.2 }}
              className="text-3xl md:text-4xl lg:text-5xl xl:text-6xl 2xl:text-7xl font-semibold mb-8 w-full"
            >
              <div className="relative">
                {/* Enhanced elegant spark effect */}
                <motion.div
                  className="absolute -top-2 -right-3 w-2 h-2 rounded-full bg-blue-300/70"
                  animate={{
                    y: [0, -8, 0],
                    x: [0, 5, 0],
                    opacity: [0, 0.8, 0],
                    scale: [0.5, 1, 0.5],
                    boxShadow: [
                      "0 0 2px 1px rgba(96, 165, 250, 0.2)",
                      "0 0 6px 2px rgba(96, 165, 250, 0.4)",
                      "0 0 2px 1px rgba(96, 165, 250, 0.2)",
                    ],
                  }}
                  transition={{
                    duration: 3.5,
                    repeat: Infinity,
                    repeatType: "loop",
                  }}
                />

                {/* Subtle glow behind title */}
                <div className="absolute -inset-4 bg-blue-500/5 blur-2xl rounded-full -z-10 opacity-70" />
              </div>

              {/* Enhanced Subtitle with premium gradient and effects */}
              <div className="relative">
                {/* Enhanced gradient text with better contrast and readability */}
                <span className="bg-gradient-to-r from-blue-300 via-violet-300 to-blue-300 text-transparent bg-clip-text relative z-10 font-semibold tracking-tight drop-shadow-[0_1px_1px_rgba(0,0,0,0.5)]">
                  {dictionary?.title || "Mobile Apps for iOS & Android"}
                </span>
              </div>
            </motion.h1>

            {/* Description */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="max-w-2xl text-lg md:text-xl xl:text-2xl font-semibold mt-0 text-white/95 w-full mb-2"
            >
              <TypeAnimation
                sequence={[
                  "Flutter Apps für iOS & Android in 3-4 Wochen 📱",
                  2000,
                  "TOGG, Spotz & 15+ erfolgreiche Projekte 🚀",
                  2000,
                  "Festpreis • Pünktlich • Ohne Risiko 🏆",
                  2000,
                  "Während andere planen, entwickeln wir bereits ✨",
                  2000,
                  "",
                  500,
                ]}
                wrapper="span"
                speed={40}
                cursor={true}
                repeat={Infinity}
                className="inline-block"
              />
            </motion.div>

            {/* CTA Buttons - Moved before metrics */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7, delay: 0.8 }}
              className="flex flex-row flex-wrap items-center gap-4 mt-6 w-full justify-center mb-8"
            >
              {/* Start Your Project Button */}
              <motion.a
                href="#contact"
                className="w-full md:w-auto relative group overflow-hidden bg-gradient-to-br from-blue-600/90 via-indigo-600/90 to-violet-600/90 backdrop-blur-md text-white px-8 py-4 rounded-lg font-medium border border-white/10 flex items-center justify-center gap-2 whitespace-nowrap shadow-lg shadow-blue-900/20 transition-all duration-300"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                {/* Enhanced inner glow effect */}
                <div className="absolute inset-[1px] rounded-[6px] bg-gradient-to-br from-white/10 to-transparent opacity-50" />

                {/* Professional pulsing background glow */}
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-blue-400/20 via-indigo-400/30 to-violet-400/20 opacity-0"
                  animate={{
                    opacity: [0, 0.6, 0],
                  }}
                  transition={{
                    duration: 3.5,
                    repeat: Infinity,
                    repeatType: "loop",
                  }}
                />

                {/* Elegant shine effect on hover */}
                <motion.div
                  className="absolute inset-0 -translate-x-full group-hover:translate-x-full transition-transform duration-1200 bg-gradient-to-r from-transparent via-white/25 to-transparent"
                  initial={{ x: "-100%" }}
                  whileHover={{ x: "200%" }}
                  transition={{ duration: 1.8, ease: "easeInOut" }}
                />

                {/* Enhanced mobile icon */}
                <motion.div
                  whileHover={{ rotate: 15, scale: 1.1 }}
                  transition={{ type: "spring", stiffness: 400, damping: 10 }}
                  className="relative"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 drop-shadow-[0_0_2px_rgba(255,255,255,0.3)]"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"
                    />
                  </svg>
                </motion.div>

                {/* Button text with subtle text shadow */}
                <span className="relative z-10 tracking-wide drop-shadow-[0_0_1px_rgba(0,0,0,0.5)]">
                  {dictionary?.cta?.startProject || "Start App Project"}
                </span>
              </motion.a>

              {/* Book Consultation Button */}
              <motion.a
                href="https://calendly.com/v-hermann-it"
                target="_blank"
                rel="noopener noreferrer"
                className="w-full md:w-auto relative group overflow-hidden bg-white/8 backdrop-blur-lg text-white px-8 py-4 rounded-lg font-medium border border-white/15 flex items-center justify-center gap-2 whitespace-nowrap shadow-lg shadow-blue-900/5 transition-all duration-300"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                {/* Enhanced glass morphism effect */}
                <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-white/5 opacity-80" />

                {/* Subtle gradient background on hover */}
                <motion.div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 via-indigo-500/10 to-violet-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-400" />

                {/* Enhanced icon with subtle animation */}
                <motion.div
                  whileHover={{ rotate: 15, scale: 1.1 }}
                  transition={{ type: "spring", stiffness: 400, damping: 10 }}
                  className="relative"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 drop-shadow-[0_0_2px_rgba(255,255,255,0.3)]"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                    />
                  </svg>
                </motion.div>

                {/* Button text with subtle text shadow */}
                <span className="relative z-10 tracking-wide drop-shadow-[0_0_1px_rgba(0,0,0,0.5)]">
                  {dictionary?.cta?.freeConsultation || "Free Consultation"}
                </span>
              </motion.a>

              {/* App Cost Calculator Button */}
              <motion.a
                href="#pricing-calculator"
                className="w-full md:w-auto relative group overflow-hidden bg-gradient-to-br from-emerald-600/20 via-teal-600/20 to-emerald-600/20 backdrop-blur-lg text-white px-8 py-4 rounded-lg font-medium border border-emerald-300/20 flex items-center justify-center gap-2 whitespace-nowrap shadow-lg shadow-blue-900/5 transition-all duration-300"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                {/* Enhanced glass morphism effect */}
                <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-white/5 opacity-80" />

                {/* Professional pulsing gradient glow */}
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-emerald-500/20 via-teal-500/30 to-emerald-500/20"
                  initial={{ opacity: 0 }}
                  animate={{
                    opacity: [0, 0.7, 0],
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    repeatType: "loop",
                  }}
                />

                {/* Enhanced calculator icon with animation */}
                <motion.div
                  whileHover={{ rotate: 10, scale: 1.1 }}
                  transition={{ type: "spring", stiffness: 400, damping: 10 }}
                  className="relative"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 drop-shadow-[0_0_2px_rgba(255,255,255,0.3)]"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    strokeWidth={2}
                  >
                    <rect x="4" y="3" width="16" height="18" rx="2" ry="2" />
                    <line x1="8" y1="7" x2="16" y2="7" />
                    <line x1="8" y1="11" x2="16" y2="11" />
                    <line x1="8" y1="15" x2="12" y2="15" />
                    <line x1="8" y1="19" x2="12" y2="19" />
                    <line x1="14" y1="15" x2="16" y2="15" />
                    <line x1="14" y1="19" x2="16" y2="19" />
                  </svg>
                </motion.div>

                {/* App-specific button text */}
                <span className="relative z-10 tracking-wide drop-shadow-[0_0_1px_rgba(0,0,0,0.5)]">
                  {dictionary?.cta?.calculateCost || "Calculate App Cost"}
                </span>
              </motion.a>
            </motion.div>

            {/* Horizontal Metrics Bar - Moved after CTA buttons and removed container */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1.2 }}
              className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6 mb-8 w-full max-w-3xl mt-16"
            >
              <div className="flex flex-col items-center text-center">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mb-2">
                  <svg
                    className="w-5 h-5 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </div>
                <div className="text-xl md:text-2xl font-bold text-white mb-1">
                  8+
                </div>
                <div className="text-xs text-gray-300">Jahre</div>
              </div>

              <div className="flex flex-col items-center text-center">
                <div className="w-10 h-10 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-lg flex items-center justify-center mb-2">
                  <svg
                    className="w-5 h-5 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M13 10V3L4 14h7v7l9-11h-7z"
                    />
                  </svg>
                </div>
                <div className="text-xl md:text-2xl font-bold text-white mb-1">
                  50%
                </div>
                <div className="text-xs text-gray-300">Schnellerer Markteintritt</div>
              </div>

              <div className="flex flex-col items-center text-center">
                <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mb-2">
                  <svg
                    className="w-5 h-5 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </div>
                <div className="text-xl md:text-2xl font-bold text-white mb-1">
                  30%
                </div>
                <div className="text-xs text-gray-300">Massive Kosteneinsparung</div>
              </div>

              <div className="flex flex-col items-center text-center">
                <div className="w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center mb-2">
                  <svg
                    className="w-5 h-5 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </div>
                <div className="text-xl md:text-2xl font-bold text-white mb-1">
                  Quality
                </div>
                <div className="text-xs text-gray-300">Focus</div>
              </div>
            </motion.div>

            {/* Trusted by Industry Leaders - Horizontal Line */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1.4 }}
              className="mt-10 w-full max-w-6xl"
            >
              <div className="text-center mb-6">
                <h3 className="text-xs font-medium text-white/60 mb-4 tracking-wide">
                  Trusted by Industry Leaders
                </h3>
              </div>

              {/* Enhanced Company Row with Logos and Stars */}
              <div className="flex flex-wrap items-center justify-center gap-6 md:gap-8">
                {/* Lufthansa - First with blue color and logo */}
                <motion.div
                  className="flex flex-col items-center p-4 hover:scale-105 transition-all duration-300 min-w-[120px]"
                  whileHover={{ scale: 1.05, y: -2 }}
                >
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-700 rounded-full flex items-center justify-center mb-3 shadow-lg overflow-hidden">
                    <img
                      src="/images/companies/lufthansa.png"
                      alt="Lufthansa"
                      className="w-full h-full object-cover filter brightness-0 invert"
                    />
                  </div>
                  <h4 className="text-white font-semibold mb-2 text-sm">
                    Lufthansa
                  </h4>
                  <div className="flex gap-0.5 mb-2">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <motion.svg
                        key={star}
                        className="w-3 h-3 text-yellow-400 fill-current"
                        viewBox="0 0 20 20"
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ duration: 0.3, delay: star * 0.1 }}
                      >
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.286 3.954a1 1 0 00.95.69h4.162c.969 0 1.371 1.24.588 1.81l-3.37 2.448a1 1 0 00-.364 1.118l1.286 3.954c.3.921-.755 1.688-1.54 1.118l-3.37-2.448a1 1 0 00-1.175 0l-3.37 2.448c-.784.57-1.838-.197-1.539-1.118l1.286-3.954a1 1 0 00-.364-1.118L2.49 9.381c-.783-.57-.38-1.81.588-1.81h4.162a1 1 0 00.95-.69l1.286-3.954z" />
                      </motion.svg>
                    ))}
                  </div>
                  <span className="text-xs text-blue-400 font-medium">
                    Aviation Leader
                  </span>
                </motion.div>

                {/* TOGG */}
                <motion.div
                  className="flex flex-col items-center p-4 hover:scale-105 transition-all duration-300 min-w-[120px]"
                  whileHover={{ scale: 1.05, y: -2 }}
                >
                  <div className="w-12 h-12 bg-gradient-to-br from-slate-600 to-slate-800 rounded-full flex items-center justify-center mb-3 shadow-lg overflow-hidden">
                    <img
                      src="/images/companies/togg.png"
                      alt="TOGG"
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <h4 className="text-white font-semibold mb-2 text-sm">
                    TOGG
                  </h4>
                  <div className="flex gap-0.5 mb-2">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <motion.svg
                        key={star}
                        className="w-3 h-3 text-yellow-400 fill-current"
                        viewBox="0 0 20 20"
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ duration: 0.3, delay: star * 0.1 }}
                      >
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.286 3.954a1 1 0 00.95.69h4.162c.969 0 1.371 1.24.588 1.81l-3.37 2.448a1 1 0 00-.364 1.118l1.286 3.954c.3.921-.755 1.688-1.54 1.118l-3.37-2.448a1 1 0 00-1.175 0l-3.37 2.448c-.784.57-1.838-.197-1.539-1.118l1.286-3.954a1 1 0 00-.364-1.118L2.49 9.381c-.783-.57-.38-1.81.588-1.81h4.162a1 1 0 00.95-.69l1.286-3.954z" />
                      </motion.svg>
                    ))}
                  </div>
                  <span className="text-xs text-emerald-400 font-medium">
                    Automotive Tech
                  </span>
                </motion.div>

                {/* Union Investment */}
                <motion.div
                  className="flex flex-col items-center p-4 hover:scale-105 transition-all duration-300 min-w-[120px]"
                  whileHover={{ scale: 1.05, y: -2 }}
                >
                  <div className="w-12 h-12 bg-gradient-to-br from-slate-600 to-slate-800 rounded-full flex items-center justify-center mb-3 shadow-lg overflow-hidden">
                    <img
                      src="/images/companies/union-investment.png"
                      alt="Union Investment"
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <h4 className="text-white font-semibold mb-2 text-sm text-center">
                    Union Investment
                  </h4>
                  <div className="flex gap-0.5 mb-2">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <motion.svg
                        key={star}
                        className="w-3 h-3 text-yellow-400 fill-current"
                        viewBox="0 0 20 20"
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ duration: 0.3, delay: star * 0.1 }}
                      >
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.286 3.954a1 1 0 00.95.69h4.162c.969 0 1.371 1.24.588 1.81l-3.37 2.448a1 1 0 00-.364 1.118l1.286 3.954c.3.921-.755 1.688-1.54 1.118l-3.37-2.448a1 1 0 00-1.175 0l-3.37 2.448c-.784.57-1.838-.197-1.539-1.118l1.286-3.954a1 1 0 00-.364-1.118L2.49 9.381c-.783-.57-.38-1.81.588-1.81h4.162a1 1 0 00.95-.69l1.286-3.954z" />
                      </motion.svg>
                    ))}
                  </div>
                  <span className="text-xs text-violet-400 font-medium">
                    Financial Services
                  </span>
                </motion.div>

                {/* Ultimind */}
                <motion.div
                  className="flex flex-col items-center p-4 hover:scale-105 transition-all duration-300 min-w-[120px]"
                  whileHover={{ scale: 1.05, y: -2 }}
                >
                  <div className="w-12 h-12 bg-gradient-to-br from-slate-600 to-slate-800 rounded-full flex items-center justify-center mb-3 shadow-lg overflow-hidden">
                    <span className="text-lg font-bold text-white">U</span>
                  </div>
                  <h4 className="text-white font-semibold mb-2 text-sm">
                    Ultimind
                  </h4>
                  <div className="flex gap-0.5 mb-2">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <motion.svg
                        key={star}
                        className="w-3 h-3 text-yellow-400 fill-current"
                        viewBox="0 0 20 20"
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ duration: 0.3, delay: star * 0.1 }}
                      >
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.286 3.954a1 1 0 00.95.69h4.162c.969 0 1.371 1.24.588 1.81l-3.37 2.448a1 1 0 00-.364 1.118l1.286 3.954c.3.921-.755 1.688-1.54 1.118l-3.37-2.448a1 1 0 00-1.175 0l-3.37 2.448c-.784.57-1.838-.197-1.539-1.118l1.286-3.954a1 1 0 00-.364-1.118L2.49 9.381c-.783-.57-.38-1.81.588-1.81h4.162a1 1 0 00.95-.69l1.286-3.954z" />
                      </motion.svg>
                    ))}
                  </div>
                  <span className="text-xs text-cyan-400 font-medium">
                    AI Technology
                  </span>
                </motion.div>

                {/* Walenwein */}
                <motion.div
                  className="flex flex-col items-center p-4 hover:scale-105 transition-all duration-300 min-w-[120px]"
                  whileHover={{ scale: 1.05, y: -2 }}
                >
                  <div className="w-12 h-12 bg-gradient-to-br from-slate-600 to-slate-800 rounded-full flex items-center justify-center mb-3 shadow-lg overflow-hidden">
                    <span className="text-lg font-bold text-white">W</span>
                  </div>
                  <h4 className="text-white font-semibold mb-2 text-sm">
                    Walenwein
                  </h4>
                  <div className="flex gap-0.5 mb-2">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <motion.svg
                        key={star}
                        className="w-3 h-3 text-yellow-400 fill-current"
                        viewBox="0 0 20 20"
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ duration: 0.3, delay: star * 0.1 }}
                      >
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.286 3.954a1 1 0 00.95.69h4.162c.969 0 1.371 1.24.588 1.81l-3.37 2.448a1 1 0 00-.364 1.118l1.286 3.954c.3.921-.755 1.688-1.54 1.118l-3.37-2.448a1 1 0 00-1.175 0l-3.37 2.448c-.784.57-1.838-.197-1.539-1.118l1.286-3.954a1 1 0 00-.364-1.118L2.49 9.381c-.783-.57-.38-1.81.588-1.81h4.162a1 1 0 00.95-.69l1.286-3.954z" />
                      </motion.svg>
                    ))}
                  </div>
                  <span className="text-xs text-orange-400 font-medium">
                    Real Estate
                  </span>
                </motion.div>
              </div>
            </motion.div>
          </div>
        </div>

        {/* Mobile Metric Cards - Centered and Wider */}
        <div className="xl:hidden w-full mt-12 px-4">
          <div className="grid grid-cols-1 gap-5">
            {/* App Development Speed */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="bg-gradient-to-br from-blue-900/40 to-blue-800/20 border border-blue-500/30 rounded-lg p-5 relative overflow-hidden"
            >
              {/* Glowing accent */}
              <div className="absolute -top-10 -right-10 w-20 h-20 rounded-full bg-gradient-to-br from-blue-500/20 to-blue-400/5 blur-xl opacity-60" />

              <div className="flex justify-between items-start">
                <div>
                  <div className="text-xs font-medium text-blue-400 mb-1">
                    {dictionary?.metrics?.development?.label ||
                      "Flutter App Development"}
                  </div>
                  <div className="flex items-baseline">
                    <div className="text-2xl font-bold text-white">
                      {dictionary?.metrics?.development?.value || "40%"}
                    </div>
                    <div className="ml-1 text-xs text-gray-300">
                      {dictionary?.metrics?.development?.description ||
                        "faster"}
                    </div>
                  </div>
                </div>
                <div className="p-1.5 rounded-lg bg-white/10 border border-white/5">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={1.8}
                    stroke="currentColor"
                    className="w-4 h-4 text-blue-400"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"
                    />
                  </svg>
                </div>
              </div>

              {/* Mini sparkline */}
              <div className="relative h-8 mt-2 mb-2">
                <svg
                  width="100%"
                  height="100%"
                  viewBox="0 0 100 30"
                  preserveAspectRatio="none"
                >
                  {/* Area under the sparkline */}
                  <motion.path
                    d="M 0,30 L 15,25 L 30,20 L 45,15 L 60,10 L 75,5 L 90,10 L 100,0 L 100,30 L 0,30 Z"
                    className="fill-blue-500/20"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 1, delay: 0.5 }}
                  />

                  {/* Sparkline */}
                  <motion.path
                    d="M 0,30 L 15,25 L 30,20 L 45,15 L 60,10 L 75,5 L 90,10 L 100,0"
                    className="stroke-blue-500 fill-none stroke-2"
                    initial={{ pathLength: 0 }}
                    animate={{ pathLength: 1 }}
                    transition={{ duration: 1.5, delay: 0.3, ease: "easeOut" }}
                  />

                  {/* End point */}
                  <motion.circle
                    cx={100}
                    cy={0}
                    r={2}
                    className="text-blue-400 fill-current"
                    initial={{ scale: 0 }}
                    animate={{ scale: [0, 1.2, 1] }}
                    transition={{ duration: 0.5, delay: 1.8 }}
                  />
                </svg>
              </div>

              {/* How section */}
              <div className="pt-2">
                <div className="text-[10px] font-semibold text-blue-400 mb-1">
                  {dictionary?.metrics?.development?.howTitle ||
                    "Cross-Platform:"}
                </div>
                <div className="text-[10px] text-gray-300 leading-tight">
                  {dictionary?.metrics?.development?.howExplanation ||
                    "One Flutter code for iOS & Android - no double development"}
                </div>
              </div>
            </motion.div>

            {/* App Store Launch */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="bg-gradient-to-br from-violet-900/40 to-violet-800/20 border border-violet-500/30 rounded-lg p-5 relative overflow-hidden"
            >
              {/* Glowing accent */}
              <div className="absolute -top-10 -right-10 w-20 h-20 rounded-full bg-gradient-to-br from-violet-500/20 to-violet-400/5 blur-xl opacity-60" />

              <div className="flex justify-between items-start">
                <div>
                  <div className="text-xs font-medium text-violet-400 mb-1">
                    {dictionary?.metrics?.timeToMarket?.label ||
                      "App Store Launch"}
                  </div>
                  <div className="flex items-baseline">
                    <div className="text-2xl font-bold text-white">
                      {dictionary?.metrics?.timeToMarket?.value || "50%"}
                    </div>
                    <div className="ml-1 text-xs text-gray-300">
                      {dictionary?.metrics?.timeToMarket?.description ||
                        "faster"}
                    </div>
                  </div>
                </div>
                <div className="p-1.5 rounded-lg bg-white/10 border border-white/5">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={1.8}
                    stroke="currentColor"
                    className="w-4 h-4 text-violet-400"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M13.5 21v-7.5a.75.75 0 01.75-.75h3a.75.75 0 01.75.75V21m-4.5 0H2.36m11.14 0H18m0 0h3.64m-1.39 0V9.349m-16.5 11.65V9.35m0 0a3.001 3.001 0 003.75-.615A2.993 2.993 0 009.75 9.75c.896 0 1.7-.393 2.25-1.016a2.993 2.993 0 002.25 1.016c.896 0 1.7-.393 2.25-1.016a3.001 3.001 0 003.75.614m-16.5 0a3.001 3.001 0 01-.094-2.43 3.001 3.001 0 011.357-2.247m5.487 0a3.001 3.001 0 013.75.614m-16.5 0a3.001 3.001 0 01.094-2.43 3.001 3.001 0 012.25-1.016 2.993 2.993 0 012.25 1.016 2.993 2.993 0 002.25-1.016 3.001 3.001 0 013.75-.614m-16.5 0V9.349m0 0h16.5"
                    />
                  </svg>
                </div>
              </div>

              {/* Mini sparkline */}
              <div className="relative h-8 mt-2 mb-2">
                <svg
                  width="100%"
                  height="100%"
                  viewBox="0 0 100 30"
                  preserveAspectRatio="none"
                >
                  {/* Area under the sparkline */}
                  <motion.path
                    d="M 0,30 L 15,25 L 30,20 L 45,15 L 60,10 L 75,5 L 90,2 L 100,0 L 100,30 L 0,30 Z"
                    className="fill-violet-500/20"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 1, delay: 0.5 }}
                  />

                  {/* Sparkline */}
                  <motion.path
                    d="M 0,30 L 15,25 L 30,20 L 45,15 L 60,10 L 75,5 L 90,2 L 100,0"
                    className="stroke-violet-500 fill-none stroke-2"
                    initial={{ pathLength: 0 }}
                    animate={{ pathLength: 1 }}
                    transition={{ duration: 1.5, delay: 0.3, ease: "easeOut" }}
                  />

                  {/* End point */}
                  <motion.circle
                    cx={100}
                    cy={0}
                    r={2}
                    className="text-violet-400 fill-current"
                    initial={{ scale: 0 }}
                    animate={{ scale: [0, 1.2, 1] }}
                    transition={{ duration: 0.5, delay: 1.8 }}
                  />
                </svg>
              </div>

              {/* How section */}
              <div className="pt-2">
                <div className="text-[10px] font-semibold text-violet-400 mb-1">
                  {dictionary?.metrics?.timeToMarket?.howTitle || "Automated:"}
                </div>
                <div className="text-[10px] text-gray-300 leading-tight">
                  {dictionary?.metrics?.timeToMarket?.howExplanation ||
                    "CI/CD Pipeline for automatic tests & App Store deployment"}
                </div>
              </div>
            </motion.div>

            {/* App Development Costs */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.5 }}
              className="bg-gradient-to-br from-emerald-900/40 to-emerald-800/20 border border-emerald-500/30 rounded-lg p-5 relative overflow-hidden"
            >
              {/* Glowing accent */}
              <div className="absolute -top-10 -right-10 w-20 h-20 rounded-full bg-gradient-to-br from-emerald-500/20 to-emerald-400/5 blur-xl opacity-60" />

              <div className="flex justify-between items-start">
                <div>
                  <div className="text-xs font-medium text-emerald-400 mb-1">
                    {dictionary?.metrics?.costSaving?.label ||
                      "App Development Costs"}
                  </div>
                  <div className="flex items-baseline">
                    <div className="text-2xl font-bold text-white">
                      {dictionary?.metrics?.costSaving?.value || "30%"}
                    </div>
                    <div className="ml-1 text-xs text-gray-300">
                      {dictionary?.metrics?.costSaving?.description ||
                        "cheaper"}
                    </div>
                  </div>
                </div>
                <div className="p-1.5 rounded-lg bg-white/10 border border-white/5">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={1.8}
                    stroke="currentColor"
                    className="w-4 h-4 text-emerald-400"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                    />
                  </svg>
                </div>
              </div>

              {/* Mini sparkline */}
              <div className="relative h-8 mt-2 mb-2">
                <svg
                  width="100%"
                  height="100%"
                  viewBox="0 0 100 30"
                  preserveAspectRatio="none"
                >
                  {/* Area under the sparkline */}
                  <motion.path
                    d="M 0,30 L 15,28 L 30,25 L 45,20 L 60,15 L 75,10 L 90,5 L 100,0 L 100,30 L 0,30 Z"
                    className="fill-emerald-500/20"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 1, delay: 0.5 }}
                  />

                  {/* Sparkline */}
                  <motion.path
                    d="M 0,30 L 15,28 L 30,25 L 45,20 L 60,15 L 75,10 L 90,5 L 100,0"
                    className="stroke-emerald-500 fill-none stroke-2"
                    initial={{ pathLength: 0 }}
                    animate={{ pathLength: 1 }}
                    transition={{ duration: 1.5, delay: 0.3, ease: "easeOut" }}
                  />

                  {/* End point */}
                  <motion.circle
                    cx={100}
                    cy={0}
                    r={2}
                    className="text-emerald-400 fill-current"
                    initial={{ scale: 0 }}
                    animate={{ scale: [0, 1.2, 1] }}
                    transition={{ duration: 0.5, delay: 1.8 }}
                  />
                </svg>
              </div>

              {/* How section */}
              <div className="pt-2">
                <div className="text-[10px] font-semibold text-emerald-400 mb-1">
                  {dictionary?.metrics?.costSaving?.howTitle ||
                    "Cross-Platform Advantage:"}
                </div>
                <div className="text-[10px] text-gray-300 leading-tight">
                  {dictionary?.metrics?.costSaving?.howExplanation ||
                    "No separate iOS & Android teams - one developer for both platforms"}
                </div>
              </div>
            </motion.div>
          </div>
        </div>
        {/* Tech Blog Floating Button mit Vorschau */}
        <div className="fixed bottom-6 right-24 z-50">
          {/* Speech Bubble */}
          <AnimatePresence>
            {isBlogOpen && currentPost && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8, y: 20 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.8, y: 20 }}
                transition={{ duration: 0.3, ease: "easeOut" }}
                className="absolute bottom-16 right-0 mb-4"
              >
                <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700 p-4 w-80 relative">
                  {/* Arrow pointing to the button */}
                  <div className="absolute -bottom-2 right-6 w-4 h-4 bg-white dark:bg-gray-800 border-r border-b border-gray-200 dark:border-gray-700 transform rotate-45"></div>

                  {/* Close button */}
                  <button
                    onClick={() => setIsBlogOpen(false)}
                    className="absolute top-2 right-2 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                  >
                    <X className="w-4 h-4 text-gray-500" />
                  </button>

                  {/* Header */}
                  <div className="flex items-center gap-2 mb-3">
                    <div className="w-6 h-6 bg-gradient-to-r from-violet-500 to-purple-500 rounded-full flex items-center justify-center">
                      <TrendingUp className="w-3 h-3 text-white" />
                    </div>
                    <span className="text-sm font-medium text-gray-600 dark:text-gray-300">
                      Neuester Artikel
                    </span>
                    {recentPosts.length > 1 && (
                      <div className="flex gap-1 ml-auto">
                        {recentPosts.map((_, index) => (
                          <div
                            key={index}
                            className={`w-1.5 h-1.5 rounded-full transition-colors ${
                              index === currentPostIndex
                                ? "bg-violet-500"
                                : "bg-gray-300 dark:bg-gray-600"
                            }`}
                          />
                        ))}
                      </div>
                    )}
                  </div>

                  {/* Current Post */}
                  <motion.div
                    key={currentPostIndex}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                    className="cursor-pointer group"
                    onClick={() => handlePostClick(currentPost.slug)}
                  >
                    {/* Post Image */}
                    <div className="relative h-32 rounded-lg overflow-hidden mb-3">
                      <Image
                        src={currentPost.featuredImage}
                        alt={currentPost.title}
                        fill
                        className="object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />

                      {/* Category Badge */}
                      <div className="absolute top-2 left-2">
                        <span className="bg-violet-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                          {currentPost.category.toUpperCase()}
                        </span>
                      </div>
                    </div>

                    {/* Post Content */}
                    <h3 className="font-bold text-gray-900 dark:text-white mb-2 line-clamp-2 group-hover:text-violet-600 dark:group-hover:text-violet-400 transition-colors">
                      {currentPost.title}
                    </h3>

                    <p className="text-sm text-gray-600 dark:text-gray-300 mb-3 line-clamp-2">
                      {currentPost.excerpt}
                    </p>

                    {/* Meta Info */}
                    <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                      <div className="flex items-center gap-3">
                        <div className="flex items-center gap-1">
                          <Clock className="w-3 h-3" />
                          <span>{currentPost.readingTime} Min</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Eye className="w-3 h-3" />
                          <span>{currentPost.views.toLocaleString()}</span>
                        </div>
                      </div>
                      <span>
                        {new Date(currentPost.publishedAt).toLocaleDateString(
                          "de-DE"
                        )}
                      </span>
                    </div>
                  </motion.div>

                  {/* View All Button */}
                  <button
                    onClick={handleBlogClick}
                    className="w-full mt-4 bg-gradient-to-r from-violet-500 to-purple-500 hover:from-violet-600 hover:to-purple-600 text-white py-2 px-4 rounded-lg font-medium transition-all duration-300 text-sm"
                  >
                    Alle Artikel ansehen
                  </button>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Floating Button */}
          <motion.button
            onClick={() => setIsBlogOpen(!isBlogOpen)}
            initial={{ opacity: 0, x: 100 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 2 }}
            className="group relative overflow-hidden bg-gradient-to-r from-violet-600/90 to-purple-600/90 backdrop-blur-md text-white px-4 py-3 rounded-full font-medium border border-white/10 flex items-center gap-2 shadow-lg hover:shadow-xl transition-all duration-300"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {/* Pulsing Effect */}
            <motion.div
              className="absolute inset-0 rounded-full bg-violet-400/30"
              animate={{
                scale: [1, 1.2, 1],
                opacity: [0.3, 0, 0.3],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                repeatType: "loop",
              }}
            />

            {/* Notification Badge */}
            {recentPosts.length > 0 && (
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 3 }}
                className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center text-xs font-bold text-white shadow-lg"
              >
                {recentPosts.length}
              </motion.div>
            )}

            <div className="w-5 h-5 rounded-full bg-white/20 flex items-center justify-center relative z-10">
              <motion.div
                animate={{ rotate: isBlogOpen ? 180 : 0 }}
                transition={{ duration: 0.3 }}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={2}
                  stroke="currentColor"
                  className="w-3 h-3"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M12 7.5h1.5m-1.5 3h1.5m-7.5 3h7.5m-7.5 3h7.5m3-9h3.375c.621 0 1.125.504 1.125 1.125V18a2.25 2.25 0 01-2.25 2.25M16.5 7.5V18a2.25 2.25 0 002.25 2.25M16.5 7.5V4.875c0-.621-.504-1.125-1.125-1.125H4.125C3.504 3.75 3 4.254 3 4.875V18a2.25 2.25 0 002.25 2.25h6.75"
                  />
                </svg>
              </motion.div>
            </div>
            <span className="text-sm relative z-10">Tech Blog</span>
          </motion.button>
        </div>
      </div>
    </div>
  );
};
