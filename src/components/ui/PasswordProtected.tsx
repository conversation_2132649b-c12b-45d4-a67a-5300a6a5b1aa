"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { <PERSON>, Eye, EyeOff, Shield, AlertCircle } from "lucide-react";
import { Button } from "./Button";

interface PasswordProtectedProps {
  children: React.ReactNode;
  clientId: string;
  redirectPath: string;
  title?: string;
  description?: string;
}

export function PasswordProtected({
  children,
  clientId,
  redirectPath,
  title = "Access Protected Content",
  description = "Please enter the access password that was provided to you.",
}: PasswordProtectedProps) {
  const [password, setPassword] = useState("");
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [attempts, setAttempts] = useState(0);

  // Check if already authenticated on mount
  useEffect(() => {
    const sessionKey = `auth_${clientId}`;
    const authData = sessionStorage.getItem(sessionKey);

    if (authData) {
      try {
        const parsed = JSON.parse(authData);
        const now = Date.now();

        // Check if session is still valid (4 hours)
        if (parsed.expires > now) {
          setIsAuthenticated(true);
          return;
        } else {
          // Session expired, remove it
          sessionStorage.removeItem(sessionKey);
        }
      } catch (e) {
        // Invalid session data, remove it
        sessionStorage.removeItem(sessionKey);
      }
    }

    setIsAuthenticated(false);
  }, [clientId]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!password.trim()) {
      setError("Please enter a password");
      return;
    }

    setIsLoading(true);
    setError("");

    try {
      const response = await fetch("/api/verify-password", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          clientId,
          password: password.trim(),
        }),
      });

      const data = await response.json();

      if (data.success) {
        // Store authentication in session storage
        const sessionKey = `auth_${clientId}`;
        const authData = {
          authenticated: true,
          expires: Date.now() + 4 * 60 * 60 * 1000, // 4 hours
        };
        sessionStorage.setItem(sessionKey, JSON.stringify(authData));

        setIsAuthenticated(true);
        setError("");
      } else {
        setAttempts((prev) => prev + 1);
        setError(data.error || "Invalid password");
        setPassword("");

        // Lock out after 5 failed attempts
        if (attempts >= 4) {
          setError("Too many failed attempts. Please try again later.");
          setTimeout(() => {
            setAttempts(0);
            setError("");
          }, 300000); // 5 minutes lockout
        }
      }
    } catch (error) {
      console.error("Password verification error:", error);
      setError("Connection error. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  // Show loading state while checking authentication
  if (isAuthenticated === null) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
        <div className="flex items-center space-x-3">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full"
          />
          <span className="text-gray-600 dark:text-gray-300">
            Checking access...
          </span>
        </div>
      </div>
    );
  }

  // Show password form if not authenticated
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="w-full max-w-md"
        >
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 p-8">
            {/* Header */}
            <div className="text-center mb-8">
              <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center mx-auto mb-4">
                <Shield className="w-8 h-8 text-blue-600 dark:text-blue-400" />
              </div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                {title}
              </h1>
              <p className="text-gray-600 dark:text-gray-400 text-sm">
                {description}
              </p>
            </div>

            {/* Form */}
            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label
                  htmlFor="password"
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
                >
                  Access Password
                </label>
                <div className="relative">
                  <input
                    type={showPassword ? "text" : "password"}
                    id="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white transition-colors"
                    placeholder="Enter your access password"
                    disabled={isLoading || attempts >= 5}
                    autoComplete="off"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                    disabled={isLoading}
                  >
                    {showPassword ? (
                      <EyeOff className="w-5 h-5" />
                    ) : (
                      <Eye className="w-5 h-5" />
                    )}
                  </button>
                </div>
              </div>

              {/* Error Message */}
              <AnimatePresence>
                {error && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: "auto" }}
                    exit={{ opacity: 0, height: 0 }}
                    className="flex items-center space-x-2 text-red-600 dark:text-red-400 text-sm bg-red-50 dark:bg-red-900/20 p-3 rounded-lg border border-red-200 dark:border-red-800"
                  >
                    <AlertCircle className="w-4 h-4 flex-shrink-0" />
                    <span>{error}</span>
                  </motion.div>
                )}
              </AnimatePresence>

              {/* Submit Button */}
              <Button
                type="submit"
                className="w-full"
                disabled={isLoading || attempts >= 5}
              >
                {isLoading ? (
                  <div className="flex items-center justify-center space-x-2">
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{
                        duration: 1,
                        repeat: Infinity,
                        ease: "linear",
                      }}
                      className="w-4 h-4 border-2 border-white border-t-transparent rounded-full"
                    />
                    <span>Verifying...</span>
                  </div>
                ) : (
                  <div className="flex items-center justify-center space-x-2">
                    <Lock className="w-4 h-4" />
                    <span>Access Content</span>
                  </div>
                )}
              </Button>

              {/* Attempts Warning */}
              {attempts > 0 && attempts < 5 && (
                <p className="text-center text-sm text-yellow-600 dark:text-yellow-400">
                  {5 - attempts} attempt{5 - attempts !== 1 ? "s" : ""}{" "}
                  remaining
                </p>
              )}

              {/* Help Text */}
              <div className="text-center">
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Can't find your password? Check your email or contact support.
                </p>
              </div>
            </form>
          </div>
        </motion.div>
      </div>
    );
  }

  // Show protected content if authenticated
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      {children}
    </motion.div>
  );
}
