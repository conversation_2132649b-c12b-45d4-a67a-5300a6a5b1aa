"use client";

import React from "react";
import { motion, Variants } from "framer-motion";
import { useInView } from "react-intersection-observer";

export type AnimationDirection = "left" | "right" | "top" | "bottom";

interface ScrollAnimationProps {
  children: React.ReactNode;
  direction?: AnimationDirection;
  delay?: number;
  duration?: number;
  threshold?: number;
  className?: string;
  distanceOffset?: number; // Distance to travel (in pixels)
  once?: boolean;
  style?: React.CSSProperties;
}

export function ScrollAnimation({
  children,
  direction = "left",
  delay = 0,
  duration = 0.6,
  threshold = 0.1,
  className = "",
  distanceOffset = 50,
  once = false, // Changed default to false so animations trigger on each scroll into view
  style,
}: ScrollAnimationProps) {
  const [ref, inView] = useInView({
    triggerOnce: once,
    threshold,
    rootMargin: "-10px 0px", // Reduced for more precise triggering
  });

  // Define animations based on direction
  const variants: Variants = {
    hidden: {
      x: direction === "left" ? -distanceOffset : direction === "right" ? distanceOffset : 0,
      y: direction === "top" ? -distanceOffset : direction === "bottom" ? distanceOffset : 0,
      opacity: 0,
    },
    visible: {
      x: 0,
      y: 0,
      opacity: 1,
      transition: {
        duration,
        delay,
        ease: [0.22, 1, 0.36, 1], // Custom cubic bezier for smooth animation
      },
    },
  };

  return (
    <motion.div
      ref={ref}
      initial="hidden"
      animate={inView ? "visible" : "hidden"}
      variants={variants}
      className={className}
      style={style}
    >
      {children}
    </motion.div>
  );
}

// Group version that staggers its children
interface ScrollAnimationGroupProps {
  children: React.ReactNode;
  direction?: AnimationDirection;
  staggerDelay?: number;
  threshold?: number;
  className?: string;
  childClassName?: string;
  distanceOffset?: number;
  once?: boolean;
}

export function ScrollAnimationGroup({
  children,
  direction = "left",
  staggerDelay = 0.1,
  threshold = 0.1,
  className = "",
  childClassName = "",
  distanceOffset = 50,
  once = false, // Changed default to false to match ScrollAnimation component
}: ScrollAnimationGroupProps) {
  const [ref, inView] = useInView({
    triggerOnce: once,
    threshold,
    rootMargin: "-10px 0px", // Reduced for more precise triggering
  });

  // Container animation
  const containerVariants: Variants = {
    hidden: {},
    visible: {
      transition: {
        staggerChildren: staggerDelay,
      },
    },
  };

  // Child animations based on direction
  const childVariants: Variants = {
    hidden: {
      x: direction === "left" ? -distanceOffset : direction === "right" ? distanceOffset : 0,
      y: direction === "top" ? -distanceOffset : direction === "bottom" ? distanceOffset : 0,
      opacity: 0,
    },
    visible: {
      x: 0,
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: [0.22, 1, 0.36, 1], // Custom cubic bezier for smooth animation
      },
    },
  };

  // Wrap each child with animation
  const animatedChildren = React.Children.map(children, (child) => {
    if (React.isValidElement(child)) {
      return (
        <motion.div
          variants={childVariants}
          className={childClassName}
        >
          {child}
        </motion.div>
      );
    }
    return child;
  });

  return (
    <motion.div
      ref={ref}
      initial="hidden"
      animate={inView ? "visible" : "hidden"}
      variants={containerVariants}
      className={className}
    >
      {animatedChildren}
    </motion.div>
  );
}
