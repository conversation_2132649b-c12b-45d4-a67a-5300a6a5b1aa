'use client'

import React, { useState } from 'react'
import Image, { ImageProps } from 'next/image'
import { useAccessibility } from '@/providers/AccessibilityProvider'

interface AccessibleImageProps extends Omit<ImageProps, 'alt'> {
  alt: string
  caption?: string
  longDescription?: string
  showCaption?: boolean
}

export function AccessibleImage({
  alt,
  caption,
  longDescription,
  showCaption = false,
  ...props
}: AccessibleImageProps) {
  const { settings, speakText } = useAccessibility()
  const [isLoaded, setIsLoaded] = useState(false)
  const [error, setError] = useState(false)
  
  // Generiere eine eindeutige ID für ARIA-Attribute
  const id = React.useId()
  const descriptionId = `img-desc-${id}`
  
  // Funktion zum Vorlesen der Bildbeschreibung
  const handleReadDescription = () => {
    const textToRead = longDescription || caption || alt
    speakText(textToRead)
  }
  
  return (
    <figure className="relative">
      {/* Bild mit verbesserter Barrierefreiheit */}
      <div 
        className={`relative ${error ? 'bg-gray-100 dark:bg-gray-800 flex items-center justify-center' : ''}`}
        style={{ aspectRatio: props.width && props.height ? `${props.width}/${props.height}` : 'auto' }}
      >
        {!error ? (
          <Image
            {...props}
            alt={alt}
            aria-describedby={longDescription ? descriptionId : undefined}
            onLoad={() => setIsLoaded(true)}
            onError={() => setError(true)}
            className={`${props.className || ''} ${settings.highContrast ? 'grayscale contrast-125' : ''}`}
          />
        ) : (
          <div className="text-sm text-gray-500 dark:text-gray-400 p-4 text-center">
            {alt || 'Bild konnte nicht geladen werden'}
          </div>
        )}
        
        {/* Lade-Indikator */}
        {!isLoaded && !error && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-800 bg-opacity-50 dark:bg-opacity-50">
            <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
          </div>
        )}
        
        {/* Vorlese-Button für Bildschirmleser */}
        {settings.textToSpeechEnabled && (
          <button
            type="button"
            onClick={handleReadDescription}
            className="absolute bottom-2 right-2 bg-primary text-white p-1 rounded-full opacity-70 hover:opacity-100 focus:opacity-100 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
            aria-label="Bildbeschreibung vorlesen"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M11 5L6 9H2v6h4l5 4V5z"></path>
              <path d="M15.54 8.46a5 5 0 0 1 0 7.07"></path>
              <path d="M19.07 4.93a10 10 0 0 1 0 14.14"></path>
            </svg>
          </button>
        )}
      </div>
      
      {/* Bildunterschrift */}
      {(showCaption && caption) && (
        <figcaption className="text-sm text-gray-600 dark:text-gray-400 mt-2 text-center">
          {caption}
        </figcaption>
      )}
      
      {/* Lange Beschreibung (für Screenreader) */}
      {longDescription && (
        <div id={descriptionId} className="sr-only">
          {longDescription}
        </div>
      )}
    </figure>
  )
}
