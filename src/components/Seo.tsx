import { Metadata } from "next";

export interface SeoProps {
  title: string;
  description: string;
  keywords?: string;
  canonical?: string;
  locale: string;
  alternateLanguages?: Array<{
    locale: string;
    href: string;
  }>;
  openGraph?: {
    title?: string;
    description?: string;
    image?: string;
    type?: "website" | "article" | "product";
    url?: string;
  };
  twitter?: {
    title?: string;
    description?: string;
    image?: string;
    card?: "summary" | "summary_large_image";
  };
  noIndex?: boolean;
  noFollow?: boolean;
  robotsDirectives?: string[];
}

const DEFAULT_DOMAIN = "https://innovatio-pro.com";

export function generateSeoMetadata({
  title,
  description,
  keywords,
  canonical,
  locale,
  alternateLanguages = [],
  openGraph,
  twitter,
  noIndex = false,
  noFollow = false,
  robotsDirectives = [],
}: SeoProps): Metadata {
  // Build robots meta content
  const robotsContent: string[] = [];
  if (noIndex) robotsContent.push("noindex");
  if (noFollow) robotsContent.push("nofollow");
  if (!noIndex) robotsContent.push("index");
  if (!noFollow) robotsContent.push("follow");
  robotsContent.push(...robotsDirectives);

  // Default OpenGraph values
  const ogTitle = openGraph?.title || title;
  const ogDescription = openGraph?.description || description;
  const ogImage = openGraph?.image || `${DEFAULT_DOMAIN}/images/og-default.jpg`;
  const ogType = openGraph?.type || "website";
  const ogUrl = openGraph?.url || canonical || DEFAULT_DOMAIN;

  // Default Twitter values
  const twitterTitle = twitter?.title || title;
  const twitterDescription = twitter?.description || description;
  const twitterImage =
    twitter?.image || `${DEFAULT_DOMAIN}/images/twitter-default.jpg`;
  const twitterCard = twitter?.card || "summary_large_image";

  // Generate hreflang based on locale
  const hreflangCode =
    locale === "en"
      ? "en-US"
      : locale === "de"
      ? "de-DE"
      : locale === "ru"
      ? "ru-RU"
      : locale === "tr"
      ? "tr-TR"
      : locale === "ar"
      ? "ar-AE"
      : "en-US";

  // Build alternate languages object
  const languages: Record<string, string> = {};
  alternateLanguages.forEach(({ locale: altLocale, href }) => {
    const altHreflang =
      altLocale === "en"
        ? "en-US"
        : altLocale === "de"
        ? "de-DE"
        : altLocale === "ru"
        ? "ru-RU"
        : altLocale === "tr"
        ? "tr-TR"
        : altLocale === "ar"
        ? "ar-AE"
        : altLocale;
    languages[altHreflang] = href;
  });

  // Add x-default
  languages["x-default"] = `${DEFAULT_DOMAIN}/en`;

  return {
    title,
    description,
    keywords: keywords?.split(",").map((k) => k.trim()),
    robots: {
      index: !noIndex,
      follow: !noFollow,
      googleBot: {
        index: !noIndex,
        follow: !noFollow,
        "max-image-preview": "large",
        "max-video-preview": -1,
        "max-snippet": -1,
      },
    },
    alternates: {
      canonical,
      languages,
    },
    openGraph: {
      title: ogTitle,
      description: ogDescription,
      url: ogUrl,
      siteName: "Innovatio",
      locale: hreflangCode,
      type: ogType as any,
      images: [
        {
          url: ogImage,
          width: 1200,
          height: 630,
          alt: ogTitle,
        },
      ],
    },
    twitter: {
      card: twitterCard as any,
      title: twitterTitle,
      description: twitterDescription,
      images: [twitterImage],
    },
    metadataBase: new URL(DEFAULT_DOMAIN),
    verification: {
      google: "google-site-verification-code", // Replace with actual verification code
    },
    other: {
      "format-detection": "telephone=no",
      "theme-color": "#3b82f6",
    },
  };
}

// Utility function to create structured data script
export function createStructuredData(data: object): string {
  return JSON.stringify(data, null, 0);
}

// Common structured data generators
export const structuredDataGenerators = {
  website: (locale: string) => ({
    "@context": "https://schema.org",
    "@type": "WebSite",
    name: "Innovatio",
    url: DEFAULT_DOMAIN,
    description:
      "Professional website templates and custom web development services",
    inLanguage: locale,
    publisher: {
      "@type": "Organization",
      name: "Innovatio",
      url: DEFAULT_DOMAIN,
    },
  }),

  organization: () => ({
    "@context": "https://schema.org",
    "@type": "Organization",
    name: "Innovatio",
    url: DEFAULT_DOMAIN,
    logo: `${DEFAULT_DOMAIN}/images/logo.png`,
    sameAs: [
      "https://twitter.com/innovatio",
      "https://linkedin.com/company/innovatio",
      "https://github.com/innovatio",
    ],
    contactPoint: {
      "@type": "ContactPoint",
      telephone: "******-0123",
      contactType: "customer service",
      availableLanguage: ["English", "German", "Russian", "Turkish", "Arabic"],
    },
  }),

  breadcrumb: (items: Array<{ name: string; url: string }>) => ({
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    itemListElement: items.map((item, index) => ({
      "@type": "ListItem",
      position: index + 1,
      name: item.name,
      item: item.url,
    })),
  }),

  product: (
    name: string,
    description: string,
    price?: string,
    currency?: string
  ) => ({
    "@context": "https://schema.org",
    "@type": "Product",
    name,
    description,
    brand: {
      "@type": "Brand",
      name: "Innovatio",
    },
    ...(price &&
      currency && {
        offers: {
          "@type": "Offer",
          price,
          priceCurrency: currency,
          availability: "https://schema.org/InStock",
        },
      }),
  }),

  faq: (questions: Array<{ question: string; answer: string }>) => ({
    "@context": "https://schema.org",
    "@type": "FAQPage",
    mainEntity: questions.map(({ question, answer }) => ({
      "@type": "Question",
      name: question,
      acceptedAnswer: {
        "@type": "Answer",
        text: answer,
      },
    })),
  }),
};

export default generateSeoMetadata;
