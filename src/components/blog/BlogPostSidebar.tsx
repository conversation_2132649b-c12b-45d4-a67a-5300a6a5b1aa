"use client";

import React from "react";
import { motion } from "framer-motion";
import Image from "next/image";
import Link from "next/link";
import {
  Clock,
  Eye,
  Calendar,
  Tag,
  Share2,
  Bookmark,
  Heart,
  ArrowRight,
  TrendingUp,
  User,
} from "lucide-react";
import { Dictionary } from "@/lib/dictionary";

interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  author: {
    name: string;
    avatar: string;
    bio: string;
  };
  publishedAt: string;
  readingTime: number;
  views: number;
  category: string;
  tags: string[];
  featuredImage: string;
  slug: string;
}

interface BlogPostSidebarProps {
  dictionary: Dictionary["blog"];
  currentPost: BlogPost;
  relatedPosts: BlogPost[];
  locale: string;
}

export const BlogPostSidebar: React.FC<BlogPostSidebarProps> = ({
  dictionary,
  currentPost,
  relatedPosts,
  locale,
}) => {
  const sharePost = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: currentPost.title,
          text: currentPost.excerpt,
          url: window.location.href,
        });
      } catch (err) {
        console.log("Error sharing:", err);
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
    }
  };

  return (
    <div className="space-y-8">
      {/* Author Card */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg border border-gray-200 dark:border-gray-700"
      >
        <div className="flex items-center gap-4 mb-4">
          <div className="relative w-16 h-16">
            <Image
              src={currentPost.author.avatar}
              alt={currentPost.author.name}
              fill
              className="rounded-full object-cover"
            />
          </div>
          <div>
            <h3 className="font-bold text-gray-900 dark:text-white">
              {currentPost.author.name}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-300">
              {dictionary?.author || "Author"}
            </p>
          </div>
        </div>
        <p className="text-gray-600 dark:text-gray-300 text-sm leading-relaxed">
          {currentPost.author.bio}
        </p>
      </motion.div>

      {/* Post Stats */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg border border-gray-200 dark:border-gray-700"
      >
        <h3 className="font-bold text-gray-900 dark:text-white mb-4">
          Artikel Details
        </h3>
        <div className="space-y-3">
          <div className="flex items-center gap-3 text-sm text-gray-600 dark:text-gray-300">
            <Calendar className="w-4 h-4" />
            <span>
              {new Date(currentPost.publishedAt).toLocaleDateString("de-DE")}
            </span>
          </div>
          <div className="flex items-center gap-3 text-sm text-gray-600 dark:text-gray-300">
            <Clock className="w-4 h-4" />
            <span>
              {currentPost.readingTime} Min{" "}
              {dictionary?.readingTime || "Lesezeit"}
            </span>
          </div>
          <div className="flex items-center gap-3 text-sm text-gray-600 dark:text-gray-300">
            <Eye className="w-4 h-4" />
            <span>
              {currentPost.views.toLocaleString()}{" "}
              {dictionary?.views || "Aufrufe"}
            </span>
          </div>
        </div>
      </motion.div>

      {/* Tags */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg border border-gray-200 dark:border-gray-700"
      >
        <h3 className="font-bold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
          <Tag className="w-5 h-5" />
          {dictionary?.tags || "Tags"}
        </h3>
        <div className="flex flex-wrap gap-2">
          {currentPost.tags.map((tag, index) => (
            <span
              key={index}
              className="bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-3 py-1 rounded-full text-sm font-medium hover:bg-blue-100 dark:hover:bg-blue-900/50 transition-colors cursor-pointer"
            >
              {tag}
            </span>
          ))}
        </div>
      </motion.div>

      {/* Social Actions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg border border-gray-200 dark:border-gray-700"
      >
        <h3 className="font-bold text-gray-900 dark:text-white mb-4">
          Artikel teilen
        </h3>
        <div className="flex gap-3">
          <button
            onClick={sharePost}
            className="flex-1 flex items-center justify-center gap-2 bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 py-2 px-3 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/50 transition-colors"
          >
            <Share2 className="w-4 h-4" />
            <span className="text-sm font-medium">Teilen</span>
          </button>
          <button className="flex-1 flex items-center justify-center gap-2 bg-gray-50 dark:bg-gray-700 text-gray-700 dark:text-gray-300 py-2 px-3 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
            <Bookmark className="w-4 h-4" />
            <span className="text-sm font-medium">Merken</span>
          </button>
          <button className="flex-1 flex items-center justify-center gap-2 bg-red-50 dark:bg-red-900/30 text-red-700 dark:text-red-300 py-2 px-3 rounded-lg hover:bg-red-100 dark:hover:bg-red-900/50 transition-colors">
            <Heart className="w-4 h-4" />
            <span className="text-sm font-medium">Liken</span>
          </button>
        </div>
      </motion.div>

      {/* Related Posts */}
      {relatedPosts.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg border border-gray-200 dark:border-gray-700"
        >
          <h3 className="font-bold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
            <TrendingUp className="w-5 h-5" />
            Ähnliche Artikel
          </h3>
          <div className="space-y-4">
            {relatedPosts.slice(0, 3).map((post) => (
              <Link
                key={post.id}
                href={`/${locale}/blog/${post.slug}`}
                className="block group"
              >
                <div className="flex gap-3">
                  <div className="relative w-16 h-16 flex-shrink-0">
                    <Image
                      src={post.featuredImage}
                      alt={post.title}
                      fill
                      className="rounded-lg object-cover"
                    />
                  </div>
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-gray-900 dark:text-white line-clamp-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                      {post.title}
                    </h4>
                    <div className="flex items-center gap-2 mt-1 text-xs text-gray-500 dark:text-gray-400">
                      <Clock className="w-3 h-3" />
                      <span>{post.readingTime} Min</span>
                      <span>•</span>
                      <Eye className="w-3 h-3" />
                      <span>{post.views.toLocaleString()}</span>
                    </div>
                  </div>
                  <ArrowRight className="w-4 h-4 text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors opacity-0 group-hover:opacity-100" />
                </div>
              </Link>
            ))}
          </div>
          <Link
            href={`/${locale}/blog`}
            className="inline-flex items-center gap-2 text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium text-sm mt-4 group"
          >
            Alle Artikel ansehen
            <ArrowRight className="w-4 h-4 transition-transform group-hover:translate-x-1" />
          </Link>
        </motion.div>
      )}

      {/* Newsletter CTA */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-2xl p-6 border border-blue-200 dark:border-blue-800"
      >
        <div className="text-center">
          <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
            <User className="w-6 h-6 text-white" />
          </div>
          <h3 className="font-bold text-gray-900 dark:text-white mb-2">
            Mehr Tech-Insights?
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
            Abonnieren Sie unseren Newsletter für die neuesten Flutter- und
            AI-Trends.
          </p>
          <Link
            href={`/${locale}/blog#subscription`}
            className="inline-flex items-center gap-2 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg font-medium text-sm transition-colors"
          >
            Newsletter abonnieren
            <ArrowRight className="w-4 h-4" />
          </Link>
        </div>
      </motion.div>
    </div>
  );
};
