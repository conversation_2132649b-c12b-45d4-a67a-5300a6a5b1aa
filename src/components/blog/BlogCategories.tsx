"use client";

import React from "react";
import { motion } from "framer-motion";
import { useRouter, useSearchParams } from "next/navigation";
import {
  Code,
  Smartphone,
  Brain,
  TrendingUp,
  BookOpen,
  Zap,
  Target,
} from "lucide-react";
import { Dictionary } from "@/lib/dictionary";

interface BlogCategoriesProps {
  dictionary: Dictionary["blog"];
  currentCategory: string;
  locale: string;
}

const categories = [
  {
    id: "all",
    icon: BookOpen,
    label: "Alle Artikel",
    color: "from-gray-500 to-gray-600",
    bgColor: "bg-gray-100 dark:bg-gray-800",
    count: 45,
  },
  {
    id: "flutter",
    icon: Code,
    label: "Flutter Development",
    color: "from-blue-500 to-cyan-500",
    bgColor: "bg-blue-100 dark:bg-blue-900/30",
    count: 18,
  },
  {
    id: "mobile",
    icon: Smartphone,
    label: "Mobile Trends",
    color: "from-green-500 to-emerald-500",
    bgColor: "bg-green-100 dark:bg-green-900/30",
    count: 12,
  },
  {
    id: "ai",
    icon: Brain,
    label: "AI Integration",
    color: "from-purple-500 to-violet-500",
    bgColor: "bg-purple-100 dark:bg-purple-900/30",
    count: 8,
  },
  {
    id: "performance",
    icon: Zap,
    label: "Performance",
    color: "from-yellow-500 to-orange-500",
    bgColor: "bg-yellow-100 dark:bg-yellow-900/30",
    count: 6,
  },
  {
    id: "case-studies",
    icon: Target,
    label: "Case Studies",
    color: "from-red-500 to-pink-500",
    bgColor: "bg-red-100 dark:bg-red-900/30",
    count: 4,
  },
  {
    id: "trends",
    icon: TrendingUp,
    label: "Industry Trends",
    color: "from-indigo-500 to-blue-500",
    bgColor: "bg-indigo-100 dark:bg-indigo-900/30",
    count: 7,
  },
];

export const BlogCategories: React.FC<BlogCategoriesProps> = ({
  dictionary,
  currentCategory,
  locale,
}) => {
  const router = useRouter();
  const searchParams = useSearchParams();

  const handleCategoryChange = (categoryId: string) => {
    const params = new URLSearchParams(searchParams || undefined);

    if (categoryId === "all") {
      params.delete("category");
    } else {
      params.set("category", categoryId);
    }

    // Reset page when changing category
    params.delete("page");

    const queryString = params.toString();
    const url = `/${locale}/blog${queryString ? `?${queryString}` : ""}`;

    router.push(url);
  };

  return (
    <section className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 sticky top-0 z-40 backdrop-blur-sm bg-white/95 dark:bg-gray-800/95">
      <div className="max-w-7xl mx-auto px-4 py-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
          {/* Section Title */}
          <div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
              {dictionary?.categoriesTitle || "Kategorien"}
            </h2>
            <p className="text-gray-600 dark:text-gray-300">
              {dictionary?.categoriesDescription ||
                "Entdecken Sie Artikel zu verschiedenen Tech-Bereichen"}
            </p>
          </div>

          {/* Categories Grid */}
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-7 gap-3 lg:gap-4">
            {categories.map((category, index) => {
              const isActive = currentCategory === category.id;
              const Icon = category.icon;

              return (
                <motion.button
                  key={category.id}
                  onClick={() => handleCategoryChange(category.id)}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  whileHover={{
                    scale: 1.05,
                    y: -2,
                  }}
                  whileTap={{ scale: 0.95 }}
                  className={`
                    relative group p-4 rounded-xl transition-all duration-300 text-left
                    ${
                      isActive
                        ? "bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/40 dark:to-indigo-900/40 border-2 border-blue-500 shadow-lg shadow-blue-500/20"
                        : "bg-gray-50 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-600 hover:border-blue-300 dark:hover:border-blue-500"
                    }
                  `}
                >
                  {/* Background Glow Effect */}
                  {isActive && (
                    <motion.div
                      layoutId="categoryGlow"
                      className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-purple-500/10 rounded-xl"
                      transition={{
                        type: "spring",
                        bounce: 0.2,
                        duration: 0.6,
                      }}
                    />
                  )}

                  <div className="relative z-10">
                    {/* Icon */}
                    <div
                      className={`
                      w-12 h-12 rounded-lg flex items-center justify-center mb-3 transition-all duration-300
                      ${
                        isActive
                          ? "bg-gradient-to-br from-blue-500 to-purple-500 text-white shadow-lg"
                          : `${category.bgColor} text-gray-600 dark:text-gray-300 group-hover:scale-110`
                      }
                    `}
                    >
                      <Icon className="w-6 h-6" />
                    </div>

                    {/* Label and Count */}
                    <div>
                      <h3
                        className={`
                        font-semibold text-sm mb-1 transition-colors duration-300
                        ${
                          isActive
                            ? "text-blue-900 dark:text-blue-100"
                            : "text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400"
                        }
                      `}
                      >
                        {category.label}
                      </h3>

                      <div className="flex items-center gap-2">
                        <span
                          className={`
                          text-xs px-2 py-1 rounded-full font-medium
                          ${
                            isActive
                              ? "bg-blue-500 text-white"
                              : "bg-gray-200 dark:bg-gray-600 text-gray-600 dark:text-gray-300"
                          }
                        `}
                        >
                          {category.count}
                        </span>

                        {isActive && (
                          <motion.div
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            className="w-2 h-2 bg-blue-500 rounded-full"
                          />
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Hover Effect */}
                  <motion.div
                    className="absolute inset-0 rounded-xl bg-gradient-to-br from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                    whileHover={{ opacity: 1 }}
                  />
                </motion.button>
              );
            })}
          </div>
        </div>

        {/* Active Category Info */}
        {currentCategory !== "all" && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800"
          >
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                {React.createElement(
                  categories.find((cat) => cat.id === currentCategory)?.icon ||
                    BookOpen,
                  { className: "w-5 h-5 text-white" }
                )}
              </div>
              <div>
                <h4 className="font-semibold text-blue-900 dark:text-blue-100">
                  Aktuelle Kategorie:{" "}
                  {categories.find((cat) => cat.id === currentCategory)?.label}
                </h4>
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  {categories.find((cat) => cat.id === currentCategory)?.count}{" "}
                  Artikel gefunden
                </p>
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </section>
  );
};
