"use client";

import React from "react";
import { motion } from "framer-motion";
import { <PERSON>, <PERSON>, Brain, Zap, TrendingUp } from "lucide-react";
import { TypeAnimation } from "react-type-animation";
import { Dictionary } from "@/lib/dictionary";

interface BlogHeroProps {
  dictionary: Dictionary["blog"];
}

const TechIcon = ({
  icon: Icon,
  delay,
}: {
  icon: React.ComponentType<{ className?: string }>;
  delay: number;
}) => (
  <motion.div
    initial={{ opacity: 0, scale: 0.5, rotate: -180 }}
    animate={{ opacity: 1, scale: 1, rotate: 0 }}
    transition={{ duration: 0.8, delay, ease: "easeOut" }}
    className="relative"
  >
    <motion.div
      animate={{
        rotate: [0, 5, -5, 0],
        scale: [1, 1.05, 1],
      }}
      transition={{
        duration: 4,
        repeat: Infinity,
        ease: "easeInOut",
      }}
      className="w-16 h-16 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-xl flex items-center justify-center backdrop-blur-sm border border-blue-500/30"
    >
      <Icon className="w-8 h-8 text-blue-400" />
    </motion.div>
    <motion.div
      className="absolute inset-0 bg-blue-500/20 rounded-xl blur-xl"
      animate={{
        opacity: [0.3, 0.6, 0.3],
        scale: [0.8, 1.2, 0.8],
      }}
      transition={{
        duration: 3,
        repeat: Infinity,
        ease: "easeInOut",
      }}
    />
  </motion.div>
);

export const BlogHero: React.FC<BlogHeroProps> = ({ dictionary }) => {
  const techIcons = [
    { icon: Code, delay: 0.2 },
    { icon: Rocket, delay: 0.4 },
    { icon: Brain, delay: 0.6 },
    { icon: Zap, delay: 0.8 },
    { icon: TrendingUp, delay: 1.0 },
  ];

  return (
    <section className="relative min-h-[70vh] bg-gradient-to-br from-[#152660] via-[#1e3a8a] to-[#152660] dark:from-[#0f1629] dark:via-[#1e293b] dark:to-[#0f1629] text-white overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0">
        {/* Geometric Patterns */}
        <motion.div
          className="absolute top-20 left-10 w-32 h-32 border border-blue-500/30 rounded-full"
          animate={{ rotate: 360 }}
          transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
        />
        <motion.div
          className="absolute bottom-20 right-10 w-24 h-24 border border-purple-500/30 rounded-square rotate-45"
          animate={{ rotate: 405 }}
          transition={{ duration: 15, repeat: Infinity, ease: "linear" }}
        />

        {/* Floating Particles */}
        {[...Array(8)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-blue-400/40 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -30, 0],
              opacity: [0.4, 0.8, 0.4],
            }}
            transition={{
              duration: 3 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 py-20 flex flex-col items-center text-center">
        {/* Tech Icons Row */}
        <div className="flex items-center justify-center gap-6 mb-12">
          {techIcons.map((item, index) => (
            <TechIcon key={index} icon={item.icon} delay={item.delay} />
          ))}
        </div>

        {/* Main Title */}
        <motion.h1
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          className="text-5xl md:text-7xl font-bold mb-8 bg-gradient-to-r from-blue-300 via-purple-300 to-blue-300 text-transparent bg-clip-text leading-tight"
        >
          {dictionary?.title || "Tech-Insights"}
        </motion.h1>

        {/* Animated Subtitle */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.5 }}
          className="text-xl md:text-2xl text-blue-100 mb-10 max-w-4xl font-medium"
        >
          <TypeAnimation
            sequence={[
              "Future of Cross-Platform Development",
              2000,
              "Cutting-edge Flutter Development Insights",
              2000,
              "AI-Powered Mobile Solutions",
              2000,
            ]}
            wrapper="span"
            speed={50}
            cursor={true}
            repeat={Infinity}
          />
        </motion.div>

        {/* Description */}
        <motion.p
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.7 }}
          className="text-lg text-blue-200/80 max-w-3xl leading-relaxed"
        >
          {dictionary?.description ||
            "Explore the latest trends, insights, and innovations in mobile development. From Flutter best practices to AI integration strategies - stay ahead in the tech revolution."}
        </motion.p>

        {/* Metrics */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.9 }}
          className="grid grid-cols-3 gap-8 mt-12"
        >
          {[
            { label: "Articles", value: "50+" },
            { label: "Tech Topics", value: "15+" },
            { label: "Subscribers", value: "1K+" },
          ].map((metric, index) => (
            <div key={index} className="text-center">
              <div className="text-3xl font-bold text-blue-300 mb-2">
                {metric.value}
              </div>
              <div className="text-blue-200/70 text-sm">{metric.label}</div>
            </div>
          ))}
        </motion.div>

        {/* Scroll Indicator */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1.5 }}
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        >
          <motion.div
            animate={{ y: [0, 10, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="w-6 h-10 border-2 border-blue-400/50 rounded-full flex justify-center"
          >
            <motion.div
              animate={{ y: [0, 12, 0] }}
              transition={{ duration: 2, repeat: Infinity }}
              className="w-1 h-3 bg-blue-400 rounded-full mt-2"
            />
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};
