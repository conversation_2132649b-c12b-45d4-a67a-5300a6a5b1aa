"use client";

import React from "react";
import { motion } from "framer-motion";

export const BlogLoading: React.FC = () => {
  return (
    <div className="space-y-8">
      {/* Results Info Skeleton */}
      <div className="flex items-center justify-between">
        <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded-lg w-48 animate-pulse" />
      </div>

      {/* Blog Grid Skeleton */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {Array.from({ length: 6 }, (_, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg dark:shadow-none border border-gray-200 dark:border-gray-700 overflow-hidden"
          >
            {/* Image Skeleton */}
            <div className="h-48 bg-gray-200 dark:bg-gray-700 animate-pulse relative">
              <div className="absolute top-4 left-4 h-6 w-16 bg-gray-300 dark:bg-gray-600 rounded-full animate-pulse" />
              <div className="absolute top-4 right-4 h-6 w-16 bg-gray-300 dark:bg-gray-600 rounded-lg animate-pulse" />
            </div>

            {/* Content Skeleton */}
            <div className="p-6 space-y-4">
              {/* Meta Info Skeleton */}
              <div className="flex items-center gap-4">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-24 animate-pulse" />
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-16 animate-pulse" />
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-20 animate-pulse" />
              </div>

              {/* Title Skeleton */}
              <div className="space-y-2">
                <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-full animate-pulse" />
                <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-3/4 animate-pulse" />
              </div>

              {/* Excerpt Skeleton */}
              <div className="space-y-2">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-full animate-pulse" />
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-5/6 animate-pulse" />
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-4/6 animate-pulse" />
              </div>

              {/* Tags Skeleton */}
              <div className="flex gap-2">
                <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded-md w-16 animate-pulse" />
                <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded-md w-20 animate-pulse" />
                <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded-md w-14 animate-pulse" />
              </div>

              {/* Read More Link Skeleton */}
              <div className="h-5 bg-gray-200 dark:bg-gray-700 rounded w-24 animate-pulse" />
            </div>
          </motion.div>
        ))}
      </div>

      {/* Pagination Skeleton */}
      <div className="flex justify-center mt-12">
        <div className="flex items-center gap-2">
          {Array.from({ length: 3 }, (_, i) => (
            <div
              key={i}
              className="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse"
            />
          ))}
        </div>
      </div>
    </div>
  );
};
