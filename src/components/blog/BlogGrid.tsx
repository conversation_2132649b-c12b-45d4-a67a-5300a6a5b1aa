"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import Image from "next/image";
import Link from "next/link";
import { Clock, User, Eye, ArrowRight, Tag, Heart, Share2 } from "lucide-react";
import { Dictionary } from "@/lib/dictionary";
import { blogService, BlogPost } from "@/lib/blog-service";

interface BlogGridProps {
  dictionary: Dictionary["blog"];
  category: string;
  page: number;
  tag?: string;
  locale: string;
}

// Blog posts are now managed through the blog service

const BlogCard: React.FC<{ post: BlogPost; index: number; locale: string }> = ({
  post,
  index,
  locale,
}) => {
  const [isLiked, setIsLiked] = useState(false);
  const [likeCount, setLikeCount] = useState(
    Math.floor(Math.random() * 50) + 10
  );

  const handleLike = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsLiked(!isLiked);
    setLikeCount((prev) => (isLiked ? prev - 1 : prev + 1));
  };

  const handleShare = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (navigator.share) {
      try {
        await navigator.share({
          title: post.title,
          text: post.excerpt,
          url: `${window.location.origin}/${locale}/blog/${post.slug}`,
        });
      } catch (err) {
        console.log("Error sharing:", err);
      }
    } else {
      await navigator.clipboard.writeText(
        `${window.location.origin}/${locale}/blog/${post.slug}`
      );
    }
  };

  return (
    <motion.article
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.1 }}
      className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg dark:shadow-none border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-xl dark:hover:shadow-2xl transition-all duration-300 group"
    >
      <Link href={`/${locale}/blog/${post.slug}`} className="block">
        {/* Featured Image */}
        <div className="relative h-48 overflow-hidden">
          <Image
            src={post.featuredImage}
            alt={post.title}
            fill
            className="object-cover transition-transform duration-500 group-hover:scale-105"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent" />

          {/* Category Badge */}
          <div className="absolute top-4 left-4">
            <span className="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-medium">
              {post.category.charAt(0).toUpperCase() + post.category.slice(1)}
            </span>
          </div>

          {/* Reading Time */}
          <div className="absolute top-4 right-4 bg-black/50 backdrop-blur-sm text-white px-2 py-1 rounded-lg text-sm flex items-center gap-1">
            <Clock className="w-3 h-3" />
            {post.readingTime} Min
          </div>

          {/* Like Button Overlay */}
          <motion.button
            onClick={handleLike}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            className={`
              absolute bottom-4 right-4 w-10 h-10 rounded-full flex items-center justify-center backdrop-blur-sm transition-all duration-300
              ${
                isLiked
                  ? "bg-red-500 text-white shadow-lg shadow-red-500/25"
                  : "bg-white/20 text-white hover:bg-white/30"
              }
            `}
          >
            <motion.div
              animate={{ scale: isLiked ? [1, 1.3, 1] : 1 }}
              transition={{ duration: 0.3 }}
            >
              <Heart className={`w-5 h-5 ${isLiked ? "fill-current" : ""}`} />
            </motion.div>
          </motion.button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Meta Info */}
          <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400 mb-3">
            <div className="flex items-center gap-2">
              <User className="w-4 h-4" />
              {post.author.name}
            </div>
            <div className="flex items-center gap-2">
              <Eye className="w-4 h-4" />
              {post.views.toLocaleString()}
            </div>
            <time>
              {new Date(post.publishedAt).toLocaleDateString("de-DE")}
            </time>
          </div>

          {/* Title */}
          <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-3 line-clamp-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
            {post.title}
          </h2>

          {/* Excerpt */}
          <p className="text-gray-600 dark:text-gray-300 mb-4 line-clamp-3 leading-relaxed">
            {post.excerpt}
          </p>

          {/* Tags */}
          <div className="flex flex-wrap gap-2 mb-4">
            {post.tags.slice(0, 3).map((tag, index) => (
              <span
                key={index}
                className="inline-flex items-center gap-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-2 py-1 rounded-md text-xs"
              >
                <Tag className="w-3 h-3" />
                {tag}
              </span>
            ))}
          </div>

          {/* Actions */}
          <div className="flex items-center justify-between">
            {/* Like and Bookmark */}
            <div className="flex items-center gap-2">
              <button
                onClick={handleLike}
                className={`
                  flex items-center gap-1 px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-300
                  ${
                    isLiked
                      ? "bg-red-50 text-red-600 dark:bg-red-900/20 dark:text-red-400"
                      : "text-gray-500 dark:text-gray-400 hover:bg-red-50 dark:hover:bg-red-900/20 hover:text-red-600 dark:hover:text-red-400"
                  }
                `}
              >
                <Heart className={`w-4 h-4 ${isLiked ? "fill-current" : ""}`} />
                <span>{likeCount}</span>
              </button>

              <button
                onClick={handleShare}
                className="p-1.5 rounded-lg text-gray-500 dark:text-gray-400 hover:bg-green-50 dark:hover:bg-green-900/20 hover:text-green-600 dark:hover:text-green-400 transition-all duration-300"
              >
                <Share2 className="w-4 h-4" />
              </button>
            </div>

            {/* Read More Link */}
            <div className="inline-flex items-center gap-2 text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-semibold transition-colors group/link">
              Weiterlesen
              <ArrowRight className="w-4 h-4 transition-transform group-hover/link:translate-x-1" />
            </div>
          </div>
        </div>
      </Link>
    </motion.article>
  );
};

export const BlogGrid: React.FC<BlogGridProps> = ({
  category,
  page,
  tag,
  locale,
}) => {
  // Get posts from blog service
  const allPosts =
    category === "all"
      ? blogService.getAllPosts(locale)
      : blogService.getPostsByCategory(category, locale);

  // Filter posts based on tag if provided
  const filteredPosts = tag ? blogService.getPostsByTag(tag, locale) : allPosts;

  // Pagination
  const postsPerPage = 6;
  const startIndex = (page - 1) * postsPerPage;
  const paginatedPosts = filteredPosts.slice(
    startIndex,
    startIndex + postsPerPage
  );
  const totalPages = Math.ceil(filteredPosts.length / postsPerPage);

  if (filteredPosts.length === 0) {
    return (
      <div className="text-center py-16">
        <div className="w-24 h-24 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-6">
          <Tag className="w-12 h-12 text-gray-400" />
        </div>
        <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          Keine Artikel gefunden
        </h3>
        <p className="text-gray-600 dark:text-gray-300 max-w-md mx-auto">
          Für diese Kategorie oder diesen Tag sind aktuell keine Artikel
          verfügbar.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Results Info */}
      <div className="flex items-center justify-between">
        <p className="text-gray-600 dark:text-gray-300">
          <span className="font-semibold text-gray-900 dark:text-white">
            {filteredPosts.length}
          </span>{" "}
          {filteredPosts.length === 1 ? "Artikel" : "Artikel"} gefunden
          {category !== "all" && <span> in {category}</span>}
          {tag && <span> mit Tag &ldquo;{tag}&rdquo;</span>}
        </p>
      </div>

      {/* Blog Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {paginatedPosts.map((post, index) => (
          <BlogCard key={post.id} post={post} index={index} locale={locale} />
        ))}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center mt-12">
          <div className="flex items-center gap-2">
            {Array.from({ length: totalPages }, (_, i) => i + 1).map(
              (pageNum) => (
                <Link
                  key={pageNum}
                  href={`/${locale}/blog?${new URLSearchParams({
                    ...(category !== "all" && { category }),
                    ...(tag && { tag }),
                    page: pageNum.toString(),
                  })}`}
                  className={`
                  px-4 py-2 rounded-lg font-medium transition-all
                  ${
                    pageNum === page
                      ? "bg-blue-500 text-white shadow-lg"
                      : "bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-gray-600"
                  }
                `}
                >
                  {pageNum}
                </Link>
              )
            )}
          </div>
        </div>
      )}
    </div>
  );
};
