/* Enhanced Scrollbar Styling for Modern UI - Performance Optimized */

/* Hide scrollbar for Chrome, Safari and Opera */
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

/* Premium Custom Scrollbar Design */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(148, 163, 184, 0.1);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #3f5781, #2d3f5f);
  border-radius: 3px;
  transition: background 0.3s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #1e3a70, #243650);
}

/* Enhanced Service Card Effects - Performance Optimized */
.service-card {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  will-change: transform, box-shadow;
  transform: translateZ(0); /* Force hardware acceleration */
  backface-visibility: hidden;
}

.service-card:hover {
  transform: translateY(-8px) scale(1.02) translateZ(0);
  box-shadow: 
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    0 0 50px rgba(59, 130, 246, 0.15);
}

/* Dark mode hover effects */
.dark .service-card:hover {
  box-shadow: 
    0 25px 50px -12px rgba(0, 0, 0, 0.5),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    0 0 50px rgba(59, 130, 246, 0.2);
}

/* Gradient Radial Utility */
.bg-gradient-radial {
  background: radial-gradient(circle, var(--tw-gradient-stops));
}

/* Icon Glow Effect - Performance Optimized */
.icon-glow {
  position: relative;
  will-change: transform;
}

.icon-glow::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.3), rgba(147, 51, 234, 0.3));
  opacity: 0;
  transition: opacity 0.3s ease;
  filter: blur(20px);
  z-index: -1;
  will-change: opacity;
}

.service-card:hover .icon-glow::before {
  opacity: 1;
}

/* Shimmer Animation for Premium Feel - GPU Accelerated */
@keyframes shimmer {
  0% {
    transform: translateX(-100%) translateZ(0);
  }
  100% {
    transform: translateX(100%) translateZ(0);
  }
}

.shimmer-effect {
  position: relative;
  overflow: hidden;
}

.shimmer-effect::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transform: translateX(-100%) translateZ(0);
  transition: transform 0.6s ease;
  will-change: transform;
}

.shimmer-effect:hover::after {
  animation: shimmer 1s ease-in-out;
}

/* Enhanced Focus States for Accessibility */
.service-card:focus-visible {
  outline: 2px solid #3f5781;
  outline-offset: 4px;
  border-color: #3f5781;
}

.dark .service-card:focus-visible {
  outline-color: #8fa3c7;
  border-color: #8fa3c7;
}

/* Smooth Scroll Behavior */
html {
  scroll-behavior: smooth;
}

/* Performance Optimizations - Force GPU Acceleration */
.service-card,
.service-card *,
.animated-element {
  will-change: transform;
  backface-visibility: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transform: translateZ(0);
}

/* Mobile Touch Optimizations */
@media (hover: none) and (pointer: coarse) {
  .service-card:hover {
    transform: none;
    box-shadow: 
      0 10px 25px -5px rgba(0, 0, 0, 0.2),
      0 0 0 1px rgba(255, 255, 255, 0.1);
  }
  
  .service-card:active {
    transform: scale(0.98) translateZ(0);
  }
  
  /* Disable heavy animations on mobile */
  .animated-element {
    animation: none !important;
    transition: none !important;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .service-card,
  .animated-element,
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .shimmer-effect::after {
    animation: none;
  }
}

/* Container Queries for Better Performance */
@container (max-width: 768px) {
  .service-card {
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
  }
}

/* Critical performance hints */
.critical-performance {
  contain: layout style paint;
  content-visibility: auto;
}

/* Lazy loading optimization */
.lazy-load {
  content-visibility: auto;
  contain-intrinsic-size: 200px;
}
