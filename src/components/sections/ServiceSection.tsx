'use client'

import { Section } from '@/components/ui/Section';
import {
  motion,
  AnimatePresence,
  Variants,
  useReducedMotion,
} from "framer-motion";
import { useInView } from "react-intersection-observer";
import { type Dictionary } from "@/lib/dictionary";
import {
  Smartphone,
  Home,
  Lightbulb,
  Rocket,
  FileCode,
  Headphones,
  Brain,
  ArrowRight,
  Clock,
  DollarSign,
  Shield,
  Zap,
  BarChart,
  Check,
  ChevronDown,
  ChevronUp,
  X,
  ChevronLeft,
  ChevronRight,
  Info,
  Code,
  Database,
  Layers,
  Star,
  Award,
  Verified,
  TrendingUp,
  Users,
  Globe,
  Sparkles,
} from "lucide-react";
import { useState, useRef, useEffect, useMemo, useCallback } from "react";
import Image from "next/image";
import { cn } from "@/lib/utils";
import {
  ScrollAnimation,
  ScrollAnimationGroup,
} from "@/components/ui/ScrollAnimation";
import "./scrollbar-hide.css";

// Enhanced tech stack data with trust indicators and performance metrics
const techStackData = {
  prototype: {
    title: "Rapid Prototyping Technologies",
    description:
      "Modern tools for lightning-fast, secure prototype development",
    trustBadge: "⚡ Fast Development",
    reliability: "99.9% Uptime",
    technologies: [
      {
        name: "Flutter",
        icon: "/icons/flutter.svg",
        description: "Cross-platform UI toolkit",
        experience: "5+ years",
        projects: "15+",
      },
      {
        name: "Dart",
        icon: "/icons/flutter.svg",
        description: "High-performance language",
        experience: "5+ years",
        projects: "15+",
      },
      {
        name: "Firebase",
        icon: "/icons/firebase.svg",
        description: "Enterprise backend services",
        experience: "4+ years",
        projects: "12+",
      },
      {
        name: "Figma",
        icon: "/icons/uiux.svg",
        description: "Professional UI/UX design",
        experience: "6+ years",
        projects: "20+",
      },
    ],
  },
  mvp: {
    title: "MVP Development Stack",
    description:
      "Production-ready technologies for scalable minimum viable products",
    trustBadge: "🏆 Market-Tested",
    reliability: "Enterprise-Grade",
    technologies: [
      {
        name: "Flutter",
        icon: "/icons/flutter.svg",
        description: "Cross-platform development",
        experience: "5+ years",
        projects: "15+",
      },
      {
        name: "Dart",
        icon: "/icons/flutter.svg",
        description: "Type-safe programming",
        experience: "5+ years",
        projects: "15+",
      },
      {
        name: "Firebase",
        icon: "/icons/firebase.svg",
        description: "Scalable backend & auth",
        experience: "4+ years",
        projects: "12+",
      },
      {
        name: "Supabase",
        icon: "/icons/supabase.svg",
        description: "Modern backend service",
        experience: "3+ years",
        projects: "8+",
      },
      {
        name: "REST APIs",
        icon: "/icons/api.svg",
        description: "Secure API integration",
        experience: "6+ years",
        projects: "18+",
      },
    ],
  },
  fullstack: {
    title: "Full Stack Development Technologies",
    description:
      "Complete enterprise solution stack with security and scalability",
    trustBadge: "🔒 Enterprise Security",
    reliability: "ISO 27001 Ready",
    technologies: [
      {
        name: "Flutter",
        icon: "/icons/flutter.svg",
        description: "Mobile app development",
        experience: "5+ years",
        projects: "15+",
      },
      {
        name: "React",
        icon: "/icons/react.svg",
        description: "Modern web frontend",
        experience: "6+ years",
        projects: "18+",
      },
      {
        name: "Node.js",
        icon: "/icons/nodejs.svg",
        description: "High-performance backend",
        experience: "5+ years",
        projects: "14+",
      },
      {
        name: "Firebase",
        icon: "/icons/firebase.svg",
        description: "Cloud services platform",
        experience: "4+ years",
        projects: "12+",
      },
      {
        name: "Supabase",
        icon: "/icons/supabase.svg",
        description: "PostgreSQL backend",
        experience: "3+ years",
        projects: "8+",
      },
      {
        name: "AWS",
        icon: "/icons/aws.svg",
        description: "Enterprise cloud infrastructure",
        experience: "4+ years",
        projects: "10+",
      },
    ],
  },
  homepage: {
    title: "Website Development Stack",
    description:
      "Premium technologies for high-performance, SEO-optimized websites",
    trustBadge: "📈 98% PageSpeed",
    reliability: "SEO Optimized",
    technologies: [
      {
        name: "Next.js",
        icon: "/icons/nextjs.svg",
        description: "Production React framework",
        experience: "4+ years",
        projects: "12+",
      },
      {
        name: "React",
        icon: "/icons/react.svg",
        description: "Industry-standard UI library",
        experience: "6+ years",
        projects: "18+",
      },
      {
        name: "TypeScript",
        icon: "/icons/typescript.svg",
        description: "Enterprise-grade JavaScript",
        experience: "5+ years",
        projects: "15+",
      },
      {
        name: "Tailwind CSS",
        icon: "/icons/css.svg",
        description: "Performance-focused CSS",
        experience: "4+ years",
        projects: "12+",
      },
      {
        name: "Vercel",
        icon: "/icons/nextjs.svg",
        description: "Global edge deployment",
        experience: "4+ years",
        projects: "12+",
      },
    ],
  },
  landingpage: {
    title: "Accessible Landing Page Technologies",
    description:
      "WCAG 2.1 AA compliant technologies with premium user experience",
    trustBadge: "♿ WCAG 2.1 AA",
    reliability: "Accessibility Certified",
    technologies: [
      {
        name: "Next.js",
        icon: "/icons/nextjs.svg",
        description: "SSR React framework",
        experience: "4+ years",
        projects: "12+",
      },
      {
        name: "React",
        icon: "/icons/react.svg",
        description: "Accessible UI components",
        experience: "6+ years",
        projects: "18+",
      },
      {
        name: "Aria",
        icon: "/icons/api.svg",
        description: "Accessibility standards",
        experience: "5+ years",
        projects: "15+",
      },
      {
        name: "Tailwind CSS",
        icon: "/icons/css.svg",
        description: "Accessible design system",
        experience: "4+ years",
        projects: "12+",
      },
      {
        name: "Lighthouse",
        icon: "/icons/api.svg",
        description: "Performance & accessibility testing",
        experience: "5+ years",
        projects: "15+",
      },
      {
        name: "Framer Motion",
        icon: "/icons/api.svg",
        description: "Reduced-motion animations",
        experience: "3+ years",
        projects: "10+",
      },
    ],
  },
  qa: {
    title: "Quality Assurance Tools",
    description: "Enterprise testing suite for 99.9% reliability and security",
    trustBadge: "🛡️ 99.9% Reliable",
    reliability: "Zero-Defect Goal",
    technologies: [
      {
        name: "Jest",
        icon: "/icons/api.svg",
        description: "Comprehensive testing framework",
        experience: "5+ years",
        projects: "12+",
      },
      {
        name: "Cypress",
        icon: "/icons/api.svg",
        description: "End-to-end testing suite",
        experience: "4+ years",
        projects: "10+",
      },
      {
        name: "Flutter Test",
        icon: "/icons/flutter.svg",
        description: "Mobile app testing",
        experience: "5+ years",
        projects: "15+",
      },
      {
        name: "Postman",
        icon: "/icons/api.svg",
        description: "API testing & documentation",
        experience: "6+ years",
        projects: "18+",
      },
      {
        name: "SonarQube",
        icon: "/icons/api.svg",
        description: "Code quality analysis",
        experience: "4+ years",
        projects: "10+",
      },
    ],
  },
  consulting: {
    title: "Consulting & Analysis Tools",
    description: "Technologies we use for technical consulting",
    technologies: [
      { name: "Figma", icon: "/icons/uiux.svg", description: "UI/UX design" },
      {
        name: "Draw.io",
        icon: "/icons/api.svg",
        description: "System architecture",
      },
      {
        name: "Jira",
        icon: "/icons/api.svg",
        description: "Project management",
      },
      { name: "Notion", icon: "/icons/api.svg", description: "Documentation" },
    ],
  },
  architecture: {
    title: "Architecture & Infrastructure",
    description: "Technologies for robust system architecture",
    technologies: [
      { name: "AWS", icon: "/icons/aws.svg", description: "Cloud services" },
      {
        name: "Google Cloud",
        icon: "/icons/google-cloud.svg",
        description: "Cloud platform",
      },
      {
        name: "Docker",
        icon: "/icons/api.svg",
        description: "Containerization",
      },
      {
        name: "Kubernetes",
        icon: "/icons/api.svg",
        description: "Container orchestration",
      },
      {
        name: "CI/CD",
        icon: "/icons/api.svg",
        description: "Continuous integration",
      },
    ],
  },
  aiSolutions: {
    title: "AI & Machine Learning Stack",
    description: "Technologies for intelligent solutions",
    technologies: [
      {
        name: "TensorFlow",
        icon: "/icons/tensorflow.svg",
        description: "ML framework",
      },
      {
        name: "ChatGPT API",
        icon: "/icons/chatgpt.svg",
        description: "AI integration",
      },
      { name: "ML Kit", icon: "/icons/mlkit.svg", description: "Mobile ML" },
      {
        name: "Python",
        icon: "/icons/api.svg",
        description: "Programming language",
      },
    ],
  },
};

// Enhanced trust indicators for service types
const serviceMetrics = {
  mvp: {
    completionRate: "95%",
    avgDeliveryTime: "2-4 weeks",
    clientSatisfaction: "98%",
    successfulLaunches: "25+",
  },
  prototype: {
    completionRate: "100%",
    avgDeliveryTime: "1-2 weeks",
    clientSatisfaction: "99%",
    iterationSpeed: "5x faster",
  },
  fullstack: {
    completionRate: "92%",
    avgDeliveryTime: "6-12 weeks",
    clientSatisfaction: "96%",
    scalabilityRating: "Enterprise",
  },
  homepage: {
    completionRate: "98%",
    avgDeliveryTime: "2-3 weeks",
    clientSatisfaction: "97%",
    pagespeedScore: "95+",
  },
  landingpage: {
    completionRate: "100%",
    avgDeliveryTime: "1-2 weeks",
    clientSatisfaction: "98%",
    accessibilityScore: "AA",
  },
  qa: {
    completionRate: "100%",
    avgDeliveryTime: "1-3 weeks",
    clientSatisfaction: "99%",
    bugDetectionRate: "99.8%",
  },
};

interface ServiceCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  index: number;
  inView: boolean;
  serviceKey: string;
  onClick: () => void;
  className?: string;
}

// Create some CSS styles for the pattern grid and card effects
const patternStyles = `
  .bg-pattern-grid {
    background-image: radial-gradient(rgba(255, 255, 255, 0.15) 1px, transparent 1px);
    background-size: 10px 10px;
    background-position: 0 0;
  }
  
  .service-card {
    position: relative;
    transition: all 0.4s ease;
  }
  
  .service-card:hover .card-bg {
    opacity: 1;
    transform: scale(1.03);
  }
  
  .service-card::after {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: 1rem;
    padding: 2px;
    background: linear-gradient(130deg, #3f5781 0%, transparent 40%, transparent 60%, #5d82c3 100%);
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    opacity: 0;
    transition: opacity 0.4s ease;
  }
  
  .service-card:hover::after {
    opacity: 1;
  }
  
  .icon-glow {
    position: relative;
  }
  
  .icon-glow::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: 50%;
    padding: 0px;
    background: linear-gradient(130deg, #3f5781 0%, #5d82c3 100%);
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    opacity: 0;
    transition: all 0.3s ease;
    z-index: -1;
  }
  
  .service-card:hover .icon-glow::before {
    opacity: 1;
    padding: 2px;
  }
`;

// Enhanced Service card component with trust indicators and modern effects
const ServiceCard = ({
  title,
  description,
  icon,
  index,
  inView,
  serviceKey,
  onClick,
  className = "",
}: ServiceCardProps) => {
  // Function to render icon with enhanced effects
  const renderIcon = () => {
    return (
      <div className="relative icon-glow p-4">
        {/* Animated background orb */}
        <motion.div
          className="absolute inset-0 rounded-full bg-gradient-to-br from-blue-400/20 via-indigo-500/20 to-purple-600/20 blur-xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.6, 0.3],
          }}
          transition={{
            duration: 3,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
        <div className="scale-[1.8] group-hover:scale-[2.1] transition-all duration-500 relative z-10 will-change-transform drop-shadow-lg">
          {icon}
        </div>
      </div>
    );
  };

  // Enhanced badge system with animations
  const getBadge = () => {
    switch (serviceKey) {
      case "mvp":
        return {
          text: "Most Popular",
          bgColor:
            "bg-gradient-to-r from-orange-400 via-amber-500 to-yellow-500",
          textColor: "text-white",
          icon: <Star className="w-3 h-3" />,
          glow: "shadow-lg shadow-orange-500/25",
        };
      case "prototype":
        return {
          text: "Lightning Fast",
          bgColor: "bg-gradient-to-r from-blue-500 via-cyan-500 to-teal-500",
          textColor: "text-white",
          icon: <Zap className="w-3 h-3" />,
          glow: "shadow-lg shadow-blue-500/25",
        };
      case "fullstack":
        return {
          text: "Enterprise Ready",
          bgColor:
            "bg-gradient-to-r from-indigo-500 via-purple-600 to-pink-600",
          textColor: "text-white",
          icon: <Shield className="w-3 h-3" />,
          glow: "shadow-lg shadow-purple-500/25",
        };
      case "homepage":
        return {
          text: "SEO Optimized",
          bgColor:
            "bg-gradient-to-r from-teal-400 via-emerald-500 to-green-500",
          textColor: "text-white",
          icon: <TrendingUp className="w-3 h-3" />,
          glow: "shadow-lg shadow-emerald-500/25",
        };
      case "landingpage":
        return {
          text: "WCAG Certified",
          bgColor: "bg-gradient-to-r from-green-400 via-blue-500 to-indigo-600",
          textColor: "text-white",
          icon: <Verified className="w-3 h-3" />,
          glow: "shadow-lg shadow-blue-500/25",
        };
      case "qa":
        return {
          text: "Zero Defects",
          bgColor:
            "bg-gradient-to-r from-green-500 via-teal-500 to-emerald-600",
          textColor: "text-white",
          icon: <Award className="w-3 h-3" />,
          glow: "shadow-lg shadow-green-500/25",
        };
      default:
        return {
          text: "Premium",
          bgColor:
            "bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-600",
          textColor: "text-white",
          icon: <Sparkles className="w-3 h-3" />,
          glow: "shadow-lg shadow-blue-500/25",
        };
    }
  };

  const badge = getBadge();
  const metrics = serviceMetrics[serviceKey as keyof typeof serviceMetrics];

  return (
    <motion.div
      initial={{ opacity: 0, y: 30, scale: 0.9 }}
      animate={inView ? { opacity: 1, y: 0, scale: 1 } : {}}
      transition={{
        duration: 0.6,
        delay: index * 0.1,
        type: "spring",
        stiffness: 100,
        damping: 15,
      }}
      whileHover={{
        y: -8,
        scale: 1.02,
        transition: { duration: 0.3 },
      }}
      className={cn(
        "group flex flex-col items-center text-center cursor-pointer relative h-full",
        className
      )}
      onClick={onClick}
    >
      {/* Enhanced card with glass-morphism effect */}
      <div className="service-card w-full h-full p-6 rounded-2xl bg-white/90 dark:bg-gray-800/90 backdrop-blur-xl border border-white/20 dark:border-gray-700/30 hover:border-blue-300/50 dark:hover:border-blue-500/30 shadow-xl hover:shadow-2xl transition-all duration-500 relative overflow-hidden flex flex-col group">
        {/* Animated background gradient mesh */}
        <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-all duration-700">
          <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-blue-50/30 via-indigo-50/20 to-purple-50/30 dark:from-blue-900/20 dark:via-indigo-900/10 dark:to-purple-900/20"></div>
          <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-gradient-radial from-blue-400/10 to-transparent rounded-full blur-xl"></div>
          <div className="absolute bottom-1/4 right-1/4 w-24 h-24 bg-gradient-radial from-purple-400/10 to-transparent rounded-full blur-xl"></div>
        </div>

        {/* Enhanced premium badge with glow effect */}
        <div className="absolute -top-2 -right-2 z-20">
          <motion.div
            className={`${badge.bgColor} ${badge.textColor} ${badge.glow} text-xs font-bold px-4 py-2 rounded-bl-xl rounded-tr-2xl transform rotate-0 flex items-center gap-1.5 backdrop-blur-sm border border-white/20`}
            whileHover={{ scale: 1.05 }}
            transition={{ duration: 0.2 }}
          >
            {badge.icon}
            <span>{badge.text}</span>
            {serviceKey === "mvp" && (
              <motion.span
                className="text-yellow-200"
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [0.7, 1, 0.7],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut",
                }}
              >
                ★
              </motion.span>
            )}
          </motion.div>
        </div>

        {/* Card content with enhanced spacing */}
        <div className="flex flex-col items-center relative z-10 flex-grow">
          {/* Enhanced icon container with floating effect */}
          <motion.div
            className="mb-6 mt-3 flex items-center justify-center w-20 h-20 md:w-24 md:h-24 rounded-full bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-700/50 dark:via-gray-600/30 dark:to-gray-700/50 shadow-lg border border-white/30 dark:border-gray-600/30 transition-all duration-500 group-hover:shadow-xl group-hover:border-blue-300/50 dark:group-hover:border-blue-400/30"
            whileHover={{
              rotateY: 180,
              transition: { duration: 0.6 },
            }}
          >
            <div className="text-[#3f5781] dark:text-[#8fa3c7] transition-colors duration-300 group-hover:text-[#1e3a70] dark:group-hover:text-[#a3bce9]">
              {renderIcon()}
            </div>
          </motion.div>

          {/* Enhanced title with gradient text */}
          <h3 className="text-base md:text-lg lg:text-xl font-bold bg-gradient-to-r from-gray-800 via-gray-900 to-gray-800 dark:from-gray-100 dark:via-white dark:to-gray-100 bg-clip-text text-transparent group-hover:from-[#3f5781] group-hover:via-[#1e3a70] group-hover:to-[#3f5781] dark:group-hover:from-[#8fa3c7] dark:group-hover:via-[#a3bce9] dark:group-hover:to-[#8fa3c7] transition-all duration-500 mb-3">
            {title}
          </h3>

          {/* Enhanced description with better typography */}
          <p className="text-sm md:text-base text-gray-600 dark:text-gray-300 transition-colors duration-300 mt-1 mb-4 line-clamp-3 leading-relaxed">
            {description}
          </p>

          {/* Trust metrics display */}
          {metrics && (
            <div className="grid grid-cols-2 gap-3 mb-4 w-full">
              <div className="text-center p-2 rounded-lg bg-gradient-to-br from-blue-50/50 to-indigo-50/50 dark:from-gray-700/30 dark:to-gray-600/30 border border-blue-100/50 dark:border-gray-600/30">
                <div className="text-lg md:text-xl font-bold text-[#3f5781] dark:text-[#8fa3c7]">
                  {metrics.completionRate}
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  Success Rate
                </div>
              </div>
              <div className="text-center p-2 rounded-lg bg-gradient-to-br from-emerald-50/50 to-teal-50/50 dark:from-gray-700/30 dark:to-gray-600/30 border border-emerald-100/50 dark:border-gray-600/30">
                <div className="text-lg md:text-xl font-bold text-emerald-600 dark:text-emerald-400">
                  {metrics.avgDeliveryTime}
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  Avg. Timeline
                </div>
              </div>
            </div>
          )}

          {/* Bottom area with enhanced CTA */}
          <div className="mt-auto w-full">
            {/* Enhanced divider with gradient */}
            <div className="w-16 h-[2px] bg-gradient-to-r from-transparent via-blue-300 dark:via-blue-600 to-transparent mx-auto mb-4 opacity-50 group-hover:opacity-100 transition-opacity duration-300"></div>

            {/* Enhanced learn more indicator with micro-animations */}
            <motion.div
              className="flex items-center justify-center text-sm font-semibold text-[#3f5781] dark:text-[#8fa3c7] group-hover:text-[#1e3a70] dark:group-hover:text-[#a3bce9] transition-all duration-300 transform group-hover:translate-y-0 py-2 px-4 rounded-full group-hover:bg-blue-50/50 dark:group-hover:bg-gray-700/30"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.2 }}
            >
              <span>Explore Service</span>
              <motion.div
                animate={{ x: [0, 4, 0] }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  ease: "easeInOut",
                }}
              >
                <ChevronRight className="w-4 h-4 ml-2 transition-all duration-300" />
              </motion.div>
            </motion.div>
          </div>
        </div>

        {/* Subtle corner accent */}
        <div className="absolute bottom-0 left-0 w-20 h-20 bg-gradient-to-tr from-blue-100/30 dark:from-blue-900/20 to-transparent rounded-tl-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
        <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-bl from-purple-100/30 dark:from-purple-900/20 to-transparent rounded-tr-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
      </div>
    </motion.div>
  );
};

// Service Details View Component
interface ServiceDetailsViewProps {
  serviceKey: string;
  service: Service;
  onBack: () => void;
  dictionary: ServiceSectionProps["dictionary"];
}

const ServiceDetailsView = ({
  serviceKey,
  service,
  onBack,
  dictionary,
}: ServiceDetailsViewProps) => {
  const [activeTab, setActiveTab] = useState<
    "overview" | "techstack" | "casestudies"
  >("overview");
  const { ref: detailsRef, inView: detailsInView } = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  // Get the tech stack data for this service
  const techStack = techStackData[serviceKey as keyof typeof techStackData];

  // Get the service icon
  const getServiceIcon = (serviceKey: string) => {
    switch (serviceKey) {
      case "mvp":
        return <Rocket className="w-6 h-6" />;
      case "prototype":
        return <Lightbulb className="w-6 h-6" />;
      case "fullstack":
        return <Smartphone className="w-6 h-6" />;
      case "homepage":
        return <Home className="w-6 h-6" />;
      case "landingpage":
        return <FileCode className="w-6 h-6" />;
      case "qa":
        return <Shield className="w-6 h-6" />;
      case "consulting":
        return <Headphones className="w-6 h-6" />;
      case "architecture":
        return <Layers className="w-6 h-6" />;
      case "aiSolutions":
        return <Brain className="w-6 h-6" />;
      default:
        return <Smartphone className="w-6 h-6" />;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.4 }}
      className="mt-8"
      ref={detailsRef}
    >
      <button
        onClick={onBack}
        className="flex items-center gap-2 text-gray-600 dark:text-gray-300 hover:text-[#3f5781] dark:hover:text-[#8fa3c7] mb-6 transition-colors"
      >
        <ChevronLeft className="h-4 w-4" />
        <span>Back to all services</span>
      </button>

      {/* Service header */}
      <div className="flex flex-col md:flex-row items-center mb-10">
        <div className="w-24 h-24 mb-6 md:mb-0 md:mr-8 rounded-lg border border-[#3f5781]/20 dark:border-[#8fa3c7]/20 flex items-center justify-center bg-white dark:bg-gray-800">
          <div className="text-[#3f5781] dark:text-[#8fa3c7] scale-[3.5]">
            {getServiceIcon(serviceKey)}
          </div>
        </div>
        <div className="text-center md:text-left">
          <h2 className="text-2xl sm:text-3xl font-bold mb-3 text-gray-900 dark:text-white">
            {service.title}
          </h2>
          <p className="text-gray-600 dark:text-gray-400 max-w-2xl">
            {service.description}
          </p>
        </div>
      </div>

      {/* Service details container with simplified design */}
      <div className="bg-white dark:bg-gray-800 border border-gray-100 dark:border-gray-700 rounded-xl p-6 sm:p-8 shadow-sm">
        {/* Tabs navigation */}
        <div className="flex border-b border-gray-200 dark:border-gray-700 mb-6">
          <button
            onClick={() => setActiveTab("overview")}
            className={`py-2 px-4 text-sm font-medium ${
              activeTab === "overview"
                ? "text-[#3f5781] dark:text-[#8fa3c7] border-b-2 border-[#3f5781] dark:border-[#8fa3c7]"
                : "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700/50"
            }`}
          >
            Overview
          </button>
          <button
            onClick={() => setActiveTab("techstack")}
            className={`py-2 px-4 text-sm font-medium ${
              activeTab === "techstack"
                ? "text-[#3f5781] dark:text-[#8fa3c7] border-b-2 border-[#3f5781] dark:border-[#8fa3c7]"
                : "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700/50"
            }`}
          >
            Tech Stack
          </button>
          {service.caseStudies && service.caseStudies.length > 0 && (
            <button
              onClick={() => setActiveTab("casestudies")}
              className={`py-2 px-4 text-sm font-medium ${
                activeTab === "casestudies"
                  ? "text-[#3f5781] dark:text-[#8fa3c7] border-b-2 border-[#3f5781] dark:border-[#8fa3c7]"
                  : "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700/50"
              }`}
            >
              Case Studies
            </button>
          )}
        </div>

        {/* Tab content */}
        <div className="min-h-[300px]">
          {/* Overview tab */}
          {activeTab === "overview" && (
            <div className="space-y-6">
              <p className="text-gray-700 dark:text-gray-300 text-base sm:text-lg leading-relaxed">
                {service.detailedDescription || service.description}
              </p>

              {service.features && service.features.length > 0 && (
                <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                  <h3 className="text-lg font-semibold text-[#3f5781] dark:text-[#8fa3c7] mb-4">
                    Key Features
                  </h3>
                  <ul className="space-y-3">
                    {service.features.map((feature, i) => (
                      <li
                        key={i}
                        className="flex items-start gap-2 text-gray-700 dark:text-gray-300"
                      >
                        <span className="text-[#3f5781] dark:text-[#8fa3c7] mt-0.5 flex-shrink-0">
                          <Check size={16} />
                        </span>
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {service.benefits && service.benefits.length > 0 && (
                <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                  <h3 className="text-lg font-semibold text-[#3f5781] dark:text-[#8fa3c7] mb-4">
                    Benefits
                  </h3>
                  <ul className="space-y-3">
                    {service.benefits.map((benefit, i) => (
                      <li
                        key={i}
                        className="flex items-start gap-2 text-gray-700 dark:text-gray-300"
                      >
                        <span className="text-[#3f5781] dark:text-[#8fa3c7] mt-0.5 flex-shrink-0">
                          <Check size={16} />
                        </span>
                        <span>{benefit}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                <a
                  href="#contact"
                  className="inline-flex items-center gap-2 px-6 py-3 bg-[#3f5781] hover:bg-[#2d3f5f] text-white font-medium rounded-lg transition-all duration-300"
                >
                  Get in Touch
                  <ArrowRight size={18} />
                </a>
              </div>
            </div>
          )}

          {/* Tech Stack tab */}
          {activeTab === "techstack" && techStack && (
            <div className="space-y-6">
              <div className="mb-6">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  {techStack.title}
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  {techStack.description}
                </p>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {techStack.technologies.map((tech, i) => (
                  <motion.div
                    key={tech.name}
                    initial={{ opacity: 0, y: 10 }}
                    animate={detailsInView ? { opacity: 1, y: 0 } : {}}
                    transition={{ duration: 0.3, delay: i * 0.05 }}
                    className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                  >
                    <div className="p-2 rounded-md bg-white dark:bg-gray-800 shadow-sm">
                      <Image
                        src={tech.icon}
                        alt={tech.name}
                        width={24}
                        height={24}
                        className="dark:invert-[0.85] opacity-80"
                      />
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white">
                        {tech.name}
                      </h4>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {tech.description}
                      </p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          )}

          {/* Case Studies tab */}
          {activeTab === "casestudies" &&
            service.caseStudies &&
            service.caseStudies.length > 0 && (
              <div className="space-y-8">
                {service.caseStudies.map((caseStudy, i) => (
                  <motion.div
                    key={i}
                    initial={{ opacity: 0, y: 20 }}
                    animate={detailsInView ? { opacity: 1, y: 0 } : {}}
                    transition={{ duration: 0.4, delay: i * 0.1 }}
                    className="bg-gray-50 dark:bg-gray-700/30 rounded-xl p-6 border border-gray-200 dark:border-gray-700"
                  >
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">
                      {caseStudy.title}
                    </h3>
                    <p className="text-gray-700 dark:text-gray-300 mb-4">
                      {caseStudy.description}
                    </p>

                    <div className="space-y-2">
                      <h4 className="font-medium text-gray-900 dark:text-white">
                        Results:
                      </h4>
                      <ul className="space-y-2">
                        {caseStudy.results.map((result, j) => (
                          <li key={j} className="flex items-start gap-2">
                            <span className="text-green-500 dark:text-green-400 mt-0.5">
                              <Check size={16} />
                            </span>
                            <span className="text-gray-700 dark:text-gray-300">
                              {result}
                            </span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </motion.div>
                ))}
              </div>
            )}
        </div>
      </div>
    </motion.div>
  );
};

interface Service {
  title: string;
  description: string;
  benefits?: string[];
  detailedDescription?: string;
  features?: string[];
  caseStudies?: {
    title: string;
    description: string;
    results: string[];
  }[];
}

interface ServiceSectionProps {
  dictionary: {
    title?: string;
    subtitle?: string;
    description?: string;
    mvp?: Service;
    prototype?: Service;
    fullstack?: Service;
    homepage?: Service;
    landingpage?: Service;
    qa?: Service;
    consulting?: Service;
    architecture?: Service;
    aiSolutions?: Service;
    viewAll?: string;
    comparisonTitle?: string;
    comparisonSubtitle?: string;
    timeComparison?: {
      title?: string;
      traditional?: string;
      withUs?: string;
      savings?: string;
    };
    costComparison?: {
      title?: string;
      traditional?: string;
      withUs?: string;
      savings?: string;
    };
    qualityComparison?: {
      title?: string;
      traditional?: string;
      withUs?: string;
      savings?: string;
    };
  };
}

export const ServiceSection = ({ dictionary }: ServiceSectionProps) => {
  const shouldReduceMotion = useReducedMotion();
  const { ref: servicesRef, inView: servicesInView } = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  // State to track which service is expanded in detail view
  const [expandedService, setExpandedService] = useState<string | null>(null);

  // State to track if user has scrolled the services list
  const [hasScrolled, setHasScrolled] = useState(false);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // Optimize animation performance
  const optimizedAnimationConfig = useMemo(() => ({
    floatingOrb1: shouldReduceMotion ? {} : {
      scale: [1.2, 1, 1.2],
      opacity: [0.2, 0.5, 0.2],
      x: [0, -15, 0],
      y: [0, 15, 0],
    },
    floatingOrb2: shouldReduceMotion ? {} : {
      scale: [1, 1.4, 1],
      opacity: [0.4, 0.7, 0.4],
      x: [0, 25, 0],
      y: [0, -20, 0],
    },
    sparkle1: shouldReduceMotion ? {} : {
      rotate: [0, 360],
      scale: [1, 1.2, 1],
      opacity: [0.3, 0.8, 0.3],
    },
    sparkle2: shouldReduceMotion ? {} : {
      rotate: [360, 0],
      scale: [1.2, 1, 1.2],
      opacity: [0.2, 0.6, 0.2],
    }
  }), [shouldReduceMotion]);

  const getServiceIcon = useCallback((serviceKey: string) => {
    switch (serviceKey) {
      case "mvp":
        return <Rocket className="w-6 h-6" />;
      case "prototype":
        return <Lightbulb className="w-6 h-6" />;
      case "fullstack":
        return <Smartphone className="w-6 h-6" />;
      case "homepage":
        return <Home className="w-6 h-6" />;
      case "landingpage":
        return <FileCode className="w-6 h-6" />;
      case "qa":
        return <Shield className="w-6 h-6" />;
      case "consulting":
        return <Headphones className="w-6 h-6" />;
      case "architecture":
        return <Layers className="w-6 h-6" />;
      case "aiSolutions":
        return <Brain className="w-6 h-6" />;
      default:
        return <Smartphone className="w-6 h-6" />;
    }
  }, []);

  // Define services to display - memoize to prevent re-computation
  const services = useMemo(
    () =>
      [
        { key: "mvp", data: dictionary.mvp },
        { key: "prototype", data: dictionary.prototype },
        { key: "fullstack", data: dictionary.fullstack },
        { key: "homepage", data: dictionary.homepage },
        { key: "landingpage", data: dictionary.landingpage },
        { key: "consulting", data: dictionary.consulting },
      ].filter((service) => service.data),
    [dictionary]
  );

  // Handle service card click
  const handleServiceClick = (serviceKey: string) => {
    setExpandedService(serviceKey);
    // Scroll to top of section
    document.getElementById("services")?.scrollIntoView({ behavior: "smooth" });
  };

  // Handle back button click in detail view
  const handleBackClick = () => {
    setExpandedService(null);
  };

  // Handle scroll events on the services container
  useEffect(() => {
    const scrollContainer = scrollContainerRef.current;

    const handleScroll = () => {
      if (scrollContainer && scrollContainer.scrollLeft > 10 && !hasScrolled) {
        setHasScrolled(true);
      }
    };

    if (scrollContainer) {
      scrollContainer.addEventListener("scroll", handleScroll);
    }

    return () => {
      if (scrollContainer) {
        scrollContainer.removeEventListener("scroll", handleScroll);
      }
    };
  }, [hasScrolled]);

  return (
    <Section
      id="services"
      titleClassName="text-gray-900 dark:text-white text-center"
      subtitleClassName="text-primary dark:text-blue-400 text-center"
      title={dictionary.title || "Our Services"}
      subtitle={
        dictionary.subtitle ||
        "Comprehensive Digital Solutions for Your Business"
      }
      description={dictionary.description}
      className="py-16 sm:py-20 md:py-28 bg-gradient-to-br from-gray-50 via-white to-blue-50/30 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 relative w-full overflow-hidden"
    >
      {/* Enhanced Premium Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50/20 via-transparent to-purple-50/20 dark:from-blue-900/10 dark:via-transparent dark:to-purple-900/10"></div>

      {/* Floating Orb Elements - Conditional animation for performance */}
      {!shouldReduceMotion && (
        <>
          <motion.div
            className="absolute top-20 left-10 w-32 h-32 bg-gradient-radial from-blue-400/20 via-indigo-400/10 to-transparent rounded-full blur-2xl"
            animate={{
              scale: [1, 1.3, 1],
              opacity: [0.3, 0.6, 0.3],
              x: [0, 20, 0],
              y: [0, -10, 0],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          />
          <motion.div
            className="absolute top-40 right-20 w-40 h-40 bg-gradient-radial from-purple-400/15 via-pink-400/8 to-transparent rounded-full blur-2xl"
            animate={optimizedAnimationConfig.floatingOrb1}
            transition={shouldReduceMotion ? {} : {
              duration: 10,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 2,
            }}
          />
          <motion.div
            className="absolute bottom-20 left-1/3 w-28 h-28 bg-gradient-radial from-teal-400/20 via-emerald-400/10 to-transparent rounded-full blur-xl"
            animate={optimizedAnimationConfig.floatingOrb2}
            transition={shouldReduceMotion ? {} : {
              duration: 12,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 4,
            }}
          />
        </>
      )}

      {/* Premium Gradient Lines */}
      <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-transparent via-blue-200/60 dark:via-blue-800/40 to-transparent opacity-70" />
      <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-purple-200/50 dark:via-purple-800/30 to-transparent opacity-50" />

      {/* Floating Sparkle Elements */}
      <motion.div
        className="absolute top-32 left-1/4 text-blue-400/30 dark:text-blue-300/20"
        animate={optimizedAnimationConfig.sparkle1}
        transition={shouldReduceMotion ? {} : {
          duration: 6,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      >
        <Sparkles className="w-6 h-6" />
      </motion.div>
      <motion.div
        className="absolute bottom-40 right-1/3 text-purple-400/25 dark:text-purple-300/15"
        animate={optimizedAnimationConfig.sparkle2}
        transition={shouldReduceMotion ? {} : {
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 3,
        }}
      >
        <Sparkles className="w-8 h-8" />
      </motion.div>

      <div className="container mx-auto px-4 relative z-10">
      
        <AnimatePresence mode="wait">
          {!expandedService ? (
            // Grid view of service cards
            <motion.div
              key="service-grid"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.5 }}
            >
              {/* Mobile: 2-column grid layout with staggered animations */}
              <div className="block sm:hidden mb-8 w-full">
                <ScrollAnimationGroup
                  direction="left"
                  staggerDelay={0.1}
                  distanceOffset={40}
                  className="grid grid-cols-1 gap-6 px-2"
                  childClassName="flex justify-center h-full"
                >
                  {services.map((service, index) => (
                    <ServiceCard
                      key={service.key}
                      title={service.data?.title || ""}
                      description={service.data?.description || ""}
                      icon={getServiceIcon(service.key)}
                      index={index}
                      inView={true}
                      serviceKey={service.key}
                      onClick={() => handleServiceClick(service.key)}
                      className="w-full max-w-sm mx-auto"
                    />
                  ))}
                </ScrollAnimationGroup>
              </div>

              {/* Desktop: Enhanced grid layout - 2 rows x 3 columns with animations */}
              <div
                ref={servicesRef}
                className="hidden sm:grid grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-12 max-w-7xl mx-auto mb-16 pt-8 pb-12 px-4"
                style={{ gridAutoRows: "1fr" }}
              >
                {services.map((service, index) => (
                  <ScrollAnimation
                    key={service.key}
                    direction={index % 2 === 0 ? "left" : "right"}
                    delay={0.1 + index * 0.08}
                    className="h-full"
                  >
                    <ServiceCard
                      title={service.data?.title || ""}
                      description={service.data?.description || ""}
                      icon={getServiceIcon(service.key)}
                      index={index}
                      inView={servicesInView}
                      serviceKey={service.key}
                      onClick={() => handleServiceClick(service.key)}
                      className=""
                    />
                  </ScrollAnimation>
                ))}
              </div>

              {/* Enhanced CTA Button */}
              {dictionary.viewAll && (
                <motion.div
                  className="mt-20 text-center"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.8 }}
                >
                  <motion.a
                    href="#contact"
                    className="inline-flex items-center gap-3 px-10 py-5 bg-gradient-to-r from-[#3f5781] via-[#2d3f5f] to-[#3f5781] hover:from-[#1e3a70] hover:via-[#243650] hover:to-[#1e3a70] text-white font-semibold rounded-2xl transition-all duration-500 shadow-xl hover:shadow-2xl border border-blue-300/20 backdrop-blur-sm relative overflow-hidden group"
                    whileHover={{
                      scale: 1.05,
                      y: -2,
                    }}
                    whileTap={{ scale: 0.98 }}
                    transition={{ duration: 0.2 }}
                  >
                    {/* Animated background glow */}
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-400/0 via-blue-400/20 to-blue-400/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000"></div>
                    <span className="relative z-10">{dictionary.viewAll}</span>
                    <motion.div
                      animate={{ x: [0, 4, 0] }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        ease: "easeInOut",
                      }}
                      className="relative z-10"
                    >
                      <ArrowRight size={20} />
                    </motion.div>
                  </motion.a>
                </motion.div>
              )}
            </motion.div>
          ) : (
            // Enhanced detailed view of selected service
            <motion.div
              key="service-detail"
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              transition={{ duration: 0.5 }}
            >
              {/* Find the selected service data */}
              {(() => {
                const selectedService = services.find(
                  (s) => s.key === expandedService
                );
                if (!selectedService || !selectedService.data) return null;

                return (
                  <ServiceDetailsView
                    serviceKey={selectedService.key}
                    service={selectedService.data}
                    onBack={handleBackClick}
                    dictionary={dictionary}
                  />
                );
              })()}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </Section>
  );
};

const CardContent = ({
  className = "",
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => {
  return (
    <div
      className={cn(
        "flex-1 border border-[#e0e7f1]/60 dark:border-[#8fa3c7]/5 rounded-lg bg-white dark:bg-[#121A29] group-hover:shadow-lg transition-shadow duration-300 overflow-hidden",
        className
      )}
      {...props}
    />
  );
};
