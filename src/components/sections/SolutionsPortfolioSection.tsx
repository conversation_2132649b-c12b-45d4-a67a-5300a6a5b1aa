"use client";

import { Section } from "@/components/ui/Section";
import { motion, AnimatePresence, Variants, useReducedMotion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { type Dictionary } from "@/lib/dictionary";
import { useState, useEffect, useRef, useMemo } from "react";
import { cn } from "@/lib/utils";
import Image from "next/image";
import {
  ChevronLeft,
  ChevronRight,
  X,
  Check,
  Info,
  ArrowRight,
  Smartphone,
  Home,
  Lightbulb,
  Rocket,
  FileCode,
  Headphones,
  Brain,
  Star,
  Award,
  Verified,
  TrendingUp,
  Users,
  Globe,
  Sparkles,
  Zap,
  Shield,
  CheckCircle,
} from "lucide-react";

// --- Interfaces and Types ---
interface PortfolioItem {
  id: number;
  title: string;
  category: AppCategory;
  description: string;
}

interface SolutionsPortfolioSectionProps {
  dictionary: Dictionary["solutionsPortfolio"] & {
    showcaseTitle?: string;
    showcaseSubtitle?: string;
  };
  products: {
    // Assuming products prop structure based on previous components
    title: string;
    link: string;
    thumbnail: string;
  }[];
}

type AppCategory =
  | "all"
  | "ai-assistant"
  | "food-delivery"
  | "hospitality"
  | "medical"
  | "lifestyle"
  | "automotive";

type SolutionCategory = Exclude<AppCategory, "all">;

interface SolutionInfo {
  title: string;
  description: string;
  features: string[];
  problemsSolved: string[];
  color: string; // Keep for potential future styling
  gradient: string; // Keep for potential future styling
  bgColor: string; // Keep for potential future styling
  textColor: string; // Keep for potential future styling
  iconSvg: React.ReactNode;
  sectorImage: string;
  sectorImageWidth: number; // For responsive image sizing
  sectorImageHeight: number; // For responsive image sizing
}

// --- Static Data & Helper Functions ---

// Define static parts + sector image paths & dimensions
const staticSolutionInfo: Record<
  SolutionCategory,
  Omit<
    SolutionInfo,
    "title" | "description" | "features" | "problemsSolved"
  > & {
    sectorImage: string;
    sectorImageWidth: number;
    sectorImageHeight: number;
    trustBadge: string;
    completionRate: string;
    avgProjects: string;
    successMetric: string;
  }
> = {
  // Enhanced with trust indicators and metrics
  "ai-assistant": {
    color: "orange",
    gradient: "from-orange-400 via-amber-500 to-orange-600",
    bgColor:
      "bg-gradient-to-br from-orange-50 to-amber-50 dark:bg-gradient-to-br dark:from-orange-900/30 dark:to-amber-900/20",
    textColor: "text-orange-700 dark:text-orange-300",
    iconSvg: <Brain className="w-6 h-6" />,
    sectorImage: "/images/mockups/app_spotz3.png",
    sectorImageWidth: 1600,
    sectorImageHeight: 900,
    trustBadge: "🤖 AI-Powered",
    completionRate: "98%",
    avgProjects: "8+",
    successMetric: "95% Accuracy",
  },
  "food-delivery": {
    color: "red",
    gradient: "from-red-400 via-rose-500 to-red-600",
    bgColor:
      "bg-gradient-to-br from-red-50 to-rose-50 dark:bg-gradient-to-br dark:from-red-900/30 dark:to-rose-900/20",
    textColor: "text-red-700 dark:text-red-300",
    iconSvg: <Home className="w-6 h-6" />,
    sectorImage: "/images/mockups/app_food.png",
    sectorImageWidth: 1600,
    sectorImageHeight: 900,
    trustBadge: "🚀 Market-Ready",
    completionRate: "100%",
    avgProjects: "5+",
    successMetric: "2M+ Orders",
  },
  hospitality: {
    color: "teal",
    gradient: "from-teal-500 via-emerald-500 to-green-700",
    bgColor:
      "bg-gradient-to-br from-teal-50 to-emerald-50 dark:bg-gradient-to-br dark:from-teal-900/30 dark:to-emerald-900/20",
    textColor: "text-teal-700 dark:text-teal-300",
    iconSvg: <Headphones className="w-6 h-6" />,
    sectorImage: "/images/mockups/app_hostiq_1.png",
    sectorImageWidth: 1600,
    sectorImageHeight: 900,
    trustBadge: "🏨 Enterprise-Grade",
    completionRate: "95%",
    avgProjects: "6+",
    successMetric: "500+ Hotels",
  },
  medical: {
    color: "cyan",
    gradient: "from-blue-500 via-cyan-500 to-cyan-700",
    bgColor:
      "bg-gradient-to-br from-cyan-50 to-blue-50 dark:bg-gradient-to-br dark:from-cyan-900/30 dark:to-blue-900/20",
    textColor: "text-cyan-700 dark:text-cyan-300",
    iconSvg: <FileCode className="w-6 h-6" />,
    sectorImage: "/images/mockups/app_medical.png",
    sectorImageWidth: 1600,
    sectorImageHeight: 900,
    trustBadge: "🏥 HIPAA Ready",
    completionRate: "100%",
    avgProjects: "4+",
    successMetric: "10K+ Patients",
  },
  lifestyle: {
    color: "purple",
    gradient: "from-purple-500 via-violet-500 to-pink-700",
    bgColor:
      "bg-gradient-to-br from-purple-50 to-pink-50 dark:bg-gradient-to-br dark:from-purple-900/30 dark:to-pink-900/20",
    textColor: "text-purple-700 dark:text-purple-300",
    iconSvg: <Smartphone className="w-6 h-6" />,
    sectorImage: "/images/mockups/app_plan.png",
    sectorImageWidth: 1600,
    sectorImageHeight: 900,
    trustBadge: "✨ User-Centric",
    completionRate: "96%",
    avgProjects: "7+",
    successMetric: "4.8★ Rating",
  },
  automotive: {
    color: "slate",
    gradient: "from-slate-500 via-gray-600 to-gray-700",
    bgColor:
      "bg-gradient-to-br from-slate-50 to-gray-50 dark:bg-gradient-to-br dark:from-slate-900/30 dark:to-gray-900/20",
    textColor: "text-slate-700 dark:text-slate-300",
    iconSvg: <Rocket className="w-6 h-6" />,
    sectorImage: "/images/mockups/app_togg.png",
    sectorImageWidth: 1600,
    sectorImageHeight: 900,
    trustBadge: "🚗 IoT-Connected",
    completionRate: "94%",
    avgProjects: "3+",
    successMetric: "50K+ Vehicles",
  },
};

// Helper function to get translated solution data (Keep as is)
const getTranslatedSolutionData = (
  dict: Dictionary["solutionsPortfolio"]["solutions"] | undefined,
  category: SolutionCategory
): Pick<
  SolutionInfo,
  "title" | "description" | "features" | "problemsSolved"
> => {
  const categoryKeyMap: Record<
    SolutionCategory,
    keyof NonNullable<Dictionary["solutionsPortfolio"]["solutions"]>
  > = {
    "ai-assistant": "aiAssistant",
    "food-delivery": "foodDelivery",
    hospitality: "hospitality",
    medical: "medical",
    lifestyle: "lifestyle",
    automotive: "automotive",
  };
  const dictKey = categoryKeyMap[category];
  const solutionDict = dict?.[dictKey];
  const fallbacks: Record<
    SolutionCategory,
    Pick<SolutionInfo, "title" | "description" | "features" | "problemsSolved">
  > = {
    "ai-assistant": {
      title: "AI Assistant Solutions",
      description: "Intelligent virtual assistants...",
      features: ["NLP", "Context"],
      problemsSolved: ["Info Access", "Support"],
    },
    "food-delivery": {
      title: "Food Delivery Platforms",
      description: "End-to-end systems...",
      features: ["Order Mgt", "Tracking"],
      problemsSolved: ["Discovery", "Logistics"],
    },
    hospitality: {
      title: "Hospitality Management",
      description: "Digital guest experiences...",
      features: ["Booking", "Services"],
      problemsSolved: ["Guest Exp.", "Efficiency"],
    },
    medical: {
      title: "Medical Applications",
      description: "Healthcare management platforms...",
      features: ["Patient Records", "Scheduling"],
      problemsSolved: ["Healthcare Access", "Medical Data"],
    },
    lifestyle: {
      title: "Lifestyle Applications",
      description: "Personal lifestyle management...",
      features: ["Planning", "Tracking"],
      problemsSolved: ["Organization", "Wellness"],
    },
    automotive: {
      title: "Automotive Technology",
      description: "Digital interfaces...",
      features: ["Vehicle Mgt", "Nav"],
      problemsSolved: ["Control", "Navigation"],
    },
  };
  return {
    title: solutionDict?.title ?? fallbacks[category].title,
    description: solutionDict?.description ?? fallbacks[category].description,
    features: solutionDict?.features ?? fallbacks[category].features ?? [],
    problemsSolved:
      solutionDict?.problemsSolved ?? fallbacks[category].problemsSolved ?? [],
  };
};

// --- Component Implementation (Rewritten with Detail View) ---

export const SolutionsPortfolioSection = ({
  dictionary,
  products,
}: SolutionsPortfolioSectionProps) => {
  const shouldReduceMotion = useReducedMotion();
  const sectionContainerRef = useRef<HTMLDivElement>(null); // Ref for the main container div
  const [viewRef, inView] = useInView({ triggerOnce: false, threshold: 0.1 }); // Ref for triggering inView

  // State for main layout vs detail view
  const [expandedCategory, setExpandedCategory] =
    useState<SolutionCategory | null>(null);

  // State for the central image slideshow (main view)
  const [centralImageIndex, setCentralImageIndex] = useState(0);
  const [hoveredCategory, setHoveredCategory] =
    useState<SolutionCategory | null>(null);

  // Removed detail view slideshow state as images property was removed

  const [stopImageChanges, setStopImageChanges] = useState(false);

  // Optimize animation performance
  const optimizedAnimations = useMemo(() => ({
    floatingOrb1: shouldReduceMotion ? {} : {
      scale: [1, 1.2, 1],
      opacity: [0.3, 0.6, 0.3],
      x: [0, 10, 0],
      y: [0, -10, 0],
    },
    floatingOrb2: shouldReduceMotion ? {} : {
      scale: [1.1, 1, 1.1],
      opacity: [0.2, 0.5, 0.2],
      x: [0, -8, 0],
      y: [0, 12, 0],
    },
    sparkle: shouldReduceMotion ? {} : {
      rotate: [0, 180, 360],
      scale: [1, 1.1, 1],
      opacity: [0.4, 0.8, 0.4],
    }
  }), [shouldReduceMotion]);

  // Removed fullscreen image viewer state as images property was removed

  // --- Data Preparation ---
  const solutionsData: Record<SolutionCategory, SolutionInfo> = Object.keys(
    staticSolutionInfo
  ).reduce(
    (acc, key) => {
      const category = key as SolutionCategory;
      acc[category] = {
        ...staticSolutionInfo[category],
        ...getTranslatedSolutionData(dictionary?.solutions, category),
      };
      return acc;
    },
    {} as Record<SolutionCategory, SolutionInfo>
  );

  const categories: SolutionCategory[] = [
    "lifestyle",
    "ai-assistant",
    "food-delivery",
    "hospitality",
    "medical",
    "automotive",
  ];

  // Portfolio items data (needed for detail view) - Copied from original
  const portfolioItems: PortfolioItem[] = [
    {
      id: 1,
      title: dictionary?.items?.spotzAiAssistant?.title ?? "Spotz AI Assistant",
      category: "ai-assistant",
      description:
        dictionary?.items?.spotzAiAssistant?.description ??
        "AI-powered assistant...",
    },
    {
      id: 2,
      title: dictionary?.items?.foodDelivery?.title ?? "Food Delivery",
      category: "food-delivery",
      description:
        dictionary?.items?.foodDelivery?.description ??
        "Complete food platform...",
    },
    {
      id: 3,
      title:
        dictionary?.items?.hostIQ?.title ?? "HostIQ - Hospitality SuperApp",
      category: "hospitality",
      description:
        dictionary?.items?.hostIQ?.description ??
        "All-in-one hotel solution...",
    },
    {
      id: 4,
      title: dictionary?.items?.medicalApp?.title ?? "Medical Management",
      category: "medical",
      description:
        dictionary?.items?.medicalApp?.description ??
        "Healthcare management tools...",
    },
    {
      id: 5,
      title: dictionary?.items?.lifestyleApp?.title ?? "Lifestyle Planner",
      category: "lifestyle",
      description:
        dictionary?.items?.lifestyleApp?.description ??
        "Personal lifestyle management...",
    },
    {
      id: 6,
      title: dictionary?.items?.nearby?.title ?? "Nearby",
      category: "ai-assistant",
      description:
        dictionary?.items?.nearby?.description ?? "Location-based discovery...",
    },
    {
      id: 7,
      title: dictionary?.items?.toggCarControl?.title ?? "Togg Car Control",
      category: "automotive",
      description:
        dictionary?.items?.toggCarControl?.description ??
        "Smart car control...",
    },
    {
      id: 8,
      title:
        dictionary?.items?.lifestylePlatform?.title ?? "Lifestyle Platform",
      category: "lifestyle",
      description:
        dictionary?.items?.lifestylePlatform?.description ??
        "Modern lifestyle platform...",
    },
  ];

  // --- Effects ---

  // Auto-cycle central image (main view)
  useEffect(() => {
    if (expandedCategory) return; // Don't cycle if detail view is open
    const interval = setInterval(() => {
      if (!hoveredCategory) {
        setCentralImageIndex(
          (prevIndex) => (prevIndex + 1) % categories.length
        );
      }
    }, 5000); // Increased from 4000ms to 5000ms
    return () => clearInterval(interval);
  }, [hoveredCategory, categories.length, expandedCategory]);

  // Update central image on hover (main view)
  useEffect(() => {
    if (hoveredCategory && !expandedCategory) {
      const hoveredIndex = categories.indexOf(hoveredCategory);
      if (hoveredIndex !== -1) setCentralImageIndex(hoveredIndex);
    }
  }, [hoveredCategory, categories, expandedCategory]);

  // Removed auto-cycle detail view slideshow as images property was removed

  // Removed keyboard navigation for fullscreen view as images property was removed

  // --- Handlers ---
  const handleCategoryClick = (category: SolutionCategory) => {
    setExpandedCategory(category);
  };

  const handleBackClick = () => {
    setExpandedCategory(null);
  };

  // Removed fullscreen view handlers as images property was removed

  // --- Render Logic ---

  const leftCategories = categories.slice(0, 3);
  const rightCategories = categories.slice(3);

  // Card animation variants
  const cardVariants: Variants = {
    hidden: { opacity: 0, x: -30 },
    visible: (i: number) => ({
      opacity: 1,
      x: 0,
      transition: { delay: i * 0.15, duration: 0.5 },
    }),
  };
  const cardVariantsRight: Variants = {
    hidden: { opacity: 0, x: 30 },
    visible: (i: number) => ({
      opacity: 1,
      x: 0,
      transition: { delay: i * 0.15, duration: 0.5 },
    }),
  };

  const getSolutionIcon = (category: SolutionCategory) => {
    return (
      staticSolutionInfo[category]?.iconSvg || <Lightbulb className="w-6 h-6" />
    );
  };

  // Removed detail category images function as images property was removed

  // Helper to get category name from dictionary
  const getCategoryName = (category: SolutionCategory): string => {
    const categoryKeyMap: Record<
      SolutionCategory,
      keyof Dictionary["solutionsPortfolio"]["categories"]
    > = {
      "ai-assistant": "aiAssistant",
      "food-delivery": "foodDelivery",
      hospitality: "hospitality",
      medical: "medical",
      lifestyle: "lifestyle",
      automotive: "automotive",
    };
    const dictKey = categoryKeyMap[category];
    // Fallback to capitalized category name
    return (
      dictionary?.categories?.[dictKey] ??
      category.replace("-", " ").replace(/\b\w/g, (l) => l.toUpperCase())
    );
  };

  return (
    <Section
      id="solutions-portfolio"
      titleClassName="text-gray-900 dark:text-white text-center"
      subtitleClassName="text-primary dark:text-blue-400 text-center"
      title={dictionary?.title ?? "Our Solutions"}
      subtitle={dictionary?.subtitle ?? "Innovative Digital Solutions"}
      className="py-20 md:py-28 bg-gradient-to-br from-gray-50 via-white to-blue-50/30 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 relative w-full overflow-hidden min-h-[80vh]"
      containerClassName="max-w-full px-0 md:px-8 lg:px-12"
    >
      {/* Enhanced Premium Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50/30 via-transparent to-purple-50/30 dark:from-blue-900/20 dark:via-transparent dark:to-purple-900/20"></div>

      {/* Floating Orb Elements */}
      <motion.div
        className="absolute top-16 left-12 w-40 h-40 bg-gradient-radial from-blue-400/20 via-indigo-400/10 to-transparent rounded-full blur-3xl"
        animate={{
          scale: [1, 1.3, 1],
          opacity: [0.3, 0.6, 0.3],
          x: [0, 30, 0],
          y: [0, -20, 0],
        }}
        transition={{
          duration: 10,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />
      <motion.div
        className="absolute top-32 right-16 w-52 h-52 bg-gradient-radial from-purple-400/15 via-pink-400/8 to-transparent rounded-full blur-3xl"
        animate={{
          scale: [1.2, 1, 1.2],
          opacity: [0.2, 0.5, 0.2],
          x: [0, -25, 0],
          y: [0, 25, 0],
        }}
        transition={{
          duration: 12,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 3,
        }}
      />
      <motion.div
        className="absolute bottom-24 left-1/4 w-36 h-36 bg-gradient-radial from-teal-400/20 via-emerald-400/10 to-transparent rounded-full blur-2xl"
        animate={{
          scale: [1, 1.4, 1],
          opacity: [0.4, 0.7, 0.4],
          x: [0, 35, 0],
          y: [0, -30, 0],
        }}
        transition={{
          duration: 14,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 6,
        }}
      />

      {/* Premium Gradient Lines */}
      <div className="absolute top-0 left-0 w-full h-3 bg-gradient-to-r from-transparent via-blue-200/60 dark:via-blue-800/40 to-transparent opacity-70" />

      {/* Floating Sparkle Elements */}
      <motion.div
        className="absolute top-40 left-1/5 text-blue-400/30 dark:text-blue-300/20"
        animate={{
          rotate: [0, 360],
          scale: [1, 1.3, 1],
          opacity: [0.3, 0.8, 0.3],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      >
        <Sparkles className="w-8 h-8" />
      </motion.div>
      <motion.div
        className="absolute bottom-32 right-1/4 text-purple-400/25 dark:text-purple-300/15"
        animate={{
          rotate: [360, 0],
          scale: [1.2, 1, 1.2],
          opacity: [0.2, 0.6, 0.2],
        }}
        transition={{
          duration: 10,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 4,
        }}
      >
        <Sparkles className="w-10 h-10" />
      </motion.div>

      {/* Enhanced Trust Statistics Bar */}
      <motion.div
        className="mb-16 overflow-x-auto scrollbar-hide p-4 sm:p-6 rounded-2xl bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl border border-white/30 dark:border-gray-700/30 shadow-xl max-w-5xl mx-auto"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        <div className="flex items-center justify-center gap-6 md:gap-10 min-w-max">
        <div className="flex items-center gap-2 sm:gap-3 text-center">
          <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center shadow-lg">
            <Smartphone className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
          </div>
          <div>
            <div className="text-xl sm:text-2xl font-bold text-gray-800 dark:text-gray-100">
              15+
            </div>
            <div className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">
              Projects Completed
            </div>
          </div>
        </div>
        <div className="flex items-center gap-2 sm:gap-3 text-center">
          <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-gradient-to-br from-emerald-500 to-teal-600 flex items-center justify-center shadow-lg">
            <CheckCircle className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
          </div>
          <div>
            <div className="text-xl sm:text-2xl font-bold text-gray-800 dark:text-gray-100">
              98%
            </div>
            <div className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">
              Client Satisfaction
            </div>
          </div>
        </div>
        <div className="flex items-center gap-2 sm:gap-3 text-center">
          <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-gradient-to-br from-purple-500 to-pink-600 flex items-center justify-center shadow-lg">
            <Sparkles className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
          </div>
          <div>
            <div className="text-xl sm:text-2xl font-bold text-gray-800 dark:text-gray-100">
              100%
            </div>
            <div className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">
              Customizable
            </div>
          </div>
        </div>
        <div className="flex items-center gap-2 sm:gap-3 text-center">
          <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-gradient-to-br from-orange-500 to-amber-600 flex items-center justify-center shadow-lg">
            <Award className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
          </div>
          <div>
            <div className="text-xl sm:text-2xl font-bold text-gray-800 dark:text-gray-100">
              8+
            </div>
            <div className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">
              Years Experience
            </div>
          </div>
        </div>
        </div>
      </motion.div>

      {/* Apply sectionContainerRef and viewRef here */}
      <div
        className="w-full mx-auto px-0 sm:px-2 relative z-10"
        ref={sectionContainerRef}
      >
        <AnimatePresence mode="wait">
          {!expandedCategory ? (
            // --- Main View (3 Columns) ---
            // Apply viewRef to the element that should trigger the animation
            <motion.div
              key="main-view"
              ref={viewRef} // Attach useInView ref here
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.5 }}
              className="flex flex-col lg:flex-row items-center justify-center gap-8 md:gap-10 lg:gap-8 mt-10 md:mt-12 max-w-[1600px] mx-auto"
            >
              {/* Center Column - Image Slideshow - Enhanced with glow and frame */}
              <motion.div
                className="relative w-full sm:w-3/4 lg:w-4/12 order-3 lg:order-4 overflow-hidden min-h-[520px] lg:mx-auto hidden lg:block"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={inView ? { opacity: 1, scale: 1 } : {}}
                transition={{ delay: 0.1, duration: 0.8 }}
              >
                <div className="absolute inset-0 bg-gradient-to-tr from-blue-500/10 to-purple-500/10 rounded-2xl -z-10"></div>
                <div className="absolute inset-[-2px] bg-gradient-to-tr from-blue-500/20 via-transparent to-blue-500/30 rounded-2xl z-10 opacity-75 pointer-events-none"></div>
                <div className="absolute inset-0 z-10 rounded-2xl overflow-hidden">
                  <AnimatePresence mode="wait">
                    <motion.div
                      key={centralImageIndex}
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      transition={{ duration: 0.8, ease: "easeInOut" }}
                      className="relative w-full h-full rounded-xl overflow-hidden shadow-2xl"
                    >
                      <Image
                        src={
                          solutionsData[categories[centralImageIndex]]
                            .sectorImage
                        }
                        alt={`${
                          solutionsData[categories[centralImageIndex]].title
                        } Sector Image`}
                        layout="fill"
                        objectFit="cover"
                        priority={centralImageIndex === 0}
                        sizes="(max-width: 640px) 90vw, (max-width: 1024px) 75vw, 50vw"
                        className="rounded-xl"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent opacity-60 pointer-events-none"></div>
                      <div className="absolute bottom-0 left-0 right-0 p-4 text-white">
                        <h3 className="text-lg font-bold mb-1 drop-shadow-md">
                          {solutionsData[categories[centralImageIndex]].title}
                        </h3>
                        <p className="text-sm text-white/90 line-clamp-1 drop-shadow-md">
                          {getCategoryName(categories[centralImageIndex])}
                        </p>
                      </div>
                    </motion.div>
                  </AnimatePresence>
                </div>
              </motion.div>
              {/* Mobile view: All categories in a grid - Improved Cards */}
              <div className="w-full lg:hidden grid grid-cols-1 sm:grid-cols-2 gap-6 px-4 order-1 mb-8">
                {categories.map((category, i) => {
                  const solution = solutionsData[category];
                  const staticInfo = staticSolutionInfo[category];
                  return (
                    <motion.div
                      key={category}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: i * 0.1, duration: 0.5 }}
                      className={cn(
                        "group bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm rounded-2xl border border-gray-200/60 dark:border-gray-700/50 shadow-lg hover:shadow-2xl",
                        "cursor-pointer transition-all duration-500 ease-out hover:scale-[1.02] hover:-translate-y-2",
                        "hover:border-blue-400/60 dark:hover:border-blue-500/40 relative overflow-hidden min-h-[280px]"
                      )}
                      onClick={() => handleCategoryClick(category)}
                      whileHover={{ scale: 1.02, y: -8 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      {/* Enhanced gradient accent */}
                      <div
                        className={cn(
                          "absolute top-0 left-0 w-full h-2 opacity-80",
                          staticInfo.gradient &&
                            `bg-gradient-to-r ${staticInfo.gradient}`
                        )}
                      />

                      {/* Trust badge */}
                      <div className="absolute top-3 right-3 z-20">
                        <div className="px-3 py-1 bg-gradient-to-r from-blue-500/90 to-indigo-600/90 text-white text-xs font-bold rounded-full shadow-lg backdrop-blur-sm border border-white/20">
                          {staticInfo.trustBadge}
                        </div>
                      </div>

                      <div className="relative z-10 p-6 flex flex-col h-full">
                        {/* Icon section */}
                        <div className="flex items-center justify-center mb-4">
                          <div
                            className={cn(
                              "w-16 h-16 rounded-2xl flex items-center justify-center transition-all duration-300 shadow-lg group-hover:shadow-xl",
                              staticInfo.bgColor,
                              staticInfo.textColor,
                              "group-hover:scale-110"
                            )}
                          >
                            <div className="scale-150">
                              {getSolutionIcon(category)}
                            </div>
                          </div>
                        </div>

                        {/* Title and description */}
                        <div className="text-center mb-4 flex-grow">
                          <h3 className="text-lg font-bold text-gray-800 dark:text-white mb-3 leading-tight">
                            {solution.title}
                          </h3>
                          <p className="text-sm text-gray-600 dark:text-gray-300 leading-relaxed mb-4">
                            {solution.description}
                          </p>
                        </div>

                        {/* Key features */}
                        <div className="space-y-2 mb-4">
                          {solution.features
                            ?.slice(0, 2)
                            .map((feature, idx) => (
                              <div
                                key={idx}
                                className="flex items-center gap-2 text-xs text-gray-600 dark:text-gray-400"
                              >
                                <div className="w-1.5 h-1.5 rounded-full bg-green-500"></div>
                                <span className="line-clamp-1">{feature}</span>
                              </div>
                            ))}
                        </div>

                        {/* Metrics */}
                        <div className="grid grid-cols-2 gap-3 mb-4 pt-3 border-t border-gray-100 dark:border-gray-700">
                          <div className="text-center">
                            <div className="text-lg font-bold text-blue-600 dark:text-blue-400">
                              {staticInfo.completionRate}
                            </div>
                            <div className="text-xs text-gray-500 dark:text-gray-400">
                              Success Rate
                            </div>
                          </div>
                          <div className="text-center">
                            <div className="text-lg font-bold text-emerald-600 dark:text-emerald-400">
                              {staticInfo.avgProjects}
                            </div>
                            <div className="text-xs text-gray-500 dark:text-gray-400">
                              Projects
                            </div>
                          </div>
                        </div>

                        {/* CTA */}
                        <div className="flex items-center justify-center text-sm font-semibold text-blue-600 dark:text-blue-400 group-hover:text-blue-700 dark:group-hover:text-blue-300 transition-colors">
                          <span>Explore Service</span>
                          <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                        </div>
                      </div>

                      {/* Hover glow effect */}
                      <div className="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none">
                        <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-blue-500/5 via-transparent to-purple-500/5"></div>
                      </div>
                    </motion.div>
                  );
                })}
              </div>
              {/* Left Column - Cards (desktop only) */}
              <motion.div
                className="hidden lg:flex lg:flex-col gap-4 md:gap-5 w-full lg:w-3/12 order-1 lg:order-1 lg:mx-0 lg:px-0"
                initial="hidden"
                animate={inView ? "visible" : "hidden"}
              >
                {leftCategories.map((category, i) => {
                  const solution = solutionsData[category];
                  return (
                    <motion.div
                      key={category}
                      custom={i}
                      variants={cardVariants}
                      className={`list-none flex-shrink-0 w-[85%] sm:w-[60%] lg:w-auto snap-center lg:snap-align-none ${
                        category === categories[centralImageIndex] ? "" : ""
                      }`}
                      onMouseEnter={() => setHoveredCategory(category)}
                      onMouseLeave={() => setHoveredCategory(null)}
                      onClick={(e) => {
                        e.preventDefault();
                        if (category === categories[centralImageIndex]) {
                          setStopImageChanges(true);
                          handleCategoryClick(category); // Open details screen
                        } else {
                          setCentralImageIndex(categories.indexOf(category));
                        }
                      }}
                    >
                      <div
                        className={cn(
                          "group relative h-[180px] lg:h-[190px] overflow-hidden cursor-pointer transition-all duration-500",
                          "bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm shadow-lg hover:shadow-2xl",
                          "transform hover:-translate-y-2 hover:scale-[1.02] rounded-2xl border",
                          hoveredCategory === category
                            ? "border-blue-400/60 dark:border-blue-500/40 shadow-xl ring-2 ring-blue-500/20"
                            : "border-gray-200/60 dark:border-gray-700/50"
                        )}
                      >
                        <div className="relative z-10 p-4">
                          {/* Category Badge */}
                          <div
                            className={cn(
                              "absolute top-2 right-2 px-3 py-1 rounded-full text-xs font-semibold shadow-md z-20 transition-colors duration-300",
                              solutionsData[category].gradient
                                ? `bg-gradient-to-r ${solutionsData[category].gradient}`
                                : "bg-blue-500",
                              "text-white dark:text-white border border-white/30 dark:border-gray-700/40"
                            )}
                          >
                            {getCategoryName(category)}
                          </div>
                          <div className="flex items-center gap-3 mb-2 pt-3">
                            <div
                              className={cn(
                                "p-2 rounded-md text-[#3f5781] dark:text-[#a0b3d7] transition-colors duration-300",
                                hoveredCategory === category
                                  ? "bg-blue-100 dark:bg-blue-900/50"
                                  : "bg-gray-100 dark:bg-gray-700"
                              )}
                            >
                              {getSolutionIcon(category)}
                            </div>
                            <h3 className="text-sm md:text-base font-bold text-gray-800 dark:text-white line-clamp-1">
                              {solution.title}
                            </h3>
                          </div>

                          <p className="text-xs md:text-sm text-gray-600 dark:text-gray-300 mb-2">
                            {solution.description}
                          </p>

                          {/* Add a featured problem to highlight */}
                          {solution.problemsSolved &&
                            solution.problemsSolved.length > 0 && (
                              <div className="mt-2 pt-2 border-t border-gray-100 dark:border-gray-700">
                                <div className="flex items-center text-xs text-[#3f5781] dark:text-[#a0b3d7]">
                                  <Check
                                    size={12}
                                    className="mr-1 flex-shrink-0"
                                  />
                                  <span className="font-medium">
                                    Solves: {solution.problemsSolved[0]}
                                  </span>
                                </div>
                              </div>
                            )}
                        </div>
                      </div>
                    </motion.div>
                  );
                })}
              </motion.div>
              {/* Right column - Cards (desktop only) */}
              <motion.div
                className="hidden lg:flex lg:flex-col gap-4 md:gap-5 w-full lg:w-3/12 order-2 lg:order-3 lg:mx-0 lg:px-0"
                initial="hidden"
                animate={inView ? "visible" : "hidden"}
              >
                {rightCategories.map((category, i) => {
                  const solution = solutionsData[category];
                  return (
                    <motion.div
                      key={category}
                      custom={i}
                      variants={cardVariantsRight}
                      className="list-none flex-shrink-0 w-[85%] sm:w-[60%] lg:w-auto snap-center lg:snap-align-none"
                      onMouseEnter={() => setHoveredCategory(category)}
                      onMouseLeave={() => setHoveredCategory(null)}
                      onClick={() => handleCategoryClick(category)}
                    >
                      <div
                        className={cn(
                          "group relative h-[180px] lg:h-[190px] overflow-hidden cursor-pointer transition-all duration-500",
                          "bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm shadow-lg hover:shadow-2xl",
                          "transform hover:-translate-y-2 hover:scale-[1.02] rounded-2xl border",
                          hoveredCategory === category
                            ? "border-blue-400/60 dark:border-blue-500/40 shadow-xl ring-2 ring-blue-500/20"
                            : "border-gray-200/60 dark:border-gray-700/50"
                        )}
                      >
                        <div className="relative z-10 p-4">
                          {/* Category Badge */}
                          <div
                            className={cn(
                              "absolute top-2 right-2 px-3 py-1 rounded-full text-xs font-semibold shadow-md z-20 transition-colors duration-300",
                              solutionsData[category].gradient
                                ? `bg-gradient-to-r ${solutionsData[category].gradient}`
                                : "bg-blue-500",
                              "text-white dark:text-white border border-white/30 dark:border-gray-700/40"
                            )}
                          >
                            {getCategoryName(category)}
                          </div>
                          <div className="flex items-center gap-3 mb-2 pt-3">
                            <div
                              className={cn(
                                "p-2 rounded-md text-[#3f5781] dark:text-[#a0b3d7] transition-colors duration-300",
                                hoveredCategory === category
                                  ? "bg-blue-100 dark:bg-blue-900/50"
                                  : "bg-gray-100 dark:bg-gray-700"
                              )}
                            >
                              {getSolutionIcon(category)}
                            </div>
                            <h3 className="text-sm md:text-base font-bold text-gray-800 dark:text-white line-clamp-1">
                              {solution.title}
                            </h3>
                          </div>

                          <p className="text-xs md:text-sm text-gray-600 dark:text-gray-300 mb-2">
                            {solution.description}
                          </p>

                          {/* Add a featured problem to highlight */}
                          {solution.problemsSolved &&
                            solution.problemsSolved.length > 0 && (
                              <div className="mt-2 pt-2 border-t border-gray-100 dark:border-gray-700">
                                <div className="flex items-center text-xs text-[#3f5781] dark:text-[#a0b3d7]">
                                  <Check
                                    size={12}
                                    className="mr-1 flex-shrink-0"
                                  />
                                  <span className="font-medium">
                                    Solves: {solution.problemsSolved[0]}
                                  </span>
                                </div>
                              </div>
                            )}
                        </div>
                      </div>
                    </motion.div>
                  );
                })}
              </motion.div>
            </motion.div>
          ) : (
            // --- Detail View ---
            <motion.div
              key="detail-view"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.4 }}
              className="mt-8 max-w-[1440px] mx-auto"
            >
              <button
                onClick={handleBackClick}
                className="flex items-center gap-2 text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-blue-400 mb-6 transition-colors mx-4 sm:mx-0"
              >
                <ChevronLeft className="h-4 w-4" />
                <span>{dictionary?.backButton ?? "Back to all solutions"}</span>
              </button>

              {/* Reusing structure from original detail view */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 bg-white dark:bg-gray-800/90 backdrop-blur-sm rounded-2xl shadow-xl p-6 sm:p-8 overflow-hidden border border-gray-200/50 dark:border-gray-700/50 mx-4 sm:mx-0">
                <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-blue-500/40 to-transparent" />

                {/* Left side: Solution information */}
                <div className="space-y-6">
                  <div className="flex flex-col items-start mb-6">
                    <div className="p-3 mb-4 bg-[#3f5781]/10 dark:bg-[#3f5781]/30 rounded-lg text-[#3f5781] dark:text-[#8fa3c7]">
                      {getSolutionIcon(expandedCategory)}
                    </div>
                    <div>
                      <h2 className="text-2xl sm:text-3xl font-bold mb-3 text-gray-900 dark:text-white text-left">
                        {solutionsData[expandedCategory].title}
                      </h2>
                      <p className="text-gray-700 dark:text-gray-300 text-base sm:text-lg leading-relaxed text-left">
                        {solutionsData[expandedCategory].description}
                      </p>
                    </div>
                  </div>

                  <div className="pt-4 border-t border-gray-200 dark:border-gray-700 text-left">
                    <div className="flex items-center mb-4">
                      <span className="text-[#3f5781] dark:text-[#8fa3c7] bg-[#3f5781]/10 dark:bg-[#3f5781]/20 p-1 rounded-md mr-1.5">
                        {" "}
                        <Info size={18} />{" "}
                      </span>
                      <h3 className="text-lg font-semibold text-[#3f5781] dark:text-[#8fa3c7] text-left">
                        {dictionary?.problemsSolved ?? "Problems Solved"}
                      </h3>
                    </div>
                    <ul className="space-y-3">
                      {solutionsData[expandedCategory].problemsSolved.map(
                        (problem, i) => (
                          <li
                            key={i}
                            className="flex items-start gap-1 text-gray-700 dark:text-gray-300 group/item text-left"
                          >
                            <span className="text-[#3f5781] dark:text-[#8fa3c7] mt-0.5 flex-shrink-0 transform transition-transform duration-300 group-hover/item:translate-x-1 mr-0.5">
                              {" "}
                              <ArrowRight size={14} />{" "}
                            </span>
                            <span>{problem}</span>
                          </li>
                        )
                      )}
                    </ul>
                  </div>

                  {/* Related Projects */}
                  <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                      {dictionary?.relatedProjects ?? "Related Projects"}
                    </h3>
                    <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
                      {portfolioItems
                        .filter((item) => item.category === expandedCategory)
                        .map((item) => (
                          <div
                            key={item.id}
                            className="relative bg-gray-100 dark:bg-gray-700/50 rounded-lg overflow-hidden hover:shadow-md transition-all duration-300 cursor-pointer group border border-gray-200/30 dark:border-gray-600/30"
                          >
                            <div className="relative h-24 mb-2 bg-gradient-to-br from-primary/10 to-primary/5 flex items-center justify-center">
                              <div className="text-primary/40 text-2xl">
                                📱
                              </div>
                              <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity"></div>
                            </div>
                            <div className="p-2">
                              {" "}
                              <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                                {" "}
                                {item.title}{" "}
                              </h4>{" "}
                            </div>
                          </div>
                        ))}
                      {portfolioItems.filter(
                        (item) => item.category === expandedCategory
                      ).length === 0 && (
                        <p className="col-span-full text-sm text-gray-500 dark:text-gray-400">
                          {dictionary?.noRelatedProjects ??
                            "No specific projects shown for this category yet."}
                        </p>
                      )}
                    </div>
                  </div>
                </div>

                {/* Right side: Category Information */}
                <div className="relative bg-gray-100 dark:bg-gray-700/50 rounded-xl overflow-hidden h-[400px] md:h-[500px] border border-gray-200/30 dark:border-gray-600/30 shadow-lg">
                  <div className="flex items-center justify-center h-full text-gray-500 dark:text-gray-400">
                    <div className="text-center p-8">
                      <div className="text-6xl mb-4">📱</div>
                      <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                        {solutionsData[expandedCategory]?.title}
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
                        {solutionsData[expandedCategory]?.description}
                      </p>
                      <div className="space-y-2">
                        {solutionsData[expandedCategory]?.features?.slice(0, 3).map((feature, index) => (
                          <div key={index} className="text-xs text-gray-500 dark:text-gray-400">
                            • {feature}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
      {/* Removed fullscreen image viewer modal as images property was removed */}
    </Section>
  );
};
