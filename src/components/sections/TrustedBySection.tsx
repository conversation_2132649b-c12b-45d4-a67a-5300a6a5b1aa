"use client";

import React from "react";
import Image from "next/image";
import { motion } from "framer-motion";
import { Star } from "lucide-react";

interface TrustedBySectionProps {
  dictionary?: {
    title: string;
  };
}

const companies = [
  {
    name: "Lufthansa",
    logo: "/images/companies/lufthansa.png",
    alt: "Lufthansa Logo"
  },
  {
    name: "TOGG",
    logo: "/images/companies/togg.png",
    alt: "TOGG Logo"
  },
  {
    name: "Union Investment",
    logo: "/images/companies/union-investment.png",
    alt: "Union Investment Logo"
  },
  {
    name: "<PERSON>esso",
    logo: "/images/companies/adesso.png",
    alt: "Adesso Logo"
  },
  {
    name: "Heg<PERSON>",
    logo: "/images/companies/hegla.png",
    alt: "Hegla Logo"
  },
  {
    name: "Lumeus",
    logo: "/images/companies/lumeus.png",
    alt: "Lumeus Logo"
  },
  {
    name: "Innovate",
    logo: "/images/companies/innovate.png",
    alt: "Innovate Logo"
  }
];

export const TrustedBySection = ({ dictionary }: TrustedBySectionProps) => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  // Duplicate companies array for infinite scroll
  const duplicatedCompanies = [...companies, ...companies];

  return (
    <section className="py-12 sm:py-16 lg:py-20 bg-slate-50 dark:bg-slate-900 w-full">
      <div className="w-full">
        <motion.div
          className="text-center mb-8 sm:mb-12 px-4 sm:px-6 lg:px-8"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <h2 className="text-lg sm:text-xl font-semibold text-slate-600 dark:text-slate-300">
            {dictionary?.title || "Trusted by industry leaders"}
          </h2>
        </motion.div>

        {/* Mobile: Horizontal Infinite Scroll */}
        <div className="block lg:hidden overflow-hidden">
          <motion.div
            className="flex gap-6 w-max"
            animate={{
              x: ["-50%", "0%"],
            }}
            transition={{
              duration: 20,
              repeat: Infinity,
              ease: "linear",
            }}
          >
            {duplicatedCompanies.map((company, index) => (
              <div
                key={`${company.name}-${index}`}
                className="group relative flex flex-col items-center justify-center space-y-3 min-w-[120px]"
              >
                <div className="relative w-20 h-20 flex items-center justify-center bg-white dark:bg-slate-800 rounded-2xl shadow-lg p-3">
                  <Image
                    src={company.logo}
                    alt={company.alt}
                    width={120}
                    height={80}
                    className="w-auto h-full max-w-full object-contain rounded-lg transition-all duration-300 ease-in-out group-hover:scale-110"
                    priority={index < 4}
                  />
                </div>
                <div className="flex space-x-1">
                  {[...Array(5)].map((_, starIndex) => (
                    <Star
                      key={starIndex}
                      className="w-3 h-3 fill-yellow-400 text-yellow-400"
                    />
                  ))}
                </div>
              </div>
            ))}
          </motion.div>
        </div>

        {/* Desktop: Grid Layout */}
        <motion.div
          className="hidden lg:grid grid-cols-7 gap-8 items-center justify-items-center w-full px-6"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {companies.map((company, index) => (
            <motion.div
              key={company.name}
              className="group relative w-full max-w-[140px] flex flex-col items-center justify-center space-y-3"
              variants={itemVariants}
            >
              <div className="relative w-24 h-24 flex items-center justify-center bg-white dark:bg-slate-800 rounded-2xl shadow-lg p-3">
                <Image
                  src={company.logo}
                  alt={company.alt}
                  width={120}
                  height={80}
                  className="w-auto h-full max-w-full object-contain rounded-lg transition-all duration-300 ease-in-out group-hover:scale-110"
                  priority={index < 4}
                />
              </div>
              <div className="flex space-x-1">
                {[...Array(5)].map((_, starIndex) => (
                  <Star
                    key={starIndex}
                    className="w-4 h-4 fill-yellow-400 text-yellow-400"
                  />
                ))}
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
};

export default TrustedBySection;