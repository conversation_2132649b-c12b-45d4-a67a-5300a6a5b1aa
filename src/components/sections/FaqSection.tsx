"use client";

import { useState, useMemo, useCallback } from "react";
import { motion, AnimatePresence, useReducedMotion } from "framer-motion";
import { Section } from "@/components/ui/Section";
import { Button } from "@/components/ui/Button";
import { useI18n } from "@/providers/I18nProvider";
import { type Dictionary } from "@/lib/dictionary";
import {
  ChevronDown,
  ChevronUp,
  HelpCircle,
  MessageCircle,
  Clock,
  Shield,
  CheckCircle,
  Star,
  Award,
  Users,
  Sparkles,
  TrendingUp,
  Zap,
  Search,
  Plus,
  Minus,
} from "lucide-react";

// Define a custom interface for FAQ items
interface FaqItem {
  id: string;
  question: string;
  answer: string;
  category?: string;
  priority?: "high" | "medium" | "low";
}

interface FaqSectionProps {
  dictionary: {
    title?: string;
    subtitle?: string;
    description?: string;
    items?: FaqItem[];
    showMore?: string;
    showLess?: string;
    searchPlaceholder?: string;
    noResultsFound?: string;
  };
}

export const FaqSection = ({ dictionary }: FaqSectionProps) => {
  const [expandedFaqs, setExpandedFaqs] = useState<boolean>(false);
  const [expandedFaqIds, setExpandedFaqIds] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const { dir } = useI18n();
  const isRtl = dir === "rtl";

  // Performance optimization: useReducedMotion hook
  const shouldReduceMotion = useReducedMotion();

  // Memoized toggle individual FAQ function
  const toggleFaq = useCallback((id: string) => {
    setExpandedFaqIds((prev) =>
      prev.includes(id) ? prev.filter((faqId) => faqId !== id) : [...prev, id]
    );
  }, []);

  // Memoized toggle showing all FAQs function
  const toggleExpandAll = useCallback(() => {
    setExpandedFaqs(!expandedFaqs);
    // If collapsing, clear all expanded FAQs
    if (expandedFaqs) {
      setExpandedFaqIds([]);
    }
  }, [expandedFaqs]);

  // Memoized filter FAQs based on search term
  const filteredFaqs = useMemo(
    () =>
      (dictionary?.items || []).filter(
        (faq) =>
          faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
          faq.answer.toLowerCase().includes(searchTerm.toLowerCase())
      ),
    [dictionary?.items, searchTerm]
  );

  // Memoized get visible FAQs based on expanded state and search
  const visibleFaqs = useMemo(
    () => (expandedFaqs ? filteredFaqs : filteredFaqs.slice(0, 5)), // Show 5 FAQs instead of 3
    [expandedFaqs, filteredFaqs]
  );

  // Memoized enhanced FAQ stats
  const faqStats = useMemo(
    () => [
      {
        icon: <MessageCircle className="w-5 h-5" />,
        value: `${dictionary?.items?.length || 12}+`,
        label: "Questions Covered",
        color: "from-blue-500 to-indigo-600",
      },
      {
        icon: <Clock className="w-5 h-5" />,
        value: "< 2min",
        label: "Average Read Time",
        color: "from-emerald-500 to-teal-600",
      },
      {
        icon: <Users className="w-5 h-5" />,
        value: "98%",
        label: "Questions Answered",
        color: "from-purple-500 to-pink-600",
      },
      {
        icon: <TrendingUp className="w-5 h-5" />,
        value: "24h",
        label: "Support Response",
        color: "from-orange-500 to-amber-600",
      },
    ],
    [dictionary?.items?.length]
  );

  // Memoized animation configuration
  const animationConfig = useMemo(
    () => ({
      floatingOrb1: shouldReduceMotion
        ? {}
        : {
            scale: [1, 1.3, 1],
            opacity: [0.3, 0.6, 0.3],
            x: [0, 30, 0],
            y: [0, -20, 0],
          },
      floatingOrb2: shouldReduceMotion
        ? {}
        : {
            scale: [1.2, 1, 1.2],
            opacity: [0.2, 0.5, 0.2],
            x: [0, -25, 0],
            y: [0, 25, 0],
          },
      sparkle: shouldReduceMotion
        ? {}
        : {
            rotate: [0, 360],
            scale: [1, 1.3, 1],
            opacity: [0.3, 0.8, 0.3],
          },
    }),
    [shouldReduceMotion]
  );

  return (
    <Section
      id="faq"
      title={dictionary?.title || "Frequently Asked Questions"}
      subtitle={dictionary?.subtitle || "Everything you need to know"}
      titleClassName="text-gray-900 dark:text-white text-center"
      subtitleClassName="text-primary dark:text-blue-400 text-center mb-12"
      className={`py-20 md:py-28 bg-gradient-to-br from-gray-50 via-white to-blue-50/30 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 relative w-full overflow-hidden ${
        isRtl ? "rtl-section" : ""
      }`}
    >
      {/* Enhanced Premium Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50/30 via-transparent to-purple-50/30 dark:from-blue-900/20 dark:via-transparent dark:to-purple-900/20"></div>

      {/* Floating Orb Elements - Conditional animations */}
      {!shouldReduceMotion && (
        <>
          <motion.div
            className="absolute top-20 left-12 w-40 h-40 bg-gradient-radial from-blue-400/20 via-indigo-400/10 to-transparent rounded-full blur-3xl"
            animate={animationConfig.floatingOrb1}
            transition={{
              duration: 10,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          />
          <motion.div
            className="absolute top-32 right-16 w-52 h-52 bg-gradient-radial from-purple-400/15 via-pink-400/8 to-transparent rounded-full blur-3xl"
            animate={animationConfig.floatingOrb2}
            transition={{
              duration: 12,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 3,
            }}
          />
        </>
      )}

      {/* Premium Gradient Lines */}
      <div className="absolute top-0 left-0 w-full h-3 bg-gradient-to-r from-transparent via-blue-200/60 dark:via-blue-800/40 to-transparent opacity-70" />

      {/* Floating Sparkle Elements - Conditional animations */}
      {!shouldReduceMotion && (
        <motion.div
          className="absolute top-40 left-1/5 text-blue-400/30 dark:text-blue-300/20"
          animate={animationConfig.sparkle}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        >
          <Sparkles className="w-8 h-8" />
        </motion.div>
      )}

      {/* FAQ Statistics Bar */}
      <motion.div
        className="mb-16 flex flex-wrap justify-center items-center gap-6 md:gap-10 p-6 rounded-2xl bg-white/70 dark:bg-gray-800/70 backdrop-blur-xl border border-white/30 dark:border-gray-700/30 shadow-lg max-w-5xl mx-auto"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        {faqStats.map((stat, index) => (
          <div key={index} className="flex items-center gap-3 text-center">
            <div
              className={`w-10 h-10 rounded-full bg-gradient-to-br ${stat.color} flex items-center justify-center text-white`}
            >
              {stat.icon}
            </div>
            <div>
              <div className="text-xl font-bold text-gray-800 dark:text-gray-100">
                {stat.value}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                {stat.label}
              </div>
            </div>
          </div>
        ))}
      </motion.div>

      <div className="container mx-auto px-4 relative z-10">
        {/* FAQ introduction */}
        <motion.div
          className="max-w-4xl mx-auto mb-12 text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
        >
          <p className="text-lg text-gray-700 dark:text-gray-300 mb-8">
            {dictionary?.description ||
              "Find answers to the most common questions about our services, processes, and support."}
          </p>

          {/* Enhanced Search Bar */}
          <div className="relative max-w-2xl mx-auto mb-8">
            <div className="relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 w-5 h-5" />
              <input
                type="text"
                placeholder={
                  dictionary?.searchPlaceholder || "Search questions..."
                }
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-12 pr-4 py-4 rounded-2xl border-2 border-gray-200 dark:border-gray-600 bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-4 focus:ring-blue-500/20 dark:focus:ring-blue-400/20 transition-all duration-300 shadow-lg"
              />
              {searchTerm && (
                <button
                  onClick={() => setSearchTerm("")}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                >
                  <svg
                    className="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              )}
            </div>
          </div>

          {/* Search Results Info */}
          {searchTerm && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="mb-6 text-sm text-gray-600 dark:text-gray-400"
            >
              {filteredFaqs.length > 0
                ? `Found ${filteredFaqs.length} question${
                    filteredFaqs.length !== 1 ? "s" : ""
                  } matching "${searchTerm}"`
                : dictionary?.noResultsFound ||
                  `No questions found matching "${searchTerm}"`}
            </motion.div>
          )}
        </motion.div>

        {/* FAQ Accordion */}
        <div className="max-w-4xl mx-auto space-y-4">
          <AnimatePresence>
            {visibleFaqs.length > 0 ? (
              visibleFaqs.map((faq: FaqItem, index: number) => (
                <FaqItemComponent
                  key={faq.id}
                  faq={faq}
                  index={index}
                  isOpen={expandedFaqIds.includes(faq.id)}
                  toggleFaq={() => toggleFaq(faq.id)}
                  searchTerm={searchTerm}
                />
              ))
            ) : searchTerm ? (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-center py-12"
              >
                <HelpCircle className="w-16 h-16 text-gray-400 dark:text-gray-500 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-600 dark:text-gray-400 mb-2">
                  No Results Found
                </h3>
                <p className="text-gray-500 dark:text-gray-500">
                  Try searching with different keywords or{" "}
                  <button
                    onClick={() => setSearchTerm("")}
                    className="text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300 underline"
                  >
                    view all questions
                  </button>
                </p>
              </motion.div>
            ) : null}
          </AnimatePresence>
        </div>

        {/* Show More/Less Button */}
        {filteredFaqs.length > 5 && !searchTerm && (
          <motion.div
            className="text-center mt-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
          >
            <motion.button
              onClick={toggleExpandAll}
              className="px-8 py-4 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white font-semibold rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 inline-flex items-center justify-center gap-3 transform hover:-translate-y-1 relative overflow-hidden group"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              {/* Animated background glow */}
              <div className="absolute inset-0 bg-gradient-to-r from-blue-400/0 via-blue-400/20 to-blue-400/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000"></div>

              <span className="relative z-10">
                {expandedFaqs
                  ? dictionary?.showLess || "Show Less Questions"
                  : dictionary?.showMore ||
                    `Show All ${filteredFaqs.length} Questions`}
              </span>
              <motion.div
                animate={{ rotate: expandedFaqs ? 180 : 0 }}
                transition={{ duration: 0.3 }}
                className="relative z-10"
              >
                <ChevronDown className="w-5 h-5" />
              </motion.div>
            </motion.button>
          </motion.div>
        )}

        {/* Additional Help Section */}
        <motion.div
          className="mt-16 max-w-4xl mx-auto"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
        >
          <div className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-2xl p-8 border border-blue-200/50 dark:border-blue-800/30 text-center">
            <div className="flex items-center justify-center gap-3 mb-4">
              <div className="w-12 h-12 rounded-full bg-blue-500 flex items-center justify-center">
                <MessageCircle className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-gray-800 dark:text-gray-200">
                Still Have Questions?
              </h3>
            </div>
            <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-2xl mx-auto">
              Can't find what you're looking for? Our support team is here to
              help you with any questions or concerns.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.a
                href="#contact"
                className="inline-flex items-center gap-2 px-6 py-3 bg-blue-500 hover:bg-blue-600 text-white font-semibold rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <MessageCircle className="w-5 h-5" />
                Contact Support
              </motion.a>
              <motion.a
                href="mailto:<EMAIL>"
                className="inline-flex items-center gap-2 px-6 py-3 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 font-semibold rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Zap className="w-5 h-5" />
                Quick Email
              </motion.a>
            </div>
          </div>
        </motion.div>
      </div>
    </Section>
  );
};

// Enhanced FAQ Item Component with modern styling
const FaqItemComponent = ({
  faq,
  index,
  isOpen,
  toggleFaq,
  searchTerm,
}: {
  faq: FaqItem;
  index: number;
  isOpen: boolean;
  toggleFaq: () => void;
  searchTerm?: string;
}) => {
  // Function to highlight search terms
  const highlightText = (text: string, term: string) => {
    if (!term) return text;
    const regex = new RegExp(
      `(${term.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")})`,
      "gi"
    );
    return text.replace(
      regex,
      '<mark class="bg-yellow-200 dark:bg-yellow-800 px-1 rounded">$1</mark>'
    );
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.5, delay: index * 0.05 }}
      className="group"
    >
      <motion.button
        onClick={toggleFaq}
        className="w-full flex justify-between items-center p-6 bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 text-left focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 border border-white/30 dark:border-gray-700/30 hover:border-blue-300/50 dark:hover:border-blue-500/30 group-hover:transform group-hover:-translate-y-1"
        aria-expanded={isOpen}
        whileHover={{ scale: 1.01 }}
        whileTap={{ scale: 0.99 }}
      >
        <div className="flex items-start gap-4 flex-1">
          <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center flex-shrink-0 mt-1 shadow-lg">
            <HelpCircle className="w-5 h-5 text-white" />
          </div>
          <div className="flex-1">
            <h4
              className="text-lg font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors text-left"
              dangerouslySetInnerHTML={{
                __html: highlightText(faq.question, searchTerm || ""),
              }}
            />
            {faq.category && (
              <span className="inline-block mt-2 px-3 py-1 text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-full">
                {faq.category}
              </span>
            )}
          </div>
        </div>
        <motion.div
          animate={{ rotate: isOpen ? 180 : 0 }}
          transition={{ duration: 0.3 }}
          className="flex-shrink-0 text-gray-500 dark:text-gray-400 group-hover:text-blue-500 dark:group-hover:text-blue-400 transition-colors ml-4"
        >
          <ChevronDown className="w-6 h-6" />
        </motion.div>
      </motion.button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.4, ease: "easeInOut" }}
            className="overflow-hidden"
          >
            <div className="mt-2 p-6 bg-gradient-to-br from-gray-50 to-blue-50/30 dark:from-gray-700/50 dark:to-gray-800/50 rounded-2xl border border-gray-100/50 dark:border-gray-600/30 backdrop-blur-sm">
              <div className="flex items-start gap-4">
                <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-emerald-500 to-teal-600 flex items-center justify-center flex-shrink-0 mt-1">
                  <CheckCircle className="w-4 h-4 text-white" />
                </div>
                <div className="flex-1">
                  <p
                    className="text-gray-700 dark:text-gray-300 leading-relaxed"
                    dangerouslySetInnerHTML={{
                      __html: highlightText(faq.answer, searchTerm || ""),
                    }}
                  />
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};
