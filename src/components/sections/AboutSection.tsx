'use client'

import { Section } from '@/components/ui/Section';
import { motion, useReducedMotion } from "framer-motion";
import { useInView } from 'react-intersection-observer';
import { type Dictionary } from '@/lib/dictionary';
import { useState, useRef, useEffect, useMemo, useCallback, memo } from "react";
import { cn } from "@/lib/utils";
import Image from "next/image";
import {
  CheckCircle,
  Sparkles,
  Star,
  Lightbulb,
  Target,
  User,
  ChevronLeft,
  ChevronRight,
  Award,
  Shield,
  Clock,
  TrendingUp,
  Calendar,
  FolderOpen,
  Users,
  Heart,
} from "lucide-react";
import {
  ScrollAnimation,
  ScrollAnimationGroup,
} from "@/components/ui/ScrollAnimation";
import Marquee from "react-fast-marquee";

interface AboutSectionProps {
  dictionary: Dictionary["about"];
  clientsDictionary?: Dictionary["clients"];
}

// Memoized client card component to prevent unnecessary re-renders
const ClientCard = memo(
  ({
    client,
    isMobile,
    onClientClick,
  }: {
    client: any;
    isMobile: boolean;
    onClientClick: (link: string) => void;
  }) => {
    const handleClick = useCallback(() => {
      onClientClick(client.link);
    }, [client.link, onClientClick]);

    if (isMobile) {
      return (
        <div
          className="mx-6 sm:mx-10 group cursor-pointer"
          onClick={handleClick}
          style={
            {
              "--client-color": client.color || "#3f5781",
            } as React.CSSProperties
          }
        >
          <div className="flex flex-col items-center">
            <div className="relative w-20 h-20 sm:w-24 sm:h-24 rounded-xl bg-white dark:bg-gray-800 shadow-md flex items-center justify-center overflow-hidden p-2 transition-all duration-300 hover:shadow-lg border border-gray-100 dark:border-gray-700/50">
              {client.highlight && (
                <span className="absolute top-0 right-0 w-3 h-3 bg-green-500 rounded-full"></span>
              )}
              <Image
                src={client.imageUrl}
                alt={client.name}
                fill
                className="object-contain p-1 rounded-2xl"
                sizes="(max-width: 640px) 80px, 96px"
                loading="lazy"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src =
                    "https://via.placeholder.com/120?text=" +
                    encodeURIComponent(client.name);
                }}
              />
            </div>
            <div className="flex flex-col items-center">
              <span className="mt-2 text-xs font-semibold text-gray-800 dark:text-gray-200 transition-colors">
                {client.name}
              </span>
              <span className="text-[10px] text-gray-600 dark:text-gray-400 mt-0.5">
                {client.industry}
              </span>
            </div>
          </div>
        </div>
      );
    }

    return (
      <div
        className="relative flex flex-col items-center cursor-pointer group max-w-[280px] w-full"
        onClick={handleClick}
        style={
          {
            "--client-color": client.color || "#3f5781",
          } as React.CSSProperties
        }
      >
        <div className="relative w-full aspect-video rounded-xl bg-white dark:bg-gray-800 shadow-lg flex items-center justify-center overflow-hidden p-4 transition-all duration-300 hover:shadow-xl border border-gray-100 dark:border-gray-700/50">
          {client.highlight && (
            <span className="absolute top-2 right-2 w-3 h-3 bg-green-500 rounded-full"></span>
          )}
          <div className="relative w-full h-full overflow-hidden rounded-lg">
            <Image
              src={client.imageUrl}
              alt={client.name}
              fill
              className="object-contain p-2 transition-transform duration-300 hover:scale-105 rounded-lg"
              sizes="(max-width: 768px) 240px, 280px"
              loading="lazy"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src =
                  "https://via.placeholder.com/200?text=" +
                  encodeURIComponent(client.name);
              }}
            />
          </div>
        </div>
        <div className="mt-3 flex flex-col items-center">
          <h3 className="text-sm font-semibold text-gray-800 dark:text-gray-200 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
            {client.name}
          </h3>
          <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
            {client.industry} Industry
          </p>
        </div>
      </div>
    );
  }
);

ClientCard.displayName = "ClientCard";

export const AboutSection = ({
  dictionary,
  clientsDictionary,
}: AboutSectionProps) => {
  const [activeTab, setActiveTab] = useState<"vision" | "mission" | "founder">(
    "vision"
  );
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const shouldReduceMotion = useReducedMotion();
  const [selectedClient, setSelectedClient] = useState<any>(null);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const [isMobile, setIsMobile] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Optimized animation configurations
  const optimizedAnimations = useMemo(() => ({
    floatingOrb1: shouldReduceMotion ? {} : {
      scale: [1, 1.1, 1],
      opacity: [0.2, 0.4, 0.2],
      x: [0, 5, 0],
      y: [0, -5, 0],
    },
    floatingOrb2: shouldReduceMotion ? {} : {
      scale: [1.05, 1, 1.05],
      opacity: [0.15, 0.35, 0.15],
      x: [0, -3, 0],
      y: [0, 8, 0],
    },
    sparkle: shouldReduceMotion ? {} : {
      rotate: [0, 180, 360],
      scale: [1, 1.05, 1],
      opacity: [0.3, 0.6, 0.3],
    }
  }), [shouldReduceMotion]);

  const clientsContainerRef = useRef<HTMLDivElement>(null);
  const { ref: clientsRef, inView: clientsInView } = useInView({
    threshold: 0.2,
  });

  const setClientRefs = useCallback(
    (element: HTMLDivElement | null) => {
      clientsContainerRef.current = element;
      clientsRef(element);
    },
    [clientsRef]
  );

  useEffect(() => {
    const checkMobile = () => setIsMobile(window.innerWidth < 768);
    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  // Optimized client click handler
  const handleClientClick = useCallback((link: string) => {
    window.open(link, "_blank");
  }, []);

  // Memoized stats array
  const stats = useMemo(
    () => [
      {
        value: dictionary.metrics?.yearsExperience || "8+",
        label: dictionary.experience,
      },
      {
        value: dictionary.metrics?.projectsCompleted || "15+",
        label: dictionary.projectsCompleted,
      },
      {
        value: dictionary.metrics?.happyClients || "12+",
        label: dictionary.clients,
      },
      {
        value: "100%",
        label: dictionary.dedication || "Dedication",
      },
    ],
    [
      dictionary.metrics?.yearsExperience,
      dictionary.metrics?.projectsCompleted,
      dictionary.metrics?.happyClients,
      dictionary.experience,
      dictionary.projectsCompleted,
      dictionary.clients,
      dictionary.dedication,
    ]
  );

  // Memoized vision items
  const visionItems = useMemo(
    () => [
      {
        key: "transformBusiness" as keyof Dictionary["about"],
        fallback: "Transforming businesses through technology",
      },
      {
        key: "createSolutions" as keyof Dictionary["about"],
        fallback: "Creating future-proof digital solutions",
      },
      {
        key: "stayingAhead" as keyof Dictionary["about"],
        fallback: "Staying at the forefront of technology",
      },
    ],
    []
  );

  // Memoized mission items
  const missionItems = useMemo(
    () => [
      {
        key: "exceptionalUX" as keyof Dictionary["about"],
        fallback: "Delivering exceptional user experiences",
      },
      {
        key: "highPerformance" as keyof Dictionary["about"],
        fallback: "Building high-performance applications",
      },
      {
        key: "solvingChallenges" as keyof Dictionary["about"],
        fallback: "Solving real business challenges with technology",
      },
    ],
    []
  );

  // Optimized clients data - removed complex styling properties
  const clients = useMemo(
    () => [
      {
        id: 1,
        name: "Lufthansa Industry Solutions",
        imageUrl: "/images/companies/lufthansa.png",
        link: "https://www.lufthansa-industry-solutions.com/",
        industry: "Aviation",
        highlight: true,
        color: "#0C479D",
      },
      {
        id: 2,
        name: "Togg",
        imageUrl: "/images/companies/togg.png",
        link: "https://apps.apple.com/tr/app/trumore/id1610978415",
        industry: "Electric Vehicles",
        highlight: true,
        color: "#1E90FF",
      },
      {
        id: 3,
        name: "Union Investment",
        imageUrl: "/images/companies/union-investment.png",
        link: "https://run-this-place.com/en/",
        industry: "Finance",
        highlight: false,
        color: "#006F99",
      },
      {
        id: 4,
        name: "Adesso SE",
        imageUrl: "/images/companies/adesso.png",
        link: "https://www.adesso.de/",
        industry: "IT Services",
        highlight: true,
        color: "#2D3C55",
      },
      {
        id: 5,
        name: "HEGLA",
        imageUrl: "/images/companies/hegla.png",
        link: "https://www.hegla.com/",
        industry: "Manufacturing",
        highlight: false,
        color: "#008657",
      },
      {
        id: 6,
        name: "Lumeus",
        imageUrl: "/images/companies/lumeus.png",
        link: "https://lumeus.com/",
        industry: "Healthcare",
        highlight: true,
        color: "#6E3B98",
      },
    ],
    []
  );

  return (
    <Section
      titleClassName="text-gray-900 dark:text-white text-center mb-2"
      subtitleClassName="text-primary dark:text-blue-400 text-center mb-8"
      title={dictionary?.title ?? "About Us"}
      subtitle={dictionary?.subtitle ?? "Our Vision & Mission"}
      className="py-16 md:py-24 bg-gray-50 dark:bg-gray-900 relative"
      id="about"
    >
      {/* Simplified background */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50/20 via-transparent to-purple-50/20 dark:from-blue-900/10 dark:via-transparent dark:to-purple-900/10"></div>

      <div className="max-w-7xl mx-auto px-4 relative z-10" ref={ref}>
        <div className="grid md:grid-cols-2 gap-8 md:gap-16 items-center">
          {/* Left column: Image and info */}
          <ScrollAnimation
            direction="left"
            delay={0.1}
            className="overflow-hidden rounded-2xl shadow-2xl dark:shadow-none relative min-h-[360px] sm:min-h-[450px] md:h-[520px] group"
          >
            <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-black/10 to-transparent z-10 pointer-events-none" />

            <img
              src="/images/founder_small.png"
              alt={dictionary?.founderTitle ?? "Founder"}
              className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-105"
              loading="lazy"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src =
                  "https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1974&q=80";
              }}
            />
            <div className="absolute bottom-0 left-0 right-0 p-4 sm:p-6 md:p-8 z-20 text-white rounded-b-2xl">
              <h3 className="text-xl sm:text-2xl font-bold mb-1 sm:mb-2 flex items-center">
                <span className="bg-primary/20 w-8 h-8 rounded-full flex items-center justify-center mr-2">
                  <User className="h-4 w-4 text-primary-foreground" />
                </span>
                Hi, I'm Viktor
              </h3>
              <p className="text-white text-xs sm:text-sm leading-relaxed font-medium">
                Mobile & Web Developer specializing in modern, efficient
                solutions
              </p>

              {/* Simplified stats row */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6 mt-4">
                {stats.map((stat, index) => (
                  <div
                    key={index}
                    className="text-center bg-black/40 backdrop-blur-sm rounded-lg p-2 sm:p-3 flex-1 transition-colors border border-white/10"
                  >
                    <div className="text-2xl sm:text-3xl font-bold text-white dark:text-blue-400">
                      {stat.value}
                    </div>
                    <div className="text-xs text-white font-medium">
                      {stat.label}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </ScrollAnimation>

          {/* Right column: Tabs */}
          <ScrollAnimation direction="right" delay={0.2} className="space-y-6">
            {/* Tab navigation - Desktop only */}
            <div className="hidden md:flex justify-center md:justify-start">
              <div className="bg-gray-100 dark:bg-gray-800/50 rounded-xl p-1.5 flex shadow-md dark:shadow-none">
                <div className="flex space-x-1 mb-6">
                  {["vision", "mission", "founder"].map((tab) => (
                    <button
                      key={tab}
                      onClick={() => setActiveTab(tab as any)}
                      className={cn(
                        "flex-1 px-4 py-2 text-sm sm:text-base font-medium rounded-lg transition-all duration-200",
                        activeTab === tab
                          ? "bg-primary text-white shadow-md dark:shadow-none dark:bg-blue-600 dark:text-white"
                          : "bg-gray-100 text-gray-600 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
                      )}
                    >
                      {tab === "vision" && (dictionary?.vision ?? "Vision")}
                      {tab === "mission" && (dictionary?.mission ?? "Mission")}
                      {tab === "founder" &&
                        (dictionary?.founderTitle ?? "Founder")}
                    </button>
                  ))}
                </div>
              </div>
            </div>

            {/* Tab content - Desktop */}
            <div className="hidden md:block min-h-[300px] p-6 bg-white dark:bg-gray-800 rounded-2xl shadow-lg dark:shadow-none border border-gray-100 dark:border-gray-700/30">
              {activeTab === "vision" && (
                <div className="space-y-4">
                  <h3 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center gap-3">
                    <span className="bg-primary/10 dark:bg-blue-500/20 w-10 h-10 rounded-full flex items-center justify-center">
                      <Target className="text-primary dark:text-blue-400 h-5 w-5" />
                    </span>
                    {dictionary?.vision ?? "Vision"}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                    {dictionary?.visionDesc ??
                      "To shape the digital future by delivering innovative technology solutions that empower businesses to thrive in the modern world."}
                  </p>
                  <ul className="space-y-4">
                    {visionItems.map((item, index) => (
                      <li
                        key={index}
                        className="flex items-start gap-3 group p-2 hover:bg-gray-50 dark:hover:bg-gray-700/50 rounded-lg transition-colors"
                      >
                        <span className="bg-primary/10 dark:bg-blue-500/20 w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                          <CheckCircle className="text-primary dark:text-blue-400 h-3.5 w-3.5" />
                        </span>
                        <span className="text-gray-700 dark:text-gray-300">
                          {(dictionary?.[item.key] as string) ?? item.fallback}
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {activeTab === "mission" && (
                <div className="space-y-4">
                  <h3 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center gap-3">
                    <span className="bg-primary/10 dark:bg-blue-500/20 w-10 h-10 rounded-full flex items-center justify-center">
                      <Lightbulb className="text-primary dark:text-blue-400 h-5 w-5" />
                    </span>
                    {dictionary?.mission ?? "Mission"}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                    {dictionary?.missionDesc ??
                      "We create high-quality digital solutions using cutting-edge technologies like Flutter to help businesses solve complex problems and achieve their goals."}
                  </p>
                  <ul className="space-y-4">
                    {missionItems.map((item, index) => (
                      <li
                        key={index}
                        className="flex items-start gap-3 group p-2 hover:bg-gray-50 dark:hover:bg-gray-700/50 rounded-lg transition-colors"
                      >
                        <span className="bg-primary/10 dark:bg-blue-500/20 w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                          <CheckCircle className="text-primary dark:text-blue-400 h-3.5 w-3.5" />
                        </span>
                        <span className="text-gray-700 dark:text-gray-300">
                          {(dictionary?.[item.key] as string) ?? item.fallback}
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {activeTab === "founder" && (
                <div className="space-y-4">
                  <h3 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center gap-3">
                    <span className="bg-primary/10 dark:bg-blue-500/20 w-10 h-10 rounded-full flex items-center justify-center">
                      <User className="text-primary dark:text-blue-400 h-5 w-5" />
                    </span>
                    {dictionary?.founderTitle ?? "Founder"}
                  </h3>
                  <div className="space-y-4">
                    <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                      {dictionary?.founderDesc ??
                        "As a passionate mobile and web developer, I specialize in leveraging the latest technologies like Flutter to build modern, efficient solutions that help businesses scale and succeed."}
                    </p>
                    <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                      With expertise in Flutter, web development, and AI
                      integration, I create seamless cross-platform experiences
                      that combine beautiful design with robust functionality.
                    </p>
                    <div className="mt-6 p-4 bg-primary/5 dark:bg-blue-500/10 rounded-xl border border-primary/10 dark:border-blue-500/20">
                      <p className="text-gray-800 dark:text-white text-lg font-medium text-center">
                        <span className="text-primary dark:text-blue-400 font-bold block mb-1">
                          My motto:
                        </span>
                        "You never lose. Either you win, or you learn."
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Mobile view - simplified stack */}
            <div className="block md:hidden space-y-8">
              {[
                {
                  key: "vision",
                  icon: Target,
                  title: dictionary?.vision ?? "Vision",
                  desc: dictionary?.visionDesc,
                  items: visionItems,
                },
                {
                  key: "mission",
                  icon: Lightbulb,
                  title: dictionary?.mission ?? "Mission",
                  desc: dictionary?.missionDesc,
                  items: missionItems,
                },
                {
                  key: "founder",
                  icon: User,
                  title: dictionary?.founderTitle ?? "Founder",
                  desc: dictionary?.founderDesc,
                  items: [],
                },
              ].map((section) => (
                <div
                  key={section.key}
                  className="p-6 bg-white dark:bg-gray-800 rounded-2xl shadow-lg dark:shadow-none border border-gray-100 dark:border-gray-700/30"
                >
                  <h3 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center gap-3 mb-4">
                    <span className="bg-primary/10 dark:bg-blue-500/20 w-10 h-10 rounded-full flex items-center justify-center">
                      <section.icon className="text-primary dark:text-blue-400 h-5 w-5" />
                    </span>
                    {section.title}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 leading-relaxed mb-4">
                    {section.desc}
                  </p>
                  {section.items.length > 0 && (
                    <ul className="space-y-3">
                      {section.items.map((item, index) => (
                        <li key={index} className="flex items-start gap-3">
                          <span className="bg-primary/10 dark:bg-blue-500/20 w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                            <CheckCircle className="text-primary dark:text-blue-400 h-3.5 w-3.5" />
                          </span>
                          <span className="text-gray-700 dark:text-gray-300">
                            {(dictionary?.[item.key] as string) ??
                              item.fallback}
                          </span>
                        </li>
                      ))}
                    </ul>
                  )}
                  {section.key === "founder" && (
                    <div className="mt-6 p-4 bg-primary/5 dark:bg-blue-500/10 rounded-xl border border-primary/10 dark:border-blue-500/20">
                      <p className="text-gray-800 dark:text-white text-lg font-medium text-center">
                        <span className="text-primary dark:text-blue-400 font-bold block mb-1">
                          My motto:
                        </span>
                        "You never lose. Either you win, or you learn."
                      </p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </ScrollAnimation>
        </div>
      </div>

      {/* Clients Section */}
      <div className="mt-20 pt-16 border-t border-gray-100 dark:border-gray-800">
        <div className="text-center mb-10">
          <ScrollAnimation direction="bottom" delay={0.1} className="w-full">
            <h2 className="text-3xl md:text-4xl font-bold text-center mb-3 dark:text-white bg-gradient-to-r from-blue-600 to-indigo-600 dark:from-blue-400 dark:to-indigo-400 bg-clip-text text-transparent inline-block">
              {clientsDictionary?.title ?? "Our Clients"}
            </h2>
            <p className="text-gray-600 dark:text-gray-300 text-center mb-2 max-w-2xl mx-auto text-lg">
              {clientsDictionary?.subtitle ?? "Companies We've Worked With"}
            </p>
            <div className="w-20 h-1 bg-gradient-to-r from-blue-500 to-indigo-500 mx-auto mb-10 rounded-full"></div>
          </ScrollAnimation>
        </div>

        {/* Desktop clients grid */}
        <div className="hidden md:block relative w-full bg-gray-50/50 dark:bg-gray-900/30 py-10 rounded-2xl">
          <div className="max-w-6xl mx-auto px-4">
            <div className="grid grid-cols-3 gap-8 items-center justify-items-center">
              {clients.map((client) => (
                <ScrollAnimation
                  key={`client-desktop-${client.id}`}
                  direction={client.id % 2 === 0 ? "left" : "right"}
                  delay={0.1 + client.id * 0.05}
                  className="w-full flex justify-center"
                >
                  <ClientCard
                    client={client}
                    isMobile={false}
                    onClientClick={handleClientClick}
                  />
                </ScrollAnimation>
              ))}
            </div>
          </div>
        </div>

        {/* Mobile clients marquee */}
        <div className="md:hidden relative w-full">
          <ScrollAnimation direction="bottom" delay={0.2} className="w-full">
            <Marquee
              gradient={false}
              speed={30}
              pauseOnHover={true}
              pauseOnClick={true}
              direction="left"
              className="py-6"
            >
              {clients.map((client) => (
                <ClientCard
                  key={`client-mobile-${client.id}`}
                  client={client}
                  isMobile={true}
                  onClientClick={handleClientClick}
                />
              ))}
            </Marquee>
          </ScrollAnimation>
        </div>

        {/* Helper indicator - mobile only */}
        {isMobile && (
          <div className="flex justify-center mt-2 mb-1">
            <div className="flex items-center gap-1.5 text-xs text-gray-500 bg-gray-100 dark:bg-gray-800 dark:text-gray-400 px-3 py-1.5 rounded-full">
              <ChevronLeft size={14} />
              <span>Swipe</span>
              <ChevronRight size={14} />
            </div>
          </div>
        )}

        <div className="text-center mt-6 text-sm text-gray-500 dark:text-gray-400 flex items-center justify-center gap-2">
          <span className="inline-block w-2 h-2 bg-primary/50 rounded-full animate-pulse"></span>
          {isMobile
            ? "Swipe logos to browse, tap to visit"
            : "Hover over logos to pause, click to visit website"}
        </div>
      </div>
    </Section>
  );
};