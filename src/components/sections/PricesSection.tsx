"use client";

import { Section } from "@/components/ui/Section";
import { Card } from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import {
  Check,
  Info,
  Mail,
  Star,
  Crown,
  Shield,
  Zap,
  TrendingUp,
  Award,
  Clock,
  Users,
  Globe,
  Sparkles,
  CheckCircle,
  ArrowRight,
  Calculator,
  DollarSign,
  Verified,
  MessageCircle,
} from "lucide-react";
import { type Dictionary } from "@/lib/dictionary";
import Link from "next/link";
import { useState, useMemo, useCallback, memo } from "react";
import { motion, AnimatePresence, useReducedMotion } from "framer-motion";
import {
  ScrollAnimation,
  ScrollAnimationGroup,
} from "@/components/ui/ScrollAnimation";
import PricingCalculatorClient from "@/components/pricing/PricingCalculatorClient";

interface PricesSectionProps {
  dictionary?: Dictionary["prices"];
  aboutDictionary?: Dictionary["about"];
}

// Memoized package card component to prevent unnecessary re-renders
const PackageCard = memo(
  ({
    pkg,
    index,
    hoveredPackage,
    setHoveredPackage,
    tooltips,
    toggleTooltip,
    handleContactClick,
    createWhatsAppMessage,
    dictionary,
  }: {
    pkg: any;
    index: number;
    hoveredPackage: string | null;
    setHoveredPackage: (id: string | null) => void;
    tooltips: { [key: string]: boolean };
    toggleTooltip: (id: string) => void;
    handleContactClick: (pkg: any) => void;
    createWhatsAppMessage: (pkg: any) => string;
    dictionary: any;
  }) => {
    const shouldReduceMotion = useReducedMotion();

    return (
      <motion.div
        key={pkg.id}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: index * 0.1 }}
        onMouseEnter={() => setHoveredPackage(pkg.id)}
        onMouseLeave={() => setHoveredPackage(null)}
        className="group relative"
      >
        {/* Package badge */}
        {pkg.badge && (
          <div className="absolute -top-2 -right-2 z-20">
            <div
              className={`text-white px-4 py-2 rounded-full text-sm font-semibold shadow-lg flex items-center gap-2 ${
                pkg.popular
                  ? "bg-gradient-to-r from-blue-500 to-indigo-600"
                  : `bg-gradient-to-r ${pkg.color}`
              }`}
            >
              {pkg.popular ? (
                <Crown className="w-4 h-4" />
              ) : (
                <Award className="w-4 h-4" />
              )}
              {pkg.badge}
            </div>
          </div>
        )}

        <div
          className={`h-full flex flex-col relative overflow-hidden border-2 rounded-3xl transition-all duration-300 hover:shadow-xl ${
            pkg.popular
              ? "bg-gradient-to-br from-blue-50/90 to-indigo-50/90 dark:from-blue-900/30 dark:to-indigo-900/30 border-blue-400/60 dark:border-blue-500/40 shadow-lg"
              : "bg-white/90 dark:bg-gray-800/90 border-gray-300/60 dark:border-gray-600/40 shadow-lg"
          }`}
        >
          {/* Pricing card content */}
          <div className="p-8 flex-grow relative z-10">
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
              <div
                className={`w-12 h-12 rounded-xl bg-gradient-to-br ${pkg.color} flex items-center justify-center text-white shadow-lg`}
              >
                {pkg.icon}
              </div>
              <button
                onClick={() => toggleTooltip(pkg.id)}
                className="text-gray-400 hover:text-blue-500 dark:hover:text-blue-400 transition-colors"
                aria-label="More information"
              >
                <Info className="h-5 w-5" />
              </button>
            </div>

            {/* Package title and timeframe */}
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
              {pkg.title}
            </h3>
            <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400 mb-6">
              <Clock className="w-4 h-4" />
              <span>{pkg.timeframe}</span>
            </div>

            {/* Info Tooltip */}
            <AnimatePresence>
              {tooltips[pkg.id] && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: "auto" }}
                  exit={{ opacity: 0, height: 0 }}
                  className="mb-6 p-4 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/30 dark:to-indigo-900/30 rounded-xl border border-blue-200/50 dark:border-blue-800/30 overflow-hidden"
                >
                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 rounded-full bg-blue-500 flex items-center justify-center flex-shrink-0 mt-0.5">
                      <Info className="w-3 h-3 text-white" />
                    </div>
                    <p className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
                      {dictionary?.priceVariesInfo ||
                        "Price may vary based on project complexity, additional requirements, and timeline constraints. Contact us for a detailed quote."}
                    </p>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Pricing */}
            <div className="mb-6">
              <div className="flex items-baseline gap-3 mb-2">
                <span className="text-4xl font-bold text-gray-900 dark:text-white">
                  {pkg.price}
                </span>
                {pkg.originalPrice && (
                  <span className="text-lg text-gray-400 dark:text-gray-500 line-through">
                    {pkg.originalPrice}
                  </span>
                )}
              </div>
              {pkg.savings && (
                <div className="inline-flex items-center gap-1 px-3 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 rounded-full text-sm font-medium">
                  <TrendingUp className="w-3 h-3" />
                  {pkg.savings}
                </div>
              )}
            </div>

            {/* Description */}
            <p className="text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">
              {pkg.description}
            </p>

            {/* Trust indicators */}
            <div className="flex flex-wrap gap-2 mb-6">
              {pkg.trustIndicators?.map((indicator: string, idx: number) => (
                <span
                  key={idx}
                  className="px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full text-xs font-medium"
                >
                  {indicator}
                </span>
              ))}
            </div>

            {/* Features list */}
            <ul className="space-y-4 mb-8">
              {pkg.features?.map((feature: string, featIndex: number) => (
                <li key={featIndex} className="flex items-start gap-3">
                  <div className="w-5 h-5 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center flex-shrink-0 mt-0.5">
                    <Check className="w-3 h-3 text-green-600 dark:text-green-400" />
                  </div>
                  <span className="text-gray-700 dark:text-gray-300 text-sm leading-relaxed">
                    {feature}
                  </span>
                </li>
              ))}
            </ul>
          </div>

          {/* CTA buttons */}
          <div className="p-8 pt-0 mt-auto relative z-10">
            {/* Contact Form Button */}
            <button
              onClick={() => handleContactClick(pkg)}
              className={`w-full py-4 px-6 rounded-2xl font-semibold transition-all duration-300 flex items-center justify-center gap-3 relative overflow-hidden group mb-3 ${
                pkg.popular
                  ? "bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white shadow-lg hover:shadow-xl"
                  : "bg-gray-900 dark:bg-white text-white dark:text-gray-900 hover:bg-gray-800 dark:hover:bg-gray-100 shadow-lg hover:shadow-xl"
              }`}
            >
              <Mail className="w-5 h-5 relative z-10" />
              <span className="relative z-10">
                {(dictionary as any)?.contactUs || "Contact Us"}
              </span>
              <ArrowRight className="w-5 h-5 relative z-10 group-hover:translate-x-1 transition-transform" />
            </button>

            {/* WhatsApp Contact Button */}
            <a
              href={createWhatsAppMessage(pkg)}
              target="_blank"
              rel="noopener noreferrer"
              className="block w-full"
            >
              <button className="w-full py-4 px-6 rounded-2xl font-semibold transition-all duration-300 flex items-center justify-center gap-3 relative overflow-hidden group bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white shadow-lg hover:shadow-xl">
                <MessageCircle className="w-5 h-5 relative z-10" />
                <span className="relative z-10">WhatsApp Chat</span>
                <ArrowRight className="w-5 h-5 relative z-10 group-hover:translate-x-1 transition-transform" />
              </button>
            </a>
          </div>
        </div>
      </motion.div>
    );
  }
);

PackageCard.displayName = "PackageCard";

const PricesSection = ({ dictionary, aboutDictionary }: PricesSectionProps) => {
  const shouldReduceMotion = useReducedMotion();
  const [tooltips, setTooltips] = useState<{ [key: string]: boolean }>({});
  const [hoveredPackage, setHoveredPackage] = useState<string | null>(null);

  const toggleTooltip = useCallback((id: string) => {
    setTooltips((prev) => ({ ...prev, [id]: !prev[id] }));
  }, []);

  const handleContactClick = useCallback((pkg: any) => {
    // Use package ID directly since it maps to service IDs
    const serviceId = pkg.id;

    // Check if we're in the browser
    if (typeof window === "undefined") return;

    // Scroll to contact form
    const contactSection = document.getElementById("contact");
    if (contactSection) {
      contactSection.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });

      // Set URL parameter for service selection
      const url = new URL(window.location.href);
      url.searchParams.set("service", serviceId);
      window.history.replaceState({}, "", url.toString());

      // Trigger custom event to notify contact form
      setTimeout(() => {
        window.dispatchEvent(
          new CustomEvent("selectService", {
            detail: { serviceId },
          })
        );
      }, 1000); // Wait for scroll to complete
    }
  }, []);

  const createWhatsAppMessage = useCallback((pkg: any) => {
    const message = encodeURIComponent(
      `Hi! I'm interested in the ${pkg.title} package (${pkg.price}).

Service: ${pkg.title}
Timeframe: ${pkg.timeframe}
Description: ${pkg.description}

Could you please provide more details about this service and your availability?

Thank you!`
    );

    return `https://wa.me/491759918357?text=${message}`;
  }, []);

  // Enhanced packages data with trust indicators and modern styling
  const packages = useMemo(
    () => [
      {
        id: "mvp",
        title: dictionary?.packages?.mvp?.title || "MVP Development",
        timeframe: dictionary?.packages?.mvp?.timeframe || "3-4 weeks",
        description:
          dictionary?.packages?.mvp?.description ||
          "Launch your idea quickly with a Minimum Viable Product",
        price: "€8,500",
        originalPrice: "€10,000",
        popular: true,
        badge: "Most Popular",
        color: "from-blue-500 to-indigo-600",
        icon: <Zap className="w-6 h-6" />,
        savings: "Save 15%",
        features: dictionary?.packages?.mvp?.features || [
          "Core functionality implementation",
          "Modern UI/UX design",
          "User authentication & security",
          "Scalable data storage solution",
          "Cross-platform deployment",
          "Worldwide Compliance Ready",
          "3 months support included",
          "Performance optimization",
        ],
        trustIndicators: ["⚡ Fast Delivery", "🔒 Secure", "📱 Modern"],
      },
      {
        id: "prototype",
        title: dictionary?.packages?.prototype?.title || "Rapid Prototype",
        timeframe: dictionary?.packages?.prototype?.timeframe || "1-2 weeks",
        description:
          dictionary?.packages?.prototype?.description ||
          "Test your concept with a functional prototype",
        price: "€4,200",
        originalPrice: "€5,000",
        badge: "Great Start",
        color: "from-emerald-500 to-teal-600",
        icon: <TrendingUp className="w-6 h-6" />,
        savings: "Save 16%",
        features: dictionary?.packages?.prototype?.features || [
          "Interactive UI mockups",
          "Core functionality demo",
          "User flow implementation",
          "Stakeholder presentation",
          "Flutter-driven development",
          "Iteration feedback loops",
          "Technical documentation",
        ],
        trustIndicators: ["🚀 Quick Setup", "✨ Interactive", "📊 Analytics"],
      },
      {
        id: "landingpage",
        title:
          dictionary?.packages?.landingpage?.title ||
          "Landing Page Development",
        timeframe: dictionary?.packages?.landingpage?.timeframe || "2-4 weeks",
        description:
          dictionary?.packages?.landingpage?.description ||
          "WCAG 2.0 compliant landing pages with the latest web technologies",
        price: "€3,000",
        originalPrice: "€3,500",
        badge: "Accessibility Focus",
        color: "from-purple-500 to-pink-600",
        icon: <Globe className="w-6 h-6" />,
        savings: "Save 14%",
        features: dictionary?.packages?.landingpage?.features || [
          "WCAG 2.0 AA compliance",
          "Inclusive design for all users",
          "Screen reader compatibility",
          "High-performance metrics",
          "SEO optimized structure",
          "Responsive design",
          "Analytics integration",
          "Conversion optimization",
        ],
        trustIndicators: ["♿ Accessible", "🎯 Optimized", "📈 High Convert"],
      },
      {
        id: "architecture",
        title:
          dictionary?.packages?.architecture?.title || "Project Architecture",
        timeframe: dictionary?.packages?.architecture?.timeframe || "1-2 weeks",
        description:
          dictionary?.packages?.architecture?.description ||
          "Solid foundation for your project's success",
        price: "€3,800",
        originalPrice: "€4,500",
        badge: "Professional",
        color: "from-orange-500 to-amber-600",
        icon: <Shield className="w-6 h-6" />,
        savings: "Save 16%",
        features: dictionary?.packages?.architecture?.features || [
          "Technical specifications",
          "System architecture design",
          "Database schema design",
          "API documentation",
          "Development roadmap",
          "Security planning",
          "Scalability guidelines",
          "Technology recommendations",
        ],
        trustIndicators: ["🏗️ Scalable", "🔐 Secure", "📋 Documented"],
      },
      {
        id: "consulting",
        title:
          dictionary?.packages?.consulting?.title || "Technical Consulting",
        timeframe: dictionary?.packages?.consulting?.timeframe || "Ongoing",
        description:
          dictionary?.packages?.consulting?.description ||
          "Expert guidance for your technical decisions",
        price: "€110/hour",
        originalPrice: "€130/hour",
        badge: "Ultimate",
        color: "from-violet-500 to-purple-600",
        icon: <Users className="w-6 h-6" />,
        savings: "Save 15%",
        features: dictionary?.packages?.consulting?.features || [
          "Technology stack recommendations",
          "Code reviews & quality assurance",
          "Performance optimization",
          "Security assessment",
          "Scalability planning",
          "Payment gateway integration",
          "Team mentoring",
          "Best practices implementation",
        ],
        trustIndicators: ["👨‍💻 Expert", "⚡ Available", "🎓 Mentoring"],
      },
    ],
    [dictionary]
  );

  return (
    <Section
      id="prices"
      title={dictionary?.title || "Unsere Preise"}
      subtitle={dictionary?.subtitle || "Transparente Preise für jede Phase"}
      description={
        dictionary?.description ||
        "Choose from our transparent pricing packages designed to fit your project needs. No hidden costs, no surprises."
      }
      className="py-20 md:py-28 bg-gradient-to-br from-gray-50 via-white to-blue-50/30 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 relative w-full"
      containerClassName="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10"
      titleClassName="text-gray-900 dark:text-white text-center"
      subtitleClassName="text-blue-600 dark:text-blue-400 text-center"
    >
      {/* Simplified Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50/20 via-transparent to-purple-50/20 dark:from-blue-900/10 dark:via-transparent dark:to-purple-900/10"></div>

      {/* Pricing disclaimer */}
      <motion.div
        className="mb-8 text-center"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.3 }}
      >
        <div className="inline-flex items-center gap-2 px-4 py-2 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800/30 rounded-full">
          <Shield className="w-4 h-4 text-amber-600 dark:text-amber-400" />
          <p className="text-amber-700 dark:text-amber-300 text-sm font-medium">
            {(dictionary as any)?.disclaimer ||
              "* Prices may vary based on project requirements and additional services. Contact us for a custom quote tailored to your specific needs."}
          </p>
        </div>
      </motion.div>

      {/* Enhanced Pricing packages with better borders */}
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8 mt-12">
        {packages.map((pkg, index) => (
          <PackageCard
            key={pkg.id}
            pkg={pkg}
            index={index}
            hoveredPackage={hoveredPackage}
            setHoveredPackage={setHoveredPackage}
            tooltips={tooltips}
            toggleTooltip={toggleTooltip}
            handleContactClick={handleContactClick}
            createWhatsAppMessage={createWhatsAppMessage}
            dictionary={dictionary}
          />
        ))}
      </div>

      {/* Pricing Calculator Section */}
      <motion.div
        className="mt-24"
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.6 }}
      >
        <div id="pricing-calculator" className="scroll-mt-24">
          <div className="text-center mb-12">
            <div className="inline-flex items-center gap-3 mb-4">
              <div className="w-12 h-12 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center">
                <Calculator className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-3xl font-bold text-gray-900 dark:text-white">
                {dictionary?.calculatorTitle || "Pricing Calculator"}
              </h3>
            </div>
            <p className="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
              Customize your perfect package and get an instant quote tailored
              to your specific needs.
            </p>
          </div>

          <div className="bg-white/90 dark:bg-gray-800/90 border-2 border-gray-300/60 dark:border-gray-600/40 rounded-3xl p-8 shadow-xl max-w-[1400px] mx-auto relative">
            <div className="relative z-10">
              <PricingCalculatorClient dictionary={dictionary} />
            </div>
          </div>
        </div>
      </motion.div>
    </Section>
  );
};

export default PricesSection;
