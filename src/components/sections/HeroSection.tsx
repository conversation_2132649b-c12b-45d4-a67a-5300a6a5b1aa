"use client";

import React from "react";
import { type Dictionary } from "@/lib/dictionary";
import { motion, useReducedMotion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { CheckCircle, ArrowRight } from "lucide-react";
import Link from "next/link";
import HeroMockup from "@/components/ui/HeroMockup";

interface HeroSectionProps {
  dictionary: Dictionary["hero"];
  aboutDictionary?: Dictionary["about"];
}



// Background elements component
const BackgroundElements = React.memo(() => {
  const shouldReduceMotion = useReducedMotion();

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {/* Gradient background */}
      <div className="absolute inset-0 bg-slate-900" />
      
      {/* Subtle pattern overlay */}
      <div className="absolute inset-0 opacity-5">
        <div
          className="w-full h-full"
          style={{
            backgroundImage: `radial-gradient(circle at 2px 2px, rgba(59, 130, 246, 0.3) 1px, transparent 0)`,
            backgroundSize: "40px 40px",
          }}
        />
      </div>
      
      {/* Floating orbs */}
      {!shouldReduceMotion && (
        <>
          <motion.div
            className="absolute w-64 h-64 rounded-full opacity-10"
            style={{
              background: "radial-gradient(circle, rgba(59, 130, 246, 0.4), transparent)",
              left: "10%",
              top: "20%",
            }}
            animate={{
              x: [0, 30, 0],
              y: [0, 20, 0],
            }}
            transition={{
              duration: 20,
              repeat: Infinity,
              repeatType: "reverse",
              ease: "linear",
            }}
          />
          <motion.div
            className="absolute w-48 h-48 rounded-full opacity-10"
            style={{
              background: "radial-gradient(circle, rgba(139, 92, 246, 0.4), transparent)",
              right: "15%",
              bottom: "30%",
            }}
            animate={{
              x: [0, -25, 0],
              y: [0, 15, 0],
            }}
            transition={{
              duration: 25,
              repeat: Infinity,
              repeatType: "reverse",
              ease: "linear",
            }}
          />
        </>
      )}
      
      {/* Bottom gradient transition */}
      <div className="absolute bottom-0 left-0 w-full h-32 bg-gradient-to-t from-slate-900 to-transparent" />
    </div>
  );
});

BackgroundElements.displayName = "BackgroundElements";

export const HeroSection = ({ dictionary, aboutDictionary }: HeroSectionProps) => {
  const shouldReduceMotion = useReducedMotion();
  
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  // Animation variants
  const fadeInUp = {
    hidden: { opacity: 0, y: 30 },
    visible: { opacity: 1, y: 0 },
  };

  const fadeInRight = {
    hidden: { opacity: 0, x: 30 },
    visible: { opacity: 1, x: 0 },
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1,
      },
    },
  };

  return (
    <section
      id="hero"
      className="relative min-h-screen w-full text-white pt-20 pb-16 md:pt-24 md:pb-20 overflow-hidden"
    >
      {/* Background Elements */}
      <BackgroundElements />

      {/* Main Content */}
      <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 h-full">
        <div className="grid lg:grid-cols-2 gap-8 sm:gap-12 lg:gap-16 items-center min-h-[calc(100vh-8rem)]">
          {/* Left Column - Text Content */}
          <motion.div
            className="space-y-6 sm:space-y-8 lg:pr-8 text-center lg:text-left"
            variants={staggerContainer}
            initial="hidden"
            animate="visible"
          >
            {/* Headline */}
            <motion.h1
              className="text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-bold leading-tight text-slate-50"
              variants={fadeInUp}
              transition={{ duration: 0.8, ease: "easeOut" }}
            >
              {dictionary.title}
            </motion.h1>

            {/* Subheadline */}
            <motion.p
              className="text-lg sm:text-xl lg:text-2xl text-slate-300 leading-relaxed max-w-2xl mx-auto lg:mx-0"
              variants={fadeInUp}
              transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
            >
              {dictionary.subtitle}
            </motion.p>

            {/* Primary CTA */}
            <motion.div
              className="pt-6 flex justify-center lg:justify-start"
              variants={fadeInUp}
              transition={{ duration: 0.8, delay: 0.4, ease: "easeOut" }}
            >
              <Link
                href="#contact"
                className="inline-flex items-center gap-3 bg-gradient-to-r from-violet-500 to-indigo-600 hover:from-violet-600 hover:to-indigo-700 text-white font-semibold px-6 py-3 sm:px-8 sm:py-4 rounded-2xl text-base sm:text-lg transition-all duration-300 transform hover:scale-105 hover:shadow-2xl group"
              >
                {dictionary.cta.startProject}
                <ArrowRight className="w-4 h-4 sm:w-5 sm:h-5 transition-transform group-hover:translate-x-1" />
              </Link>
            </motion.div>

            {/* Subtle Text Links */}
            <motion.div
              className="flex flex-wrap gap-4 sm:gap-6 pt-4 justify-center lg:justify-start"
              variants={fadeInUp}
              transition={{ duration: 0.8, delay: 0.6, ease: "easeOut" }}
            >
              <Link
                href="#consultation"
                className="relative text-sm text-neutral-300 hover:text-white hover:underline transition-all duration-300 underline-offset-4 flex items-center gap-2"
              >
                <span className="relative flex h-2 w-2">
                  <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-green-400 opacity-75"></span>
                  <span className="relative inline-flex rounded-full h-2 w-2 bg-green-500"></span>
                </span>
                {dictionary.cta.freeConsultation}
              </Link>
              <Link
                href="#calculator"
                className="text-sm text-neutral-300 hover:text-white hover:underline transition-all duration-300 underline-offset-4"
              >
                {dictionary.cta.calculateCost}
              </Link>
            </motion.div>

            {/* Trust Indicators */}
            <motion.div
              className="pt-4 sm:pt-6 space-y-3"
              variants={fadeInUp}
              transition={{ duration: 0.8, delay: 0.8, ease: "easeOut" }}
            >
              <div className="flex flex-wrap gap-3 sm:gap-4 text-xs sm:text-sm text-slate-400 justify-center lg:justify-start">
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-green-400" />
                  <span>8+ Jahre Erfahrung</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-violet-400" />
                  <span>15+ Projekte</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-indigo-400" />
                  <span>100% Qualitätsfokus</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-emerald-400" />
                  <span>100% Zufriedenheitsgarantie</span>
                </div>
              </div>
            </motion.div>
          </motion.div>

          {/* Right Column - Visual Element */}
          <motion.div
            className="relative w-full h-full flex items-start justify-center pt-8 lg:pt-0"
            initial={{ opacity: 0, x: 100 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            <HeroMockup 
              imageAlt="App Screenshots"
              appScreenshots={[
                "/images/hero/togg.png",
                "/images/hero/aigo.png",
                "/images/hero/menu.png",
                "/images/hero/detoxme.png",
                "/images/hero/reserv.png"
              ]}
            />
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
