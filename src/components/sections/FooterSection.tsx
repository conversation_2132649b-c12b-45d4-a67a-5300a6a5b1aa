'use client'

import { useState, useMemo, useCallback } from "react";
import Link from "next/link";
import { motion, useReducedMotion } from "framer-motion";
import { useI18n } from '@/providers/I18nProvider'
import { type Dictionary } from '@/lib/dictionary'
import {
  Mail,
  Phone,
  Github,
  Linkedin,
  MapPin,
  Clock,
  Shield,
  Award,
  Star,
  Heart,
  Zap,
  CheckCircle,
  ArrowUp,
  Sparkles,
  Globe,
  Users,
  Calendar,
} from "lucide-react";

// Enhanced social media links with modern icons and additional platforms - memoized
const socialLinks = [
  {
    name: "Email",
    icon: <Mail className="w-5 h-5" />,
    url: "mailto:<EMAIL>",
    color: "from-blue-500 to-indigo-600",
    hoverColor: "hover:bg-blue-500/20",
    description: "Send us an email",
  },
  {
    name: "Phone",
    icon: <Phone className="w-5 h-5" />,
    url: "tel:+491759918357",
    color: "from-green-500 to-emerald-600",
    hoverColor: "hover:bg-green-500/20",
    description: "Call us directly",
  },
  {
    name: "<PERSON>it<PERSON><PERSON>",
    icon: <Github className="w-5 h-5" />,
    url: "https://github.com/Viktor-Hermann/",
    color: "from-gray-700 to-gray-900",
    hoverColor: "hover:bg-gray-500/20",
    description: "Check our code",
  },
  {
    name: "LinkedIn",
    icon: <Linkedin className="w-5 h-5" />,
    url: "https://www.linkedin.com/in/viktor-hermann-103125245/",
    color: "from-blue-600 to-blue-800",
    hoverColor: "hover:bg-blue-600/20",
    description: "Professional network",
  },
];

// Enhanced navigation links with better organization - memoized
const navigationLinks = [
  { name: "nav.home", href: "#hero", category: "main" },
  { name: "nav.services", href: "#services", category: "main" },
  { name: "nav.solutions", href: "#solutions-portfolio", category: "main" },
  { name: "nav.about", href: "#about", category: "main" },
  { name: "nav.testimonials", href: "#testimonials", category: "secondary" },
  { name: "nav.prices", href: "#prices", category: "secondary" },
  { name: "nav.contact", href: "#contact", category: "secondary" },
];

// Enhanced services links - memoized
const serviceLinks = [
  { name: "MVP Development", href: "#services" },
  { name: "Mobile Apps", href: "#services" },
  { name: "Web Development", href: "#services" },
  { name: "Consulting", href: "#services" },
];

interface FooterSectionProps {
  dictionary: Dictionary["footer"];
  aboutDictionary?: Dictionary["about"];
  locale?: string;
}

export const FooterSection = ({
  dictionary,
  aboutDictionary,
  locale = "en",
}: FooterSectionProps) => {
  const [hoveredSocial, setHoveredSocial] = useState<string | null>(null);
  const [showScrollTop, setShowScrollTop] = useState(false);
  const { t, dir } = useI18n();
  const isRtl = dir === "rtl";

  // Performance optimization: useReducedMotion hook
  const shouldReduceMotion = useReducedMotion();

  // Memoized trust indicators for footer
  const trustIndicators = useMemo(
    () => [
      {
        icon: <Shield className="w-4 h-4" />,
        text: "Privacy Focused",
        color: "text-green-400",
      },
      {
        icon: <Award className="w-4 h-4" />,
        text: "8+ Years Experience",
        color: "text-blue-400",
      },
      {
        icon: <Users className="w-4 h-4" />,
        text: "Personal Service",
        color: "text-purple-400",
      },
      {
        icon: <Star className="w-4 h-4" />,
        text: "Quality Focused",
        color: "text-yellow-400",
      },
    ],
    []
  );

  // Memoized animation configuration
  const animationConfig = useMemo(
    () => ({
      floatingOrb1: shouldReduceMotion
        ? {}
        : {
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.5, 0.3],
            x: [0, 20, 0],
            y: [0, -15, 0],
          },
      floatingOrb2: shouldReduceMotion
        ? {}
        : {
            scale: [1.1, 1, 1.1],
            opacity: [0.2, 0.4, 0.2],
            x: [0, -15, 0],
            y: [0, 15, 0],
          },
      sparkle: shouldReduceMotion
        ? {}
        : {
            rotate: [0, 360],
            scale: [1, 1.2, 1],
            opacity: [0.2, 0.4, 0.2],
          },
    }),
    [shouldReduceMotion]
  );

  // Memoized scroll to top function
  const scrollToTop = useCallback(() => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  }, []);

  return (
    <footer
      className={`w-full bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 dark:from-gray-950 dark:via-gray-900 dark:to-gray-950 text-white relative overflow-hidden ${
        isRtl ? "rtl-section" : ""
      }`}
    >
      {/* Enhanced Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-900/10 via-transparent to-purple-900/10"></div>

      {/* Floating Orb Elements - Conditional animations */}
      {!shouldReduceMotion && (
        <>
          <motion.div
            className="absolute top-20 left-12 w-40 h-40 bg-gradient-radial from-blue-400/10 via-indigo-400/5 to-transparent rounded-full blur-3xl"
            animate={animationConfig.floatingOrb1}
            transition={{
              duration: 12,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          />
          <motion.div
            className="absolute bottom-20 right-16 w-32 h-32 bg-gradient-radial from-purple-400/8 via-pink-400/4 to-transparent rounded-full blur-2xl"
            animate={animationConfig.floatingOrb2}
            transition={{
              duration: 10,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 3,
            }}
          />
        </>
      )}

      {/* Premium Gradient Line */}
      <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-blue-400/60 to-transparent"></div>

      {/* Floating Sparkle Elements - Conditional animations */}
      {!shouldReduceMotion && (
        <motion.div
          className="absolute top-16 right-1/4 text-blue-400/20"
          animate={animationConfig.sparkle}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        >
          <Sparkles className="w-6 h-6" />
        </motion.div>
      )}

      <div className="max-w-7xl mx-auto px-4 py-16 relative z-10">
        {/* Enhanced Trust Indicators Bar */}
        <motion.div
          className="mb-12 flex flex-wrap justify-center items-center gap-6 md:gap-10 p-6 rounded-2xl bg-white/5 backdrop-blur-xl border border-white/10 shadow-lg"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          {trustIndicators.map((indicator, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.4 + index * 0.1 }}
              className="flex items-center gap-2 text-center"
            >
              <div className={`${indicator.color} bg-white/5 p-2 rounded-full`}>
                {indicator.icon}
              </div>
              <span className="text-sm font-medium text-gray-300">
                {indicator.text}
              </span>
            </motion.div>
          ))}
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-12">
          {/* Enhanced Company info */}
          <motion.div
            className="lg:col-span-2 space-y-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <div>
              <motion.h2
                className="text-3xl font-bold bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent mb-4"
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.2 }}
              >
                Innovatio-Pro
              </motion.h2>
              <p className="text-gray-300 text-lg leading-relaxed max-w-md">
                {dictionary.description ||
                  "Creating innovative digital solutions that transform businesses and empower growth through cutting-edge technology."}
              </p>
            </div>

            {/* Enhanced Social links */}
            <div>
              <h4 className="text-sm font-semibold text-gray-200 mb-4 uppercase tracking-wider">
                Connect With Us
              </h4>
              <div className="flex gap-4">
                {socialLinks.map((social) => (
                  <motion.a
                    key={social.name}
                    href={social.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    onMouseEnter={() => setHoveredSocial(social.name)}
                    onMouseLeave={() => setHoveredSocial(null)}
                    whileHover={{ scale: 1.1, y: -2 }}
                    whileTap={{ scale: 0.95 }}
                    className={`group relative p-3 rounded-xl transition-all duration-300 bg-white/5 ${social.hoverColor} border border-white/10 hover:border-white/20`}
                    aria-label={social.name}
                    title={social.description}
                  >
                    {/* Hover glow effect */}
                    <div
                      className={`absolute inset-0 rounded-xl bg-gradient-to-r ${social.color} opacity-0 group-hover:opacity-10 transition-opacity duration-300`}
                    ></div>

                    <div className="relative text-white/70 group-hover:text-white transition-colors">
                      {social.icon}
                    </div>

                    {/* Tooltip */}
                    {hoveredSocial === social.name && (
                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="absolute -top-12 left-1/2 transform -translate-x-1/2 px-3 py-1 bg-gray-800 text-white text-xs rounded-lg whitespace-nowrap border border-gray-600"
                      >
                        {social.description}
                        <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"></div>
                      </motion.div>
                    )}
                  </motion.a>
                ))}
              </div>
            </div>

            {/* Business Hours */}
            <div className="bg-white/5 backdrop-blur-sm rounded-xl p-4 border border-white/10">
              <div className="flex items-center gap-2 mb-2">
                <Clock className="w-4 h-4 text-green-400" />
                <h4 className="text-sm font-semibold text-gray-200">
                  Business Hours
                </h4>
              </div>
              <div className="text-sm text-gray-300 space-y-1">
                <div className="flex justify-between">
                  <span>Monday - Friday:</span>
                  <span className="text-green-400">9:00 AM - 6:00 PM</span>
                </div>
                <div className="flex justify-between">
                  <span>Weekend:</span>
                  <span className="text-yellow-400">By Appointment</span>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Enhanced Quick links */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <h3 className="text-lg font-semibold mb-6 text-white flex items-center gap-2">
              <Globe className="w-5 h-5 text-blue-400" />
              {t("footer.quickLinks") || "Quick Links"}
            </h3>
            <div className="space-y-3">
              {navigationLinks.map((link) => {
                const translationKey = link.name;
                return (
                  <motion.a
                    key={link.href}
                    href={link.href}
                    className="block text-gray-400 hover:text-white transition-all duration-300 hover:translate-x-2 text-sm group"
                    whileHover={{ x: 8 }}
                  >
                    <div className="flex items-center gap-2">
                      <div className="w-1 h-1 bg-blue-400 rounded-full group-hover:bg-white transition-colors"></div>
                      {t(translationKey) || translationKey.split(".")[1]}
                    </div>
                  </motion.a>
                );
              })}
            </div>

            {/* Services */}
            <div className="mt-8">
              <h4 className="text-sm font-semibold text-gray-200 mb-4 uppercase tracking-wider">
                Services
              </h4>
              <div className="space-y-2">
                {serviceLinks.map((service) => (
                  <motion.a
                    key={service.name}
                    href={service.href}
                    className="block text-gray-400 hover:text-white transition-colors duration-300 text-sm"
                    whileHover={{ x: 4 }}
                  >
                    {service.name}
                  </motion.a>
                ))}
              </div>
            </div>
          </motion.div>

          {/* Enhanced Contact info */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
          >
            <h3 className="text-lg font-semibold mb-6 text-white flex items-center gap-2">
              <Mail className="w-5 h-5 text-green-400" />
              {t("Contact") || "Contact"}
            </h3>
            <div className="space-y-4">
              <motion.a
                href="mailto:<EMAIL>"
                className="group flex items-center gap-3 text-gray-400 hover:text-white transition-all duration-300"
                whileHover={{ x: 4 }}
              >
                <div className="p-2 rounded-lg bg-blue-500/10 group-hover:bg-blue-500/20 transition-colors">
                  <Mail className="w-4 h-4 text-blue-400" />
                </div>
                <div>
                  <div className="text-sm font-medium">Email</div>
                  <div className="text-xs text-gray-500">
                    <EMAIL>
                  </div>
                </div>
              </motion.a>

              <motion.a
                href="tel:+491759918357"
                className="group flex items-center gap-3 text-gray-400 hover:text-white transition-all duration-300"
                whileHover={{ x: 4 }}
              >
                <div className="p-2 rounded-lg bg-green-500/10 group-hover:bg-green-500/20 transition-colors">
                  <Phone className="w-4 h-4 text-green-400" />
                </div>
                <div>
                  <div className="text-sm font-medium">Phone</div>
                  <div className="text-xs text-gray-500">+49 175 9918357</div>
                </div>
              </motion.a>

              <div className="flex items-center gap-3 text-gray-400">
                <div className="p-2 rounded-lg bg-purple-500/10">
                  <MapPin className="w-4 h-4 text-purple-400" />
                </div>
                <div>
                  <div className="text-sm font-medium">Location</div>
                  <div className="text-xs text-gray-500">Wyoming, USA</div>
                </div>
              </div>
            </div>

            {/* Call to Action */}
            <div className="mt-8 p-4 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-xl border border-white/10">
              <h4 className="text-sm font-semibold text-white mb-2">
                Ready to Start?
              </h4>
              <p className="text-xs text-gray-400 mb-3">
                Let's discuss your project
              </p>
              <motion.a
                href="#contact"
                className="inline-flex items-center gap-2 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white text-sm font-medium rounded-lg transition-colors"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Calendar className="w-4 h-4" />
                Get Started
              </motion.a>
            </div>
          </motion.div>
        </div>

        {/* Enhanced Bottom bar */}
        <motion.div
          className="mt-16 pt-8 border-t border-white/10"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
        >
          <div className="flex flex-col lg:flex-row justify-between items-center gap-6">
            <div className="flex flex-col md:flex-row items-center gap-4">
              <p className="text-gray-400 text-sm">
                {dictionary.copyright ||
                  `© ${new Date().getFullYear()} Innovatio-Pro. All rights reserved.`}
              </p>

              {/* Privacy & Terms */}
              <div className="flex items-center gap-4 text-sm">
                <Link
                  href={`/${locale}/privacy-policy`}
                  className="text-gray-400 hover:text-white transition-colors"
                >
                  Privacy Policy
                </Link>
                <span className="text-gray-600">•</span>
                <Link
                  href={`/${locale}/terms-and-conditions`}
                  className="text-gray-400 hover:text-white transition-colors"
                >
                  Terms of Service
                </Link>
              </div>
            </div>

            {/* Enhanced Built with note */}
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2 text-gray-400 text-sm">
                <Heart className="w-4 h-4 text-red-400" />
                <span>{t("footer.builtWith") || "Built with"}</span>
                <span className="text-blue-400 font-medium">React</span>
                <span>{t("footer.and") || "&"}</span>
                <span className="text-purple-400 font-medium">Next.js</span>
              </div>

              {/* Scroll to top button */}
              <motion.button
                onClick={scrollToTop}
                className="p-2 rounded-lg bg-white/5 hover:bg-white/10 text-gray-400 hover:text-white transition-all duration-300 border border-white/10 hover:border-white/20"
                whileHover={{ scale: 1.1, y: -2 }}
                whileTap={{ scale: 0.95 }}
                title="Scroll to top"
              >
                <ArrowUp className="w-4 h-4" />
              </motion.button>
            </div>
          </div>

          {/* Achievement badges */}
          <div className="mt-6 flex flex-wrap justify-center gap-4">
            <div className="flex items-center gap-2 px-3 py-1 bg-white/5 rounded-full text-xs text-gray-400">
              <CheckCircle className="w-3 h-3 text-green-400" />
              <span>Privacy Respected</span>
            </div>
            <div className="flex items-center gap-2 px-3 py-1 bg-white/5 rounded-full text-xs text-gray-400">
              <Shield className="w-3 h-3 text-blue-400" />
              <span>Secure Development</span>
            </div>
            <div className="flex items-center gap-2 px-3 py-1 bg-white/5 rounded-full text-xs text-gray-400">
              <Award className="w-3 h-3 text-purple-400" />
              <span>Personal Attention</span>
            </div>
          </div>
        </motion.div>
      </div>
    </footer>
  );
}; 