'use client'

import { useMemo } from "react";
import { motion, useReducedMotion } from "framer-motion";
import { Section } from "@/components/ui/Section";
import { AnimatedTestimonials } from "@/components/ui/animated-testimonials";
import { type Dictionary } from "@/lib/dictionary";
import {
  Star,
  Quote,
  Award,
  Shield,
  Sparkles,
  MessageCircle,
  ThumbsUp,
  Crown,
} from "lucide-react";

interface TestimonialsSectionProps {
  dictionary: Dictionary["testimonials"];
  aboutDictionary?: Dictionary["about"];
}

export const TestimonialsSection = ({
  dictionary,
  aboutDictionary,
}: TestimonialsSectionProps) => {
  const shouldReduceMotion = useReducedMotion();

  const floatingAnimations = useMemo(() => {
    if (shouldReduceMotion) {
      return {
        animate: {},
        transition: {},
      };
    }

    return {
      animate: {
        y: [-5, 5, -5], // Reduced movement
        rotate: [-1, 1, -1], // Reduced rotation
        scale: [1, 1.02, 1], // Reduced scaling
      },
      transition: {
        duration: 8, // Increased duration for smoother animation
        repeat: Infinity,
        ease: "easeInOut",
      },
    };
  }, [shouldReduceMotion]);

  // Optimized background animations
  const backgroundAnimations = useMemo(() => ({
    floatingOrb1: shouldReduceMotion ? {} : {
      scale: [1, 1.1, 1],
      opacity: [0.2, 0.4, 0.2],
      x: [0, 5, 0],
      y: [0, -5, 0],
    },
    floatingOrb2: shouldReduceMotion ? {} : {
      scale: [1.05, 1, 1.05],
      opacity: [0.15, 0.35, 0.15],
      x: [0, -3, 0],
      y: [0, 8, 0],
    }
  }), [shouldReduceMotion]);

  // Memoize static data to prevent re-renders
  const testimonials = useMemo(
    () => [
      {
        quote:
          "Reliable and structured! Viktor Hermann is an extremely dependable and friendly mobile app developer who leaves nothing to be desired in terms of both technical components and user experience!",
        name: "Emin C.",
        designation: "CEO at Ultimind",
        src: "/images/testimonials/emin.jpeg",
        company: "Ultimind",
        industry: "Technology",
        rating: 5,
        projectType: "Mobile App Development",
        deliveryTime: "On time",
        badge: "Verified Client",
      },
      {
        quote:
          "I got to know and appreciate Viktor as a very competent mobile developer in a project. His quick comprehension and high commitment to problem-solving are remarkable. Through him, the team has grown enormously in performance, cohesion with each other, and overall team development.",
        name: "Stefanie W.",
        designation: "Project Manager at Union Investment Real Estate GmbH",
        src: "/images/testimonials/stefanie.jpeg",
        company: "Union Investment Real Estate GmbH",
        industry: "Real Estate",
        rating: 5,
        projectType: "Team Collaboration",
        deliveryTime: "Exceeded expectations",
        badge: "Enterprise Client",
      },
      {
        quote:
          "I'm grateful for the incredible collaboration we've had on the mobile application project. His expertise in front-end development has been truly invaluable, and I'm thrilled with the outstanding results we've achieved together.",
        name: "Mohammed S.",
        designation: "Product UX Designer at Togg",
        src: "/images/testimonials/mohammed.jpeg",
        company: "Togg",
        industry: "Automotive",
        rating: 5,
        projectType: "UI/UX Development",
        deliveryTime: "Exceptional quality",
        badge: "Premium Partner",
      },
      {
        quote:
          "As an entrepreneur in the real estate industry, I was looking for professional support in portal, website, and app-based solutions. Viktor was able to explain complex connections in simple terms and immediately understood my objectives.",
        name: "Natalia W.",
        designation: "Global Real Estate Expert at Walenwein Immobilien",
        src: "/images/testimonials/natalia.jpeg",
        company: "Walenwein Immobilien",
        industry: "Real Estate",
        rating: 5,
        projectType: "Full-Stack Development",
        deliveryTime: "Perfect timeline",
        badge: "Long-term Partner",
      },
    ],
    []
  );

  // Enhanced testimonial stats
  const testimonialStats = useMemo(
    () => [
      {
        icon: <Star className="w-5 h-5" />,
        value: "5.0/5",
        label: "Client Reviews",
        color: "from-yellow-500 to-orange-600",
      },
      {
        icon: <Shield className="w-5 h-5" />,
        value: "NDA",
        label: "Protected",
        color: "from-blue-500 to-indigo-600",
      },
      {
        icon: <Award className="w-5 h-5" />,
        value: "100%",
        label: "Quality Focus",
        color: "from-emerald-500 to-teal-600",
      },
      {
        icon: <Crown className="w-5 h-5" />,
        value: "Premium",
        label: "Services",
        color: "from-purple-500 to-pink-600",
      },
    ],
    []
  );



  // Optimize animation values based on reduce motion preference
  const animationConfig = useMemo(
    () => ({
      duration: shouldReduceMotion ? 0.3 : 8,
      repeat: shouldReduceMotion ? 0 : Infinity,
      scale: shouldReduceMotion ? [1] : [1, 1.3, 1],
      opacity: shouldReduceMotion ? [0.5] : [0.3, 0.6, 0.3],
    }),
    [shouldReduceMotion]
  );

  return (
    <Section
      titleClassName="text-gray-900 dark:text-white text-center mb-2"
      subtitleClassName="text-primary dark:text-blue-400 text-center mb-12"
      title={dictionary?.title ?? "What Our Clients Say"}
      subtitle={dictionary?.subtitle ?? "Trusted by businesses worldwide"}
      className="py-20 md:py-28 bg-gradient-to-br from-gray-50 via-white to-blue-50/30 dark:from-blue-900 dark:via-gray-800 dark:to-gray-900 relative w-full overflow-hidden"
      containerClassName="max-w-full px-4 md:px-8 lg:px-12 relative z-10"
    >
      {/* Enhanced Premium Background Effects - Reduced motion friendly */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50/30 via-transparent to-purple-50/30 dark:from-blue-900/20 dark:via-transparent dark:to-purple-900/20"></div>

      {/* Floating Orb Elements - Conditional animation */}
      {!shouldReduceMotion && (
        <>
          <motion.div
            className="absolute top-20 left-12 w-40 h-40 bg-gradient-radial from-blue-400/20 via-indigo-400/10 to-transparent rounded-full blur-3xl"
            animate={{
              scale: animationConfig.scale,
              opacity: animationConfig.opacity,
              x: [0, 30, 0],
              y: [0, -20, 0],
            }}
            transition={{
              duration: 10,
              repeat: animationConfig.repeat,
              ease: "easeInOut",
            }}
          />
          <motion.div
            className="absolute top-32 right-16 w-52 h-52 bg-gradient-radial from-purple-400/15 via-pink-400/8 to-transparent rounded-full blur-3xl"
            animate={{
              scale: [1.2, 1, 1.2],
              opacity: animationConfig.opacity,
              x: [0, -25, 0],
              y: [0, 25, 0],
            }}
            transition={{
              duration: 12,
              repeat: animationConfig.repeat,
              ease: "easeInOut",
              delay: 3,
            }}
          />
        </>
      )}

  
      {/* Floating Sparkle Elements - Conditional animation */}
      {!shouldReduceMotion && (
        <>
          <motion.div
            className="absolute top-40 left-1/5 text-blue-400/30 dark:text-blue-300/20"
            animate={{
              rotate: [0, 360],
              scale: animationConfig.scale,
              opacity: animationConfig.opacity,
            }}
            transition={{
              duration: animationConfig.duration,
              repeat: animationConfig.repeat,
              ease: "easeInOut",
            }}
          >
            <Sparkles className="w-8 h-8" />
          </motion.div>
          <motion.div
            className="absolute bottom-32 right-1/4 text-purple-400/25 dark:text-purple-300/15"
            animate={{
              rotate: [360, 0],
              scale: [1.2, 1, 1.2],
              opacity: animationConfig.opacity,
            }}
            transition={{
              duration: 10,
              repeat: animationConfig.repeat,
              ease: "easeInOut",
              delay: 4,
            }}
          >
            <Sparkles className="w-10 h-10" />
          </motion.div>
        </>
      )}

      {/* Testimonial Statistics Bar */}
      <motion.div
        className="mb-16 flex flex-wrap justify-center items-center gap-6 md:gap-10 p-6 rounded-2xl bg-white/70 dark:bg-gray-800/70 backdrop-blur-xl border border-white/30 dark:border-gray-700/30 shadow-lg max-w-5xl mx-auto"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        {testimonialStats.map((stat, index) => (
          <div key={index} className="flex items-center gap-3 text-center">
            <div
              className={`w-10 h-10 rounded-full bg-gradient-to-br ${stat.color} flex items-center justify-center text-white`}
            >
              {stat.icon}
            </div>
            <div>
              <div className="text-xl font-bold text-gray-800 dark:text-gray-100">
                {stat.value}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                {stat.label}
              </div>
            </div>
          </div>
        ))}
      </motion.div>



      {/* Quote decoration */}
      <motion.div
        className="absolute top-1/3 left-8 opacity-10 dark:opacity-5"
        animate={{
          rotate: [0, 5, -5, 0],
          scale: [1, 1.1, 1],
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      >
        <Quote className="w-32 h-32 text-blue-500" />
      </motion.div>
      <motion.div
        className="absolute bottom-1/4 right-8 opacity-10 dark:opacity-5"
        animate={{
          rotate: [0, -5, 5, 0],
          scale: [1, 1.1, 1],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 2,
        }}
      >
        <MessageCircle className="w-24 h-24 text-purple-500" />
      </motion.div>

      {/* Enhanced Main content */}
      <motion.div
        className="w-full max-w-[1600px] mx-auto"
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.4 }}
      >
        <AnimatedTestimonials testimonials={testimonials} autoplay={true} />
      </motion.div>

      {/* Enhanced Client Showcase Section */}
      <motion.div
        className="mt-20 max-w-6xl mx-auto"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8, duration: 0.6 }}
      >
        <div className="text-center mb-12">
          <h3 className="text-2xl font-bold text-gray-800 dark:text-gray-200 mb-4">
            Trusted by Industry Leaders
          </h3>
          <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
            From startups to enterprise companies, we've helped businesses
            achieve their digital transformation goals.
          </p>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.9 + index * 0.1 }}
              className="group text-center"
            >
              <div className="relative mb-4 mx-auto w-20 h-20">
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full opacity-20 group-hover:opacity-30 transition-opacity"></div>
                <div className="relative w-full h-full bg-white dark:bg-gray-800 rounded-full flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 border-2 border-gray-100 dark:border-gray-700">
                  <span className="text-2xl font-bold text-gray-700 dark:text-gray-300">
                    {testimonial.company.charAt(0)}
                  </span>
                </div>
              </div>
              <h4 className="font-semibold text-gray-800 dark:text-gray-200 mb-1 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                {testimonial.company}
              </h4>
              <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">
                {testimonial.industry}
              </p>
              <div className="flex justify-center items-center gap-1">
                {[...Array(5)].map((_, starIndex) => (
                  <Star
                    key={starIndex}
                    className="w-4 h-4 fill-yellow-400 text-yellow-400"
                  />
                ))}
              </div>
              <div className="mt-2">
                <span className="inline-block px-3 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 rounded-full text-xs font-medium">
                  {testimonial.badge}
                </span>
              </div>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Call to Action Section */}
      <motion.div
        className="mt-20 max-w-4xl mx-auto text-center"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1.2, duration: 0.6 }}
      >
        <div className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-2xl p-8 border border-blue-200/50 dark:border-blue-800/30 relative overflow-hidden">
          {/* Background decoration */}
          <div className="absolute inset-0 bg-gradient-to-tr from-blue-500/5 to-purple-500/5 rounded-2xl"></div>

          <div className="relative z-10">
            <div className="flex items-center justify-center gap-3 mb-6">
              <div className="w-12 h-12 rounded-full bg-blue-500 flex items-center justify-center">
                <ThumbsUp className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-gray-800 dark:text-gray-200">
                Ready to Join Our Success Stories?
              </h3>
            </div>
            <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-2xl mx-auto text-lg">
              Join the growing list of satisfied clients who trust us with their
              digital transformation journey.
            </p>
            <motion.a
              href="#contact"
              className="inline-flex items-center gap-2 px-8 py-4 bg-blue-500 hover:bg-blue-600 text-white font-semibold rounded-2xl transition-all duration-300 shadow-lg hover:shadow-xl relative overflow-hidden group"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              {/* Animated background glow */}
              <div className="absolute inset-0 bg-gradient-to-r from-blue-400/0 via-blue-400/20 to-blue-400/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000"></div>

              <MessageCircle className="w-5 h-5 relative z-10" />
              <span className="relative z-10">Start Your Project Today</span>
            </motion.a>
          </div>
        </div>
      </motion.div>
    </Section>
  );
};
