'use client'

import { useState, useMemo, useCallback, memo, useEffect } from "react";
import { motion, AnimatePresence, useReducedMotion } from "framer-motion";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { Section } from "@/components/ui/Section";
import { Button } from "@/components/ui/Button";
import { useI18n } from "@/providers/I18nProvider";
import { type Dictionary } from "@/lib/dictionary";
import {
  SERVICES,
  BUDGET_RANGES,
  TIMELINE_OPTIONS,
  SOURCE_OPTIONS,
  formatPrice,
  getServicePrice,
  getServiceById,
} from "@/lib/config/services";
import { EnhancedContactFormData } from "@/types/proposal";
import {
  Mail,
  Phone,
  Calendar,
  MapPin,
  Send,
  CheckCircle,
  XCircle,
  Clock,
  Shield,
  Zap,
  Star,
  Award,
  Verified,
  TrendingUp,
  Users,
  Globe,
  Sparkles,
  MessageCircle,
  Headphones,
  ArrowRight,
  Calculator,
  Building,
  DollarSign,
  Package,
  ChevronDown,
} from "lucide-react";

// Contact details from Flutter code - memoized
const contactInfo = {
  email: "<EMAIL>",
  phone: "+49 175 9918357",
  location: "Wyoming",
  calendlyLink: "https://calendly.com/v-hermann-it",
};

interface ContactSectionProps {
  dictionary: Dictionary["contact"] & {
    location?: string;
  };
  aboutDictionary?: Dictionary["about"];
}

// Memoized contact method card component
const ContactMethodCard = memo(
  ({
    method,
    index,
    hoveredContact,
    setHoveredContact,
  }: {
    method: any;
    index: number;
    hoveredContact: string | null;
    setHoveredContact: (id: string | null) => void;
  }) => {
    const handleMouseEnter = useCallback(() => {
      setHoveredContact(method.id);
    }, [method.id, setHoveredContact]);

    const handleMouseLeave = useCallback(() => {
      setHoveredContact(null);
    }, [setHoveredContact]);

    return (
      <motion.a
        key={method.id}
        href={method.action}
        target={
          method.id === "calendar" || method.id === "website"
            ? "_blank"
            : method.id === "location"
              ? "_self"
              : undefined
        }
        rel={
          method.id === "calendar" || method.id === "website"
            ? "noopener noreferrer"
            : undefined
        }
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        className="group block"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: index * 0.1 }}
      >
        <div className="relative p-6 rounded-2xl transition-all duration-300 hover:shadow-xl bg-white/60 dark:bg-gray-800/60 backdrop-blur-xl border border-white/30 dark:border-gray-700/30 hover:border-blue-300/50 dark:hover:border-blue-500/30 h-full">
          {/* Trust badge */}
          <div className="absolute top-4 right-4">
            <div
              className={`px-3 py-1 rounded-full text-xs font-semibold shadow-md bg-gradient-to-r ${method.gradient} text-white border border-white/20`}
            >
              {method.trustBadge}
            </div>
          </div>

          <div className="flex flex-col items-start gap-4 relative z-10 h-full">
            <div
              className={`p-4 rounded-xl transition-all duration-300 bg-gradient-to-r ${method.gradient} shadow-lg`}
            >
              <div className="text-white">{method.icon}</div>
            </div>
            <div className="flex-1 w-full">
              <h4 className="text-xl font-bold text-gray-900 dark:text-white mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                {method.title}
              </h4>
              <p className="text-gray-700 dark:text-gray-300 font-medium mb-2">
                {method.value}
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400 mb-1">
                {method.description}
              </p>
              <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                <Clock className="w-3 h-3 mr-1" />
                <span>{method.availability}</span>
              </div>
            </div>
            <div className="text-gray-400 dark:text-gray-500 group-hover:text-blue-500 dark:group-hover:text-blue-400 transition-all self-end">
              <ArrowRight className="w-5 h-5" />
            </div>
          </div>
        </div>
      </motion.a>
    );
  }
);

ContactMethodCard.displayName = "ContactMethodCard";

// Memoized WhatsApp quick action component
const WhatsAppQuickAction = memo(
  ({
    href,
    children,
    icon: Icon,
  }: {
    href: string;
    children: React.ReactNode;
    icon: any;
  }) => (
    <a
      href={href}
      target="_blank"
      rel="noopener noreferrer"
      className="flex items-center justify-between p-4 bg-white/60 dark:bg-gray-800/60 rounded-xl border border-green-200/50 dark:border-green-800/30 hover:shadow-md transition-all duration-300 group"
    >
      <div className="flex items-center gap-3">
        <div className="w-10 h-10 rounded-full bg-gradient-to-r from-green-500 to-emerald-600 flex items-center justify-center">
          <Icon className="w-5 h-5 text-white" />
        </div>
        <div>{children}</div>
      </div>
      <ArrowRight className="w-5 h-5 text-green-600 dark:text-green-400 group-hover:translate-x-1 transition-transform" />
    </a>
  )
);

WhatsAppQuickAction.displayName = "WhatsAppQuickAction";

export const ContactSection = ({
  dictionary,
  aboutDictionary,
}: ContactSectionProps) => {
  const [formData, setFormData] = useState<EnhancedContactFormData>({
    name: "",
    email: "",
    companyName: "",
    phone: "",
    message: "",
    selectedService: "",
    estimatedBudget: "",
    projectTimeline: "",
    heardAbout: "",
  });

  const [formStatus, setFormStatus] = useState<
    "idle" | "submitting" | "success" | "error"
  >("idle");
  const [hoveredContact, setHoveredContact] = useState<string | null>(null);
  const [focusedField, setFocusedField] = useState<string | null>(null);
  const [showServiceDetails, setShowServiceDetails] = useState(false);
  const { t, dir } = useI18n();
  const isRtl = dir === "rtl";

  const shouldReduceMotion = useReducedMotion();

  // Effect to handle service selection from URL parameters and custom events
  useEffect(() => {
    // Only run on client side to avoid hydration mismatch
    if (typeof window === 'undefined') return;
    
    // Check URL parameters on component mount
    const urlParams = new URLSearchParams(window.location.search);
    const serviceFromUrl = urlParams.get("service");
    if (
      serviceFromUrl &&
      Object.values(SERVICES).some((s) => s.id === serviceFromUrl)
    ) {
      setFormData((prev) => ({ ...prev, selectedService: serviceFromUrl }));
    }

    // Listen for custom service selection events
    const handleServiceSelection = (event: CustomEvent) => {
      const { serviceId } = event.detail;
      if (
        serviceId &&
        Object.values(SERVICES).some((s) => s.id === serviceId)
      ) {
        setFormData((prev) => ({ ...prev, selectedService: serviceId }));
      }
    };

    window.addEventListener(
      "selectService",
      handleServiceSelection as EventListener
    );

    return () => {
      window.removeEventListener(
        "selectService",
        handleServiceSelection as EventListener
      );
    };
  }, []);

  // Memoized input change handler
  const handleInputChange = useCallback(
    (
      e: React.ChangeEvent<
        HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
      >
    ) => {
      const { name, value } = e.target;
      setFormData((prev) => ({
        ...prev,
        [name]: value,
      }));
    },
    []
  );

  // Memoized submit handler
  const handleSubmit = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault();
      setFormStatus("submitting");

      try {
        const response = await fetch("/api/contact/enhanced", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(formData),
        });

        if (response.ok) {
          setFormData({
            name: "",
            email: "",
            companyName: "",
            phone: "",
            message: "",
            selectedService: "",
            estimatedBudget: "",
            projectTimeline: "",
            heardAbout: "",
          });
          setFormStatus("success");
          setTimeout(() => {
            setFormStatus("idle");
          }, 4000);
        } else {
          throw new Error("Failed to send email");
        }
      } catch (error) {
        console.error("Error sending email:", error);
        setFormStatus("error");
        setTimeout(() => {
          setFormStatus("idle");
        }, 4000);
      }
    },
    [formData]
  );

  // Memoized enhanced contact methods with trust indicators and modern icons
  const contactMethods = useMemo(
    () => [
      {
        id: "email",
        icon: <Mail className="w-6 h-6" />,
        title: dictionary?.email || "Email",
        value: contactInfo.email,
        action: `mailto:${contactInfo.email}`,
        color: "#3B82F6",
        gradient: "from-blue-500 to-indigo-600",
        description: "Quick response within 4 hours",
        trustBadge: "✉️ 4h Response",
        availability: "24/7",
      },
      {
        id: "phone",
        icon: <Phone className="w-6 h-6" />,
        title: dictionary?.phone || "Phone",
        value: contactInfo.phone,
        action: `tel:${contactInfo.phone}`,
        color: "#10B981",
        gradient: "from-emerald-500 to-teal-600",
        description: "Direct line for urgent matters",
        trustBadge: "📞 Direct Line",
        availability: "Mon-Fri 9-18",
      },
      {
        id: "calendar",
        icon: <Calendar className="w-6 h-6" />,
        title: "Schedule a Call",
        value: "Book a free 15-min consultation",
        action: contactInfo.calendlyLink,
        color: "#8B5CF6",
        gradient: "from-violet-500 to-purple-600",
        description: "Free consultation call",
        trustBadge: "🎯 Free Consultation",
        availability: "Flexible",
      },
      {
        id: "location",
        icon: <MapPin className="w-6 h-6" />,
        title: dictionary?.location || "Location",
        value: contactInfo.location,
        action: "#",
        color: "#F59E0B",
        gradient: "from-amber-500 to-orange-600",
        description: "Remote work, global reach",
        trustBadge: "🌍 Global Service",
        availability: "Worldwide",
      },
    ],
    [dictionary?.email, dictionary?.phone, dictionary?.location]
  );

  // Memoized trust indicators for footer
  const trustIndicators = useMemo(
    () => [
      {
        icon: <Clock className="w-6 h-6" />,
        value: "4h",
        label: "Response Time",
        color: "from-blue-500 to-indigo-600",
      },
      {
        icon: <Verified className="w-6 h-6" />,
        value: "100%",
        label: "Secure & Private",
        color: "from-emerald-500 to-teal-600",
      },
      {
        icon: <MessageCircle className="w-6 h-6" />,
        value: "150+",
        label: "Messages Handled",
        color: "from-purple-500 to-pink-600",
      },
      {
        icon: <Headphones className="w-6 h-6" />,
        value: "24/7",
        label: "Available",
        color: "from-orange-500 to-amber-600",
      },
    ],
    []
  );

  return (
    <Section
      id="contact"
      title={dictionary?.title || "Contact Us"}
      subtitle={"Let's start shaping the future"}
      titleClassName="text-gray-900 dark:text-white text-center"
      subtitleClassName="text-primary dark:text-blue-400 text-center mb-12"
      className={`py-20 md:py-28 bg-gradient-to-br from-gray-50 via-white to-blue-50/30 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 relative w-full ${
        isRtl ? "rtl-section" : ""
      }`}
    >
      {/* Simplified Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50/30 via-transparent to-purple-50/30 dark:from-blue-900/20 dark:via-transparent dark:to-purple-900/20"></div>

      {/* Trust Statistics Bar - Horizontal Auto Scrolling */}
      <motion.div
        className="mb-16 relative overflow-hidden rounded-2xl bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl border border-white/30 dark:border-gray-700/30 shadow-xl max-w-5xl mx-auto"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        <div className="flex animate-scroll-horizontal gap-8 py-6 px-4">
          {/* First set of indicators */}
          {trustIndicators.map((indicator, index) => (
            <div key={`first-${index}`} className="flex items-center gap-3 text-center min-w-fit whitespace-nowrap">
              <div
                className={`w-12 h-12 rounded-full bg-gradient-to-br ${indicator.color} flex items-center justify-center shadow-lg`}
              >
                {indicator.icon}
              </div>
              <div>
                <div className="text-2xl font-bold text-gray-800 dark:text-gray-100">
                  {indicator.value}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  {indicator.label}
                </div>
              </div>
            </div>
          ))}
          {/* Duplicate set for seamless scrolling */}
          {trustIndicators.map((indicator, index) => (
            <div key={`second-${index}`} className="flex items-center gap-3 text-center min-w-fit whitespace-nowrap">
              <div
                className={`w-12 h-12 rounded-full bg-gradient-to-br ${indicator.color} flex items-center justify-center shadow-lg`}
              >
                {indicator.icon}
              </div>
              <div>
                <div className="text-2xl font-bold text-gray-800 dark:text-gray-100">
                  {indicator.value}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  {indicator.label}
                </div>
              </div>
            </div>
          ))}
        </div>
      </motion.div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Enhanced Hero Image */}
        <motion.div
          className="mb-12 rounded-2xl overflow-hidden shadow-2xl relative bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-gray-800 to-gray-700 border border-white/20 dark:border-gray-600/30"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <div className="relative h-80 w-full">
            <Image
              src="/images/connect.jpg"
              alt="Shaping the future together"
              fill
              className="object-cover rounded-2xl"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 70vw"
              loading="lazy"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-black/20 to-transparent z-20"></div>

            {/* Overlay content */}
            <div className="absolute bottom-0 left-0 right-0 p-8 z-30 text-white">
              <motion.h3
                className="text-2xl md:text-3xl font-bold mb-2 drop-shadow-lg"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5, duration: 0.6 }}
              >
                Ready to Start Your Project?
              </motion.h3>
              <motion.p
                className="text-white/90 text-lg drop-shadow-md"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.7, duration: 0.6 }}
              >
                Let's discuss how we can bring your vision to life
              </motion.p>
            </div>

            {/* Trust indicators overlay */}
            <div className="absolute top-4 right-4 flex gap-2 z-30">
              <div className="bg-green-500/90 text-white px-3 py-1 rounded-full text-xs font-semibold backdrop-blur-sm">
                ✓ Available Now
              </div>
              <div className="bg-blue-500/90 text-white px-3 py-1 rounded-full text-xs font-semibold backdrop-blur-sm">
                🚀 Fast Response
              </div>
            </div>
          </div>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 lg:gap-16">
          {/* Enhanced Contact Methods */}
          <motion.div
            className="relative order-2 md:order-1"
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h3 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center md:text-right">
              {dictionary?.contactInfo || "Get In Touch"}
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {contactMethods.map((method, index) => (
                <ContactMethodCard
                  key={method.id}
                  method={method}
                  index={index}
                  hoveredContact={hoveredContact}
                  setHoveredContact={setHoveredContact}
                />
              ))}
            </div>

            {/* WhatsApp Quick Contact - Full Width */}
            <motion.div
              className="mt-8 p-6 rounded-2xl bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border border-green-200/50 dark:border-green-800/30 relative"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.9, duration: 0.6 }}
            >
              <div className="absolute top-4 right-4">
                <div className="w-8 h-8 rounded-full bg-green-500 flex items-center justify-center">
                  <MessageCircle className="w-4 h-4 text-white" />
                </div>
              </div>

              <h4 className="font-bold text-gray-800 dark:text-gray-200 mb-3 text-lg">
                Quick WhatsApp Contact
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                Get instant replies and quick project discussions via WhatsApp.
              </p>

              <div className="grid grid-cols-1 gap-3">
                <WhatsAppQuickAction
                  href="https://wa.me/491759918357?text=Hi! I'm interested in discussing a new project with you. Could you please share more details about your services and availability?"
                  icon={MessageCircle}
                >
                  <div className="font-semibold text-gray-800 dark:text-gray-200">
                    New Project Inquiry
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    Discuss your project requirements
                  </div>
                </WhatsAppQuickAction>

                <WhatsAppQuickAction
                  href="https://wa.me/491759918357?text=Hello! I'd like to get a quote for my project. Could you please send me information about your pricing and process?"
                  icon={Calculator}
                >
                  <div className="font-semibold text-gray-800 dark:text-gray-200">
                    Get Quote
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    Request pricing information
                  </div>
                </WhatsAppQuickAction>

                <WhatsAppQuickAction
                  href="https://wa.me/491759918357?text=Hi! I have some technical questions about development. Could we have a quick chat about my requirements?"
                  icon={Headphones}
                >
                  <div className="font-semibold text-gray-800 dark:text-gray-200">
                    Technical Questions
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    Quick technical consultation
                  </div>
                </WhatsAppQuickAction>
              </div>
            </motion.div>
          </motion.div>

          {/* Enhanced Contact Form */}
          <motion.div
            className="order-1 md:order-2"
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <form
              onSubmit={handleSubmit}
              className="relative bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl p-8 rounded-3xl shadow-2xl border border-white/30 dark:border-gray-700/30"
              suppressHydrationWarning
            >
              <div className="relative z-10">
                <h3 className="text-3xl font-bold text-gray-900 dark:text-white mb-2 text-center">
                  {dictionary?.writeUs || "Send a Message"}
                </h3>
                <p className="text-gray-600 dark:text-gray-400 text-center mb-8">
                  Fill out the form below and I'll get back to you shortly
                </p>

                <div className="space-y-6">
                  <div>
                    <label
                      htmlFor="name"
                      className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2"
                    >
                      {dictionary?.name || "Name"}
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      onFocus={() => setFocusedField("name")}
                      onBlur={() => setFocusedField(null)}
                      required
                      className={`w-full px-4 py-3 rounded-xl border-2 transition-all duration-300 bg-white/60 dark:bg-gray-700/60 backdrop-blur-sm text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 ${
                        focusedField === "name"
                          ? "border-blue-500 dark:border-blue-400 ring-4 ring-blue-500/20 dark:ring-blue-400/20 shadow-lg"
                          : "border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"
                      }`}
                      placeholder={dictionary?.yourName || "Your Name"}
                      disabled={formStatus === "submitting"}
                    />
                  </div>

                  <div>
                    <label
                      htmlFor="email"
                      className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2"
                    >
                      {dictionary?.email || "Email"}
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      onFocus={() => setFocusedField("email")}
                      onBlur={() => setFocusedField(null)}
                      required
                      className={`w-full px-4 py-3 rounded-xl border-2 transition-all duration-300 bg-white/60 dark:bg-gray-700/60 backdrop-blur-sm text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 ${
                        focusedField === "email"
                          ? "border-blue-500 dark:border-blue-400 ring-4 ring-blue-500/20 dark:ring-blue-400/20 shadow-lg"
                          : "border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"
                      }`}
                      placeholder={dictionary?.yourEmail || "Your Email"}
                      disabled={formStatus === "submitting"}
                    />
                  </div>

                  {/* Service Selection */}
                  <div>
                    <label
                      htmlFor="selectedService"
                      className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2"
                    >
                      <Package className="w-4 h-4 inline mr-1" />
                      Interested Service
                    </label>
                    <div className="relative">
                      <select
                        id="selectedService"
                        name="selectedService"
                        value={formData.selectedService}
                        onChange={handleInputChange}
                        onFocus={() => setFocusedField("selectedService")}
                        onBlur={() => setFocusedField(null)}
                        className={`w-full px-4 py-3 rounded-xl border-2 transition-all duration-300 bg-white/60 dark:bg-gray-700/60 backdrop-blur-sm text-gray-900 dark:text-white ${
                          focusedField === "selectedService"
                            ? "border-blue-500 dark:border-blue-400 ring-4 ring-blue-500/20 dark:ring-blue-400/20 shadow-lg"
                            : "border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"
                        } appearance-none cursor-pointer`}
                        disabled={formStatus === "submitting"}
                      >
                        <option value="">Select a service...</option>
                        {Object.values(SERVICES).map((service) => (
                          <option key={service.id} value={service.id}>
                            {service.name} -{" "}
                            {formatPrice(getServicePrice(service.id))}
                            {service.id === "consulting" ? "/hour" : ""}
                          </option>
                        ))}
                      </select>
                      <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 pointer-events-none" />
                    </div>
                    {formData.selectedService && (
                      <div className="mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800/30">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-blue-900 dark:text-blue-100">
                            {getServiceById(formData.selectedService)?.name}
                          </span>
                          <span className="text-sm font-bold text-blue-600 dark:text-blue-400">
                            {formatPrice(
                              getServicePrice(formData.selectedService)
                            )}
                            {formData.selectedService === "consulting"
                              ? "/hour"
                              : ""}
                          </span>
                        </div>
                        <p className="text-xs text-blue-700 dark:text-blue-300 mt-1">
                          {
                            getServiceById(formData.selectedService)
                              ?.description
                          }
                        </p>
                      </div>
                    )}
                  </div>

                  {/* Company */}
                  <div>
                    <label
                      htmlFor="companyName"
                      className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2"
                    >
                      <Building className="w-4 h-4 inline mr-1" />
                      Company (Optional)
                    </label>
                    <input
                      type="text"
                      id="companyName"
                      name="companyName"
                      value={formData.companyName}
                      onChange={handleInputChange}
                      onFocus={() => setFocusedField("companyName")}
                      onBlur={() => setFocusedField(null)}
                      className={`w-full px-4 py-3 rounded-xl border-2 transition-all duration-300 bg-white/60 dark:bg-gray-700/60 backdrop-blur-sm text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 ${
                        focusedField === "companyName"
                          ? "border-blue-500 dark:border-blue-400 ring-4 ring-blue-500/20 dark:ring-blue-400/20 shadow-lg"
                          : "border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"
                      }`}
                      placeholder="Your Company Name"
                      disabled={formStatus === "submitting"}
                    />
                  </div>

                  {/* Phone */}
                  <div>
                    <label
                      htmlFor="phone"
                      className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2"
                    >
                      <Phone className="w-4 h-4 inline mr-1" />
                      Phone (Optional)
                    </label>
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      onFocus={() => setFocusedField("phone")}
                      onBlur={() => setFocusedField(null)}
                      className={`w-full px-4 py-3 rounded-xl border-2 transition-all duration-300 bg-white/60 dark:bg-gray-700/60 backdrop-blur-sm text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 ${
                        focusedField === "phone"
                          ? "border-blue-500 dark:border-blue-400 ring-4 ring-blue-500/20 dark:ring-blue-400/20 shadow-lg"
                          : "border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"
                      }`}
                      placeholder="+49 ************"
                      disabled={formStatus === "submitting"}
                    />
                  </div>

                  {/* Budget Range */}
                  <div>
                    <label
                      htmlFor="estimatedBudget"
                      className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2"
                    >
                      <DollarSign className="w-4 h-4 inline mr-1" />
                      Estimated Budget
                    </label>
                    <div className="relative">
                      <select
                        id="estimatedBudget"
                        name="estimatedBudget"
                        value={formData.estimatedBudget}
                        onChange={handleInputChange}
                        onFocus={() => setFocusedField("estimatedBudget")}
                        onBlur={() => setFocusedField(null)}
                        className={`w-full px-4 py-3 rounded-xl border-2 transition-all duration-300 bg-white/60 dark:bg-gray-700/60 backdrop-blur-sm text-gray-900 dark:text-white ${
                          focusedField === "estimatedBudget"
                            ? "border-blue-500 dark:border-blue-400 ring-4 ring-blue-500/20 dark:ring-blue-400/20 shadow-lg"
                            : "border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"
                        } appearance-none cursor-pointer`}
                        disabled={formStatus === "submitting"}
                      >
                        <option value="">Select budget range...</option>
                        {BUDGET_RANGES.map((range) => (
                          <option key={range.value} value={range.value}>
                            {range.label}
                          </option>
                        ))}
                      </select>
                      <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 pointer-events-none" />
                    </div>
                  </div>

                  {/* Timeline */}
                  <div>
                    <label
                      htmlFor="projectTimeline"
                      className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2"
                    >
                      <Clock className="w-4 h-4 inline mr-1" />
                      Project Timeline
                    </label>
                    <div className="relative">
                      <select
                        id="projectTimeline"
                        name="projectTimeline"
                        value={formData.projectTimeline}
                        onChange={handleInputChange}
                        onFocus={() => setFocusedField("projectTimeline")}
                        onBlur={() => setFocusedField(null)}
                        className={`w-full px-4 py-3 rounded-xl border-2 transition-all duration-300 bg-white/60 dark:bg-gray-700/60 backdrop-blur-sm text-gray-900 dark:text-white ${
                          focusedField === "projectTimeline"
                            ? "border-blue-500 dark:border-blue-400 ring-4 ring-blue-500/20 dark:ring-blue-400/20 shadow-lg"
                            : "border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"
                        } appearance-none cursor-pointer`}
                        disabled={formStatus === "submitting"}
                      >
                        <option value="">Select timeline...</option>
                        {TIMELINE_OPTIONS.map((option) => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </select>
                      <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 pointer-events-none" />
                    </div>
                  </div>

                  {/* How did you hear about us */}
                  <div>
                    <label
                      htmlFor="source"
                      className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2"
                    >
                      <Globe className="w-4 h-4 inline mr-1" />
                      How did you hear about us?
                    </label>
                    <div className="relative">
                      <select
                        id="heardAbout"
                        name="heardAbout"
                        value={formData.heardAbout}
                        onChange={handleInputChange}
                        onFocus={() => setFocusedField("heardAbout")}
                        onBlur={() => setFocusedField(null)}
                        className={`w-full px-4 py-3 rounded-xl border-2 transition-all duration-300 bg-white/60 dark:bg-gray-700/60 backdrop-blur-sm text-gray-900 dark:text-white ${
                          focusedField === "heardAbout"
                            ? "border-blue-500 dark:border-blue-400 ring-4 ring-blue-500/20 dark:ring-blue-400/20 shadow-lg"
                            : "border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"
                        } appearance-none cursor-pointer`}
                        disabled={formStatus === "submitting"}
                      >
                        <option value="">Select source...</option>
                        {SOURCE_OPTIONS.map((option) => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </select>
                      <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 pointer-events-none" />
                    </div>
                  </div>

                  <div>
                    <label
                      htmlFor="message"
                      className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2"
                    >
                      <MessageCircle className="w-4 h-4 inline mr-1" />
                      Project Description *
                    </label>
                    <textarea
                      id="message"
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      onFocus={() => setFocusedField("message")}
                      onBlur={() => setFocusedField(null)}
                      required
                      rows={6}
                      className={`w-full px-4 py-3 rounded-xl border-2 transition-all duration-300 bg-white/60 dark:bg-gray-700/60 backdrop-blur-sm text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 resize-none ${
                        focusedField === "message"
                          ? "border-blue-500 dark:border-blue-400 ring-4 ring-blue-500/20 dark:ring-blue-400/20 shadow-lg"
                          : "border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"
                      }`}
                      placeholder="Tell me about your project, goals, requirements, and any specific features you have in mind..."
                      disabled={formStatus === "submitting"}
                    />
                    <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      The more details you provide, the better I can understand
                      your needs and provide an accurate proposal.
                    </div>
                  </div>

                  <div>
                    <button
                      type="submit"
                      className={`w-full py-4 px-6 rounded-xl font-semibold transition-all duration-300 flex items-center justify-center relative overflow-hidden group ${
                        formStatus === "success"
                          ? "bg-green-500 hover:bg-green-600"
                          : formStatus === "error"
                            ? "bg-red-500 hover:bg-red-600"
                            : "bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700"
                      } text-white shadow-lg hover:shadow-xl disabled:opacity-70 disabled:cursor-not-allowed`}
                      disabled={formStatus === "submitting"}
                    >
                      <AnimatePresence mode="wait">
                        {formStatus === "submitting" ? (
                          <motion.div
                            key="submitting"
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            exit={{ opacity: 0 }}
                            className="flex items-center"
                          >
                            <motion.div
                              animate={{ rotate: 360 }}
                              transition={{
                                duration: 1,
                                repeat: Infinity,
                                ease: "linear",
                              }}
                              className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full mr-3"
                            />
                            {dictionary?.sendingMessage || "Sending Message..."}
                          </motion.div>
                        ) : formStatus === "success" ? (
                          <motion.div
                            key="success"
                            initial={{ opacity: 0, scale: 0.8 }}
                            animate={{ opacity: 1, scale: 1 }}
                            exit={{ opacity: 0 }}
                            className="flex items-center"
                          >
                            <CheckCircle className="w-5 h-5 mr-3" />
                            {dictionary?.messageSent ||
                              "Message Sent Successfully!"}
                          </motion.div>
                        ) : formStatus === "error" ? (
                          <motion.div
                            key="error"
                            initial={{ opacity: 0, scale: 0.8 }}
                            animate={{ opacity: 1, scale: 1 }}
                            exit={{ opacity: 0 }}
                            className="flex items-center"
                          >
                            <XCircle className="w-5 h-5 mr-3" />
                            {dictionary?.messageFailed ||
                              "Failed to Send. Try Again"}
                          </motion.div>
                        ) : (
                          <motion.div
                            key="idle"
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            exit={{ opacity: 0 }}
                            className="flex items-center"
                          >
                            <Send className="w-5 h-5 mr-3" />
                            {dictionary?.send || "Send Message"}
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </button>
                  </div>

                  <div className="text-center pt-4">
                    <p className="text-sm text-gray-500 dark:text-gray-400 mb-3">
                      {dictionary?.orSchedule ||
                        "Or schedule a meeting directly using the calendar link"}
                    </p>
                    <div className="flex items-center justify-center gap-2 text-xs text-gray-400 dark:text-gray-500">
                      <Shield className="w-3 h-3" />
                      <span>Your data is protected and never shared</span>
                    </div>
                  </div>
                </div>
              </div>
            </form>
          </motion.div>
        </div>
      </div>
    </Section>
  );
};