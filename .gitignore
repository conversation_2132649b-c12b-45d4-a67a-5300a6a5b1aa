# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/
# Keep critical Next.js config files
!next.config.js
!jsconfig.json
!tsconfig.json

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files - uncommented to allow environment configuration for Vercel
# .env*
# Keep example env file
!.env.example

# vercel
.vercel
# Keep Vercel config
!vercel.json

# typescript
*.tsbuildinfo
next-env.d.ts

# YoYo AI version control directory
.yoyo/
