# Email System Setup Guide - Google Workspace

## ✅ Google Workspace Integration

Das Email-System ist jetzt für Google Workspace optimiert und bietet maximale Zuverlässigkeit.

## Setup-Schritte für Google Workspace

### 1. Google App-Passwort erstellen

Da Sie Google Workspace verwenden, benötigen Sie ein App-spezifisches Passwort:

1. **Gehen Sie zu Ihrem Google-Konto:**
   - [Google Account Security](https://myaccount.google.com/security)

2. **Aktivieren Sie 2-Faktor-Authentifizierung** (falls noch nicht aktiviert)

3. **Erstellen Sie ein App-Passwort:**
   - Gehen Sie zu "Sicherheit" → "2-Faktor-Authentifizierung" → "App-Passwörter"
   - Wählen Sie "E-Mail" als App und "Andere" als Gerät
   - Geben Sie "Website Contact Form" als Namen ein
   - **Kopieren Sie das generierte 16-stellige Passwort**

### 2. Environment-Variablen aktualisieren

Aktualisieren Sie Ihre `.env.local` Datei:

```bash
# Google Workspace SMTP Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=ihr-google-app-passwort-hier

# Resend API Key (Fallback)
RESEND_API_KEY=re_Lyrn5C7g_BGcbeVzYqFhmqM2iopTDqJKX
```

**Wichtig:** Ersetzen Sie `ihr-google-app-passwort-hier` mit dem 16-stelligen App-Passwort von Google.

### 3. System-Prioritäten

Das Email-System versucht jetzt in dieser Reihenfolge:

1. **Google Workspace SMTP (Port 587)** - Primär
2. **Google Workspace SMTP (Port 465)** - Alternative
3. **Netcup SMTP** - Backup
4. **Resend API** - Final Fallback

## Vorteile von Google Workspace

- ✅ **99.9% Zuverlässigkeit** - Google's Enterprise-Grade Email-Service
- ✅ **Keine Firewall-Probleme** - SMTP läuft über Standard-Ports
- ✅ **Bessere Deliverability** - Emails landen nicht im Spam
- ✅ **Monitoring** - Logs in Google Admin Console
- ✅ **Sicherheit** - App-Passwörter und 2FA

## Testing

```bash
# Test das Email-System
curl -X POST http://localhost:3000/api/contact \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test User",
    "email": "<EMAIL>",
    "subject": "Google Workspace Test",
    "message": "Testing Google Workspace email integration"
  }'
```

## Troubleshooting

### Problem: "Invalid credentials"
**Lösung:** 
- Verwenden Sie das App-Passwort, nicht Ihr normales Google-Passwort
- Stellen Sie sicher, dass 2FA aktiviert ist
- Prüfen Sie, dass die Email-Adresse korrekt ist

### Problem: "Less secure apps"
**Lösung:** 
- Google Workspace verwendet App-Passwörter, nicht "less secure apps"
- Stellen Sie sicher, dass Sie ein App-Passwort verwenden

### Problem: "Domain not verified"
**Lösung:**
- Ihre Domain sollte bereits in Google Workspace verifiziert sein
- Falls nicht, gehen Sie zur Google Admin Console

## Gmail vs. Google Workspace

**Google Workspace (Ihr Setup):**
- Professionelle Email mit eigener Domain
- Bessere Deliverability
- Enterprise-Support

**Gmail (Consumer):**
- Weniger zuverlässig für Geschäfts-Emails
- Kann als Spam markiert werden

## Environment-Variablen Übersicht

```bash
# Aktuell (Google Workspace)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=ihr-16-stelliges-app-passwort

# Fallback (Resend)
RESEND_API_KEY=re_Lyrn5C7g_BGcbeVzYqFhmqM2iopTDqJKX
```

## Nächste Schritte

1. **Jetzt:** App-Passwort in `.env.local` eintragen
2. **Test:** Contact-Form testen
3. **Monitoring:** Email-Logs in Google Admin Console überwachen
4. **Optional:** SPF/DKIM-Records für bessere Deliverability prüfen

Das System ist jetzt Enterprise-ready mit Google Workspace! 🚀 