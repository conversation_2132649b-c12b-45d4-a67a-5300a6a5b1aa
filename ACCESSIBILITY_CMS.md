# Barrierefreiheit und CMS-Integration

Dieses Dokument beschreibt die Implementierung der Barrierefreiheits-Features und CMS-Integration für das Next.js-Projekt.

## Barrierefreiheit (WCAG 2.1 AA-konform)

Die Barrierefreiheits-Features wurden gemäß den WCAG 2.1 AA-Richtlinien implementiert und umfassen folgende Komponenten:

### AccessibilityProvider

Der `AccessibilityProvider` ist ein React Context Provider, der die Barrierefreiheits-Einstellungen verwaltet und für alle Komponenten verfügbar macht. Er bietet folgende Funktionen:

- Schriftgrößenanpassung (80-150%)
- Hochkontrast-Modus
- Reduzierte Bewegung
- Text-to-Speech (Vorlesen von Inhalten)
- Speicherung der Einstellungen im localStorage

```tsx
// Verwendung des AccessibilityProvider
import { AccessibilityProvider } from '@/providers/AccessibilityProvider'

function App({ children }) {
  return (
    <AccessibilityProvider>
      {children}
    </AccessibilityProvider>
  )
}
```

### AccessibilityMenu

Die `AccessibilityMenu`-Komponente bietet eine Benutzeroberfläche für die Barrierefreiheits-Einstellungen:

- Schriftgröße erhöhen/verringern
- Hochkontrast-Modus umschalten
- Reduzierte Bewegung umschalten
- Vorlesen aktivieren/deaktivieren
- Einstellungen zurücksetzen

```tsx
// Verwendung des AccessibilityMenu
import { AccessibilityMenu } from '@/components/ui/AccessibilityMenu'

function Layout() {
  return (
    <>
      <main>{/* Inhalt */}</main>
      <AccessibilityMenu />
    </>
  )
}
```

### Barrierefreie Komponenten

Es wurden verschiedene barrierefreie Komponenten implementiert, die WCAG 2.1 AA-konform sind:

- `AccessibleHeading`: Semantisch korrekte Überschriften mit visueller Flexibilität
- `AccessibleImage`: Bilder mit Alt-Text, Bildunterschriften und Vorlese-Funktion
- `AccessibleForm`: Formulare mit Labels, Fehlermeldungen und ARIA-Attributen
- `AccessibleNavigation`: Navigationsmenüs mit Tastatursteuerung
- `AccessibleTabs`: Tabs mit Tastatursteuerung und ARIA-Attributen
- `AccessibleDialog`: Modale Dialoge mit Fokus-Falle und Escape-Taste
- `AccessibleTooltip`: Tooltips mit ARIA-Attributen
- `AccessibleAccordion`: Akkordeons mit Tastatursteuerung
- `AccessibleAlert`: Benachrichtigungen mit ARIA-live-Regionen
- `AccessibleBreadcrumbs`: Breadcrumbs mit ARIA-Attributen
- `AccessiblePagination`: Paginierung mit Tastatursteuerung
- `AccessibleTable`: Tabellen mit Sortierung und ARIA-Attributen

### CSS-Regeln für Barrierefreiheit

Es wurden spezielle CSS-Regeln für die Barrierefreiheit implementiert:

- Hochkontrast-Modus mit angepassten Farben
- Reduzierte Bewegung für Animationen
- Verbesserte Fokus-Stile für Tastaturnavigation
- Responsive Layouts für Bildschirmvergrößerung

### Hooks für Barrierefreiheit

Es wurden verschiedene Hooks für die Barrierefreiheit implementiert:

- `useKeyboardNavigation`: Erkennung der Tastaturnavigation
- `useFocusTrap`: Fokus-Falle für Modals und Dialoge
- `useMediaQuery`: Medienabfragen für responsive Designs

### Demo-Seite

Eine Demo-Seite unter `/accessibility-demo` zeigt die Verwendung der barrierefreien Komponenten.

## CMS-Integration

Die CMS-Integration ermöglicht die Verwaltung von Inhalten über verschiedene Headless-CMS-Systeme oder ein lokales JSON-basiertes CMS.

### CMSAdapter

Der `CMSAdapter` ist ein Interface, das die Kommunikation mit verschiedenen CMS-Systemen standardisiert:

- Sanity
- Contentful
- Strapi
- Lokales JSON-CMS

```tsx
// Verwendung des CMSAdapter
import { CMSAdapter } from '@/lib/cms/CMSAdapter'
import { LocalCMSAdapter } from '@/lib/cms/adapters/LocalCMSAdapter'

const adapter: CMSAdapter = new LocalCMSAdapter()
const heroContent = await adapter.getHeroContent('de')
```

### CMSProvider

Der `CMSProvider` ist ein React Context Provider, der den ausgewählten CMS-Adapter verwaltet und für alle Komponenten verfügbar macht:

```tsx
// Verwendung des CMSProvider
import { CMSProvider } from '@/providers/CMSProvider'

function App({ children }) {
  return (
    <CMSProvider defaultCMSType="local" defaultLocale="de">
      {children}
    </CMSProvider>
  )
}
```

### CMSContent

Die `CMSContent`-Komponente lädt Inhalte aus dem CMS und rendert sie mit einer benutzerdefinierten Render-Funktion:

```tsx
// Verwendung der CMSContent-Komponente
import { CMSContent } from '@/components/cms/CMSContent'

function HeroSection() {
  return (
    <CMSContent
      contentType="hero"
      render={(data) => (
        <div>
          <h1>{data.title}</h1>
          <p>{data.subtitle}</p>
          {/* ... */}
        </div>
      )}
      fallback={<div>Lade Hero-Inhalt...</div>}
    />
  )
}
```

### Datenmodelle

Es wurden verschiedene Datenmodelle für die CMS-Integration implementiert:

- `HeroContent`: Inhalte für den Hero-Bereich
- `ServiceContent`: Inhalte für Dienstleistungen
- `PortfolioItem`: Inhalte für Portfolio-Einträge
- `PricingConfig`: Konfiguration für den Preisrechner
- `Testimonial`: Kundenbewertungen
- `FAQ`: Häufig gestellte Fragen
- `SiteSettings`: Allgemeine Seiteneinstellungen

### Admin-Interface

Ein Admin-Interface unter `/admin` ermöglicht die Verwaltung der Inhalte:

- Bearbeitung von Texten
- Hochladen und Verwalten von Bildern
- Anpassung von Preisen und Paketen
- Mehrsprachige Inhalte verwalten

### Demo-Seite

Eine Demo-Seite unter `/cms-demo` zeigt die Verwendung der CMS-Integration.

## Installation und Verwendung

1. Installiere die erforderlichen Abhängigkeiten:

```bash
npm install
```

2. Starte den Entwicklungsserver:

```bash
npm run dev
```

3. Öffne die Demo-Seiten im Browser:

- Barrierefreiheit: http://localhost:3000/accessibility-demo
- CMS-Integration: http://localhost:3000/cms-demo
- Admin-Interface: http://localhost:3000/admin

## Weitere Informationen

- Die Barrierefreiheits-Features sind WCAG 2.1 AA-konform
- Die CMS-Integration unterstützt mehrere Sprachen (de, en, ru, ar, tr)
- Alle Komponenten sind mit TypeScript implementiert
- Die Implementierung folgt den Best Practices für Barrierefreiheit und CMS-Integration
