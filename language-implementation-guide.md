# Implementierungsanleitung: Mehrsprachige Website mit automatischer Spracherkennung

Diese Anleitung erklärt Schritt für Schritt, wie du eine mehrsprachige Website mit Next.js erste<PERSON>t, die automatisch die Sprache des Benutzers basierend auf dessen Standort erkennt.

## Inhaltsverzeichnis

1. [Projektstruktur](#projektstruktur)
2. [Middleware-Setup](#middleware-setup)
3. [Dictionary-System](#dictionary-system)
4. [I18n Provider](#i18n-provider)
5. [Integration in Layouts und Komponenten](#integration-in-layouts-und-komponenten)
6. [Sprachauswahl-Komponente](#sprachauswahl-komponente)
7. [SEO-Optimierung für mehrsprachige Seiten](#seo-optimierung)

## Projektstruktur

Die mehrsprachige Implementierung verwendet folgende Dateien:

```
website_nextjs_react/
  ├── src/
  │    ├── middleware.ts                 # Hauptlogik für Spracherkennung und Routing
  │    ├── types/index.ts                # Typdefinitionen
  │    ├── providers/I18nProvider.tsx    # Context-Provider für Sprachunterstützung
  │    ├── lib/
  │    │    ├── dictionaries.ts          # Ladefunktionen für Sprachdateien
  │    │    └── dictionary.ts            # Typendefinitionen für Wörterbücher
  │    └── dictionaries/                 # Sprachdateien
  │         ├── en.json
  │         ├── de.json
  │         ├── ar.json
  │         ├── ru.json
  │         └── tr.json
  └── app/
       ├── [locale]/                     # Dynamische Routen basierend auf Sprache
       │    ├── layout.tsx               # Layout mit Sprachintegration
       │    └── page.tsx                 # Hauptseite mit übersetzbaren Inhalten
       └── layout.tsx                    # Root-Layout
```

## Middleware-Setup

Die Middleware (`src/middleware.ts`) prüft den Standort des Benutzers und leitet zur entsprechenden Sprachversion weiter.

```typescript
// src/middleware.ts
import { NextRequest, NextResponse } from 'next/server'

// Unterstützte Sprachen
export const locales = ['en', 'de', 'ar', 'ru', 'tr']

// Die Standardsprache, falls keine Übereinstimmung gefunden wird
export const defaultLocale = 'en'

// Mapping von Ländern zu Sprachen
export const countryToLocaleMap: Record<string, string> = {
  // Englischsprachige Länder
  US: 'en', // Vereinigte Staaten
  GB: 'en', // Großbritannien
  AU: 'en', // Australien
  CA: 'en', // Kanada
  NZ: 'en', // Neuseeland
  
  // Deutschsprachige Länder
  DE: 'de', // Deutschland
  AT: 'de', // Österreich
  CH: 'de', // Schweiz
  
  // Arabischsprachige Länder
  SA: 'ar', // Saudi-Arabien
  AE: 'ar', // Vereinigte Arabische Emirate
  EG: 'ar', // Ägypten
  
  // Russischsprachige Länder
  RU: 'ru', // Russland
  BY: 'ru', // Belarus
  KZ: 'ru', // Kasachstan
  
  // Türkischsprachige Länder
  TR: 'tr', // Türkei
  CY: 'tr', // Zypern (teilweise)
}

// Funktion, die prüft, ob der Pfad eine Datei ist
function isFile(pathname: string) {
  return /\.[0-9a-z]+$/i.test(pathname)
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  
  // Überspringe statische Dateien
  if (isFile(pathname)) {
    return NextResponse.next()
  }

  // Überspringe API-Routen und bestimmte Pfade
  if (
    pathname.startsWith('/api') ||
    pathname.startsWith('/_next') ||
    pathname.startsWith('/admin') ||
    pathname === '/sitemap.xml' ||
    pathname === '/robots.txt'
  ) {
    return NextResponse.next()
  }

  // Extrahiere den aktuellen Locale aus der URL
  const pathnameLocale = locales.find(
    locale => pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`
  )

  // Wenn die URL bereits eine Sprache hat, nichts tun
  if (pathnameLocale) {
    return NextResponse.next()
  }

  // Versuche, die bevorzugte Sprache des Benutzers zu ermitteln
  let locale

  // 1. Überprüfen von Cookies (wenn der Benutzer bereits eine Sprache ausgewählt hat)
  const cookieLocale = request.cookies.get('NEXT_LOCALE')?.value
  if (cookieLocale && locales.includes(cookieLocale)) {
    locale = cookieLocale
  } 
  // 2. Überprüfen der Geolocation basierend auf IP-Adresse
  else {
    // Benutze Vercel's geo headers oder andere Geo-Dienste
    const country = request.geo?.country || ''
    if (country && countryToLocaleMap[country]) {
      locale = countryToLocaleMap[country]
    } 
    // 3. Überprüfen der Accept-Language Header
    else {
      const acceptLanguage = request.headers.get('accept-language')
      if (acceptLanguage) {
        // Extrahiere und analysiere Accept-Language Header
        const preferredLocale = acceptLanguage
          .split(',')
          .map(lang => lang.split(';')[0].trim().substring(0, 2))
          .find(lang => locales.includes(lang))
        
        if (preferredLocale) {
          locale = preferredLocale
        }
      }
    }
  }

  // Fallback zur Standardsprache, wenn keine andere Methode funktioniert hat
  if (!locale) {
    locale = defaultLocale
  }

  // Leite zur lokalisierten URL weiter
  const newUrl = new URL(`/${locale}${pathname === '/' ? '' : pathname}`, request.url)
  return NextResponse.redirect(newUrl)
}

export const config = {
  matcher: ['/((?!api|_next/static|_next/image|_vercel|favicon.ico).*)'],
}
```

## Dictionary-System

Das Dictionary-System besteht aus mehreren Teilen:

### Typ-Definitionen (`src/lib/dictionary.ts`):

```typescript
// src/lib/dictionary.ts
export type Dictionary = {
  locale: string;
  navigation: {
    home: string;
    about: string;
    services: string;
    portfolio: string;
    pricing: string;
    contact: string;
    // ... weitere Navigationspunkte
  };
  home: {
    hero: {
      title: string;
      subtitle: string;
      description: string;
      cta: string;
    };
    // ... weitere Abschnitte
  };
  // ... weitere Sektionen und Übersetzungen
}
```

### Lade-Funktionen (`src/lib/dictionaries.ts`):

```typescript
// src/lib/dictionaries.ts
import type { Dictionary } from './dictionary'

const dictionaries = {
  en: () => import('@/dictionaries/en.json').then(module => module.default) as Promise<Dictionary>,
  de: () => import('@/dictionaries/de.json').then(module => module.default) as Promise<Dictionary>,
  ar: () => import('@/dictionaries/ar.json').then(module => module.default) as Promise<Dictionary>,
  ru: () => import('@/dictionaries/ru.json').then(module => module.default) as Promise<Dictionary>,
  tr: () => import('@/dictionaries/tr.json').then(module => module.default) as Promise<Dictionary>,
}

export const getDictionary = async (locale: string): Promise<Dictionary> => {
  // Sichere Fallback-Option, falls die Sprache nicht unterstützt wird
  if (!Object.keys(dictionaries).includes(locale)) {
    return dictionaries.en()
  }
  
  return dictionaries[locale as keyof typeof dictionaries]()
}
```

### Beispiel einer Sprachdatei (`src/dictionaries/de.json`):

```json
{
  "locale": "de",
  "navigation": {
    "home": "Startseite",
    "about": "Über uns",
    "services": "Dienstleistungen",
    "portfolio": "Portfolio",
    "pricing": "Preise",
    "contact": "Kontakt"
  },
  "home": {
    "hero": {
      "title": "Innovative digitale Lösungen",
      "subtitle": "Wir gestalten die digitale Zukunft",
      "description": "Wir entwickeln hochmoderne mobile und Web-Anwendungen, die Unternehmen durch innovative Technologielösungen, optimiertes UX-Design und KI-Integration transformieren.",
      "cta": "Kontaktieren Sie uns"
    }
  }
  // Weitere Übersetzungen...
}
```

## I18n Provider

Der I18n Provider stellt die Übersetzungen allen Komponenten zur Verfügung.

```tsx
// src/providers/I18nProvider.tsx
'use client'

import React, { createContext, useContext, ReactNode, useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import type { Dictionary } from '@/lib/dictionary'

// Initialer leerer Kontext
const initialDictionary: Partial<Dictionary> = {}

// Context für Internationalisierung
const I18nContext = createContext<{
  dictionary: Partial<Dictionary>
  locale: string
  setDictionary: (dict: Dictionary) => void
}>({
  dictionary: initialDictionary,
  locale: 'en',
  setDictionary: () => {},
})

// Hook für einfachen Zugriff auf den I18n-Kontext
export const useI18n = () => useContext(I18nContext)

// Provider-Komponente
export const I18nProvider = ({ children }: { children: ReactNode }) => {
  const params = useParams()
  const locale = params?.locale as string || 'en'
  const [dictionary, setDictionary] = useState<Partial<Dictionary>>(initialDictionary)

  // Lädt die Übersetzungsdaten, wenn die Komponente montiert wird
  useEffect(() => {
    const loadDictionary = async () => {
      try {
        const dict = await import(`@/dictionaries/${locale}.json`)
        setDictionary(dict)
      } catch (error) {
        console.error(`Failed to load dictionary for locale: ${locale}`, error)
        // Fallback zur englischen Sprache bei Fehlern
        const enDict = await import('@/dictionaries/en.json')
        setDictionary(enDict)
      }
    }

    loadDictionary()
  }, [locale])

  return (
    <I18nContext.Provider value={{ dictionary, locale, setDictionary }}>
      {children}
    </I18nContext.Provider>
  )
}
```

## Integration in Layouts und Komponenten

### Root Layout (`app/layout.tsx`):

Das Root-Layout dient hauptsächlich zur Weiterleitung an das Locale-Layout.

```tsx
// app/layout.tsx
import type { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Innovatio - Web & Mobile Digital Solutions Company',
  description: 'We develop cutting-edge mobile and web applications that transform businesses through innovative technology solutions, optimized UX design, and AI integration.',
  // ... weitere Metadaten
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body>{children}</body>
    </html>
  )
}
```

### Locale Layout (`app/[locale]/layout.tsx`):

Das Locale-Layout initialisiert den I18n Provider und lädt die Übersetzungen.

```tsx
// app/[locale]/layout.tsx
import type { Metadata } from 'next'
import { locales } from '@/middleware'
import { I18nProvider } from '@/providers/I18nProvider'
import { getDictionary } from '@/lib/dictionaries'
import { Header } from '@/components/layout/Header'

// Dynamische Metadaten für sprachspezifisches SEO
export async function generateMetadata({
  params: { locale },
}: {
  params: { locale: string };
}) {
  // Lade die Übersetzungen für den aktuellen Locale
  const dictionary = await getDictionary(locale)

  return {
    title: {
      default: dictionary.metadata?.title || "Innovatio - Digital Solutions Company",
      template: "%s | Innovatio",
    },
    description: dictionary.metadata?.description || "We develop cutting-edge mobile and web applications that transform businesses",
    // ... weitere sprachspezifische Metadaten
  }
}

// Dynamische Route-Generierung für alle unterstützten Sprachen
export async function generateStaticParams() {
  return locales.map((locale) => ({ locale }))
}

export default function LocaleLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: { locale: string };
}) {
  return (
    <I18nProvider>
      <Header />
      {children}
    </I18nProvider>
  )
}
```

### Verwendung in Komponenten mit Server Components:

```tsx
// app/[locale]/page.tsx
import { getDictionary } from '@/lib/dictionaries'
import { HeroSection } from '@/components/sections/HeroSection'
import { FeaturesSection } from '@/components/sections/FeaturesSection'
// ... weitere Komponenten

export default async function Home({
  params: { locale }
}: {
  params: { locale: string }
}) {
  // Lade die Übersetzungen für die aktuelle Sprache
  const dictionary = await getDictionary(locale)

  return (
    <main>
      <HeroSection dictionary={dictionary.home?.hero} />
      <FeaturesSection dictionary={dictionary.home?.features} />
      {/* ... weitere Komponenten */}
    </main>
  )
}
```

### Verwendung in Client Components:

```tsx
// src/components/sections/HeroSection.tsx
'use client'

import { Button } from '@/components/ui/Button'
import Image from 'next/image'
import { useI18n } from '@/providers/I18nProvider'

export const HeroSection = () => {
  // Zugriff auf die Übersetzungen über den Context
  const { dictionary, locale } = useI18n()
  const hero = dictionary.home?.hero || {}

  return (
    <section className="py-20">
      <div className="container">
        <h1 className="text-5xl font-bold">{hero.title || 'Innovative Digital Solutions'}</h1>
        <p className="mt-4 text-xl">{hero.description || 'We transform businesses through technology'}</p>
        <Button className="mt-8">{hero.cta || 'Contact Us'}</Button>
      </div>
    </section>
  )
}
```

## Sprachauswahl-Komponente

Erstellung einer Sprachauswahl-Komponente für die Benutzer:

```tsx
// src/components/layout/LanguageSwitcher.tsx
'use client'

import { useState } from 'react'
import { useParams, useRouter } from 'next/navigation'
import { locales } from '@/middleware'
import { Button } from '@/components/ui/Button'

export const LanguageSwitcher = () => {
  const params = useParams()
  const router = useRouter()
  const currentLocale = params?.locale as string
  const [isOpen, setIsOpen] = useState(false)

  // Sprachennamen für die Anzeige
  const localeNames: Record<string, string> = {
    en: 'English',
    de: 'Deutsch',
    ar: 'العربية',
    ru: 'Русский',
    tr: 'Türkçe',
  }

  // Sprachenwechsel-Funktion
  const switchLanguage = (locale: string) => {
    // Extrahiere den aktuellen Pfad ohne die Locale-Information
    const currentPath = window.location.pathname
    const pathWithoutLocale = currentPath.replace(new RegExp(`^/${currentLocale}`), '')
    
    // Navigiere zur neuen URL mit der gewählten Sprache
    const newPath = `/${locale}${pathWithoutLocale}`
    
    // Speichere die Sprachauswahl in einem Cookie für zukünftige Besuche
    document.cookie = `NEXT_LOCALE=${locale}; path=/; max-age=${60 * 60 * 24 * 365}`
    
    // Navigiere zur übersetzten Seite
    router.push(newPath)
    setIsOpen(false)
  }

  return (
    <div className="relative">
      <Button 
        onClick={() => setIsOpen(!isOpen)}
        variant="ghost"
        className="flex items-center gap-2"
      >
        {localeNames[currentLocale] || 'Language'}
        <span className="w-5 h-5">🌐</span>
      </Button>
      
      {isOpen && (
        <div className="absolute z-10 right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg">
          <ul className="py-1">
            {locales.map(locale => (
              <li key={locale}>
                <button
                  onClick={() => switchLanguage(locale)}
                  className={`w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 ${
                    currentLocale === locale ? 'font-bold' : ''
                  }`}
                >
                  {localeNames[locale]}
                </button>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  )
}
```

## SEO-Optimierung für mehrsprachige Seiten

### Sitemap für alle Sprachen:

```tsx
// app/sitemap.ts
import { MetadataRoute } from 'next'
import { locales } from '@/middleware'

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = 'https://example.com'
  
  // Liste aller Seiten
  const routes = [
    '',                // Homepage
    '/about',          
    '/services',       
    '/portfolio',      
    '/pricing',        
    '/contact',        
    // ... weitere Routen
  ]
  
  // Erzeuge lokalisierte URLs für jede Seite
  const localizedRoutes = locales.flatMap(locale => {
    return routes.map(route => ({
      url: `${baseUrl}/${locale}${route}`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: route === '' ? 1 : 0.8,
    }))
  })
  
  return localizedRoutes
}
```

### Robots.txt mit Sitemap-Verweis:

```tsx
// app/robots.ts
import { MetadataRoute } from 'next'

export default function robots(): MetadataRoute.Robots {
  return {
    rules: {
      userAgent: '*',
      allow: '/',
      disallow: ['/admin/'],
    },
    sitemap: 'https://example.com/sitemap.xml',
    host: 'https://example.com',
  }
}
```

### Hreflang-Links für verbesserte SEO:

Füge diese Funktion zu deinem `app/[locale]/layout.tsx` hinzu:

```tsx
// app/[locale]/layout.tsx
// ... existierender Code

// Hreflang-Links für verbesserte SEO
function generateHrefLangLinks(currentPath: string) {
  const baseUrl = 'https://example.com'
  return locales.map(locale => ({
    rel: 'alternate',
    hrefLang: locale,
    href: `${baseUrl}/${locale}${currentPath === `/${locale}` ? '' : currentPath.replace(new RegExp(`^/${locale}`), '')}`
  }))
}

export default function LocaleLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: { locale: string };
}) {
  // Extrahiere den aktuellen Pfad
  const pathname = usePathname()
  
  // Generiere hreflang Links
  const hrefLangLinks = generateHrefLangLinks(pathname)
  
  return (
    <>
      <head>
        {/* Hreflang-Links für Suchmaschinen */}
        {hrefLangLinks.map((link, i) => (
          <link key={i} rel={link.rel} hrefLang={link.hrefLang} href={link.href} />
        ))}
        <link rel="canonical" href={`https://example.com${pathname}`} />
      </head>
      <I18nProvider>
        <Header />
        {children}
      </I18nProvider>
    </>
  )
}
```

## Zusammenfassung

Diese Implementierung ermöglicht:

1. **Automatische Spracherkennung** basierend auf:
   - Benutzerstandort (über IP-Geolocation)
   - Browser-Präferenzen
   - Vorherige Benutzerauswahl (über Cookies)

2. **SEO-Optimierung** für mehrsprachige Websites durch:
   - Lokalisierte Metadaten
   - Sitemap für alle Sprachversionen
   - hreflang-Links für Suchmaschinenoptimierung

3. **Benutzerfreundliche Sprachauswahl** mit:
   - Visuellem Sprachumschalter
   - Cookie-basiertem Speichern der Sprachpräferenz

Diese Lösung kann leicht auf andere Next.js-Websites übertragen werden, indem die entsprechenden Dateien und Strukturen kopiert und an die spezifischen Anforderungen angepasst werden. 